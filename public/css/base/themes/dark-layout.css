.dark-layout {
}

:root {
    --main-color-dark: #b4b7bd;
    --main-bg-color-dark: #0C0C18;
    --main-bg-color-dark-card: #27272b;
    --main-bg-color-dark-th: #1C1C24;
    --main-text-color-dark: #F7F7F8;
    --main-blue-accent-dark: #97C4FF;
    --main-border-color: #3D3D42;
}

body.dark-layout,
.dark-layout body {
    color: var(--main-color-dark);
    background-color: var(--main-bg-color-dark);
}

.dark-layout .background-color-mode {
    color: white;
    background-color: var(--main-bg-color-dark-card);
    border-radius: 5px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.dark-layout .background-color-mode-building-tables {
    color: white;
    background-color: var(--main-bg-color-dark-card);
    border-radius: 0px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.dark-layout th,
.dark-layout .accordion-header {
    background-color: var(--main-bg-color-dark-th);
    color: var(--main-text-color-dark);
}

.dark-layout .light-dark-card {
    color: white;
    background-color: var(--main-bg-color-dark-th);
    border-radius: 5px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.10);
}

.dark-layout .show-emergency-contact {
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout .emergency-contact-icons {
    color: #EA5455 !important;
}

.dark-layout .emergency-contact-name {
    color: white !important;
}

.dark-layout .hello_dashboard,
.dark-layout p,
.dark-layout b,
.dark-layout div,
.dark-layout .titleTables,
.dark-layout .icon-in-table-no-action {
    color: var(--main-text-color-dark);
}

.dark-layout .form-control[type=file]::-webkit-file-upload-button {
    background-color: #27272B;
}

.dark-layout .form-control[type=file]::file-selector-button {
    background-color: #27272B;
}

.dark-layout .c-blue {
    color: var(--main-text-color-dark);
    fill: var(--main-text-color-dark);
}

.dark-layout .bg-white,
.dark-layout .white-field {
    background-color: transparent;
}

.dark-layout .filter-button-selected,
.dark-layout .dark-skyblue-link b {
    color: var(--main-blue-accent-dark) !important;
}

.dark-layout .border-xs {
    border: 1px solid var(--main-bg-color-dark-card);
    border-color: var(--main-bg-color-dark-card) !important;
}

.dark-layout .card-button.active {
    background-color: var(--main-bg-color-dark-th);
}

.dark-layout .table-button-border.active {
    background-color: var(--main-bg-color-dark-th);
}

.dark-layout .table-border {
    border-width: 0px 1px 1px 1px;
    border-style: solid;
    border-color: #3D3D42;
    border-radius: 0px 0px 8px 8px;
}

.dark-layout .dark-white-text {
    color: var(--main-text-color-dark) !important;
}

.dark-layout .c-blue bold {
    color: var(--main-blue-accent-dark) !important;
}

.dark-layout .table-button-border {
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #3D3D42;
    border-radius: 8px 8px 0px 0px;
}

.dark-layout h1,
.dark-layout h2,
.dark-layout h3,
.dark-layout h4,
.dark-layout h5,
.dark-layout h6 .dark-layout label {
    color: white !important;
}

.dark-layout .border,
.dark-layout .border-top,
.dark-layout .border-end,
.dark-layout .border-bottom,
.dark-layout .border-start {
    border-color: #3b4253 !important;
}

.dark-layout a:hover {
    color: #7367f0;
}

.dark-layout label {
    color: #d0d2d6;
}

.dark-layout .btn span {
    color: inherit;
}

.dark-layout hr {
    color: #3b4253;
}

.dark-layout pre {
    background-color: #161d31;
    border: 0;
}

.dark-layout pre code {
    background-color: inherit;
    text-shadow: none;
}

.dark-layout pre code .url {
    background-color: #161d31;
}

.dark-layout code {
    background-color: #161d31;
}

.dark-layout kbd {
    background-color: #161d31;
}

.dark-layout .text-dark {
    color: #b8c2cc !important;
}

.dark-layout .text-muted {
    color: #676d7d !important;
}

.dark-layout .text-body {
    color: #b4b7bd !important;
}

.dark-layout .text-body-heading {
    color: #d0d2d6;
}

.dark-layout .section-label {
    color: #676d7d;
}

.dark-layout .shadow {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24) !important;
}

.dark-layout .blockquote-footer {
    color: #676d7d;
}

.dark-layout .header-navbar-shadow {
    background: linear-gradient(180deg, rgba(22, 29, 49, 0.9) 44%, rgba(22, 29, 49, 0.43) 73%, rgba(22, 29, 49, 0));
}

.dark-layout .horizontal-layout .header-navbar {
    background-color: #283046;
}

.dark-layout .horizontal-layout.navbar-sticky .header-navbar,
.dark-layout .horizontal-layout.navbar-static .header-navbar {
    background-color: #161d31;
}

.dark-layout .navbar-sticky .header-navbar.navbar-shadow {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.75);
}

.dark-layout .header-navbar {
    background-color: #27272B;
}

.dark-layout .icon-edit {
    color: #97C4FF;
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout .date_notfication {
    color: white !important;
}

.dark-layout .repeatable-element-dark {
    background-color: #27272B !important;
    margin: 0px !important;
    margin-top: -2% !important;
    margin-bottom: 4% !important;
}

.dark-layout .btn-linkedin {
    background-color: #97C4FF !important;
    color: black !important;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .nav-link {
    color: #d0d2d6;
    background-color: transparent;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .nav-link i,
.dark-layout .header-navbar .navbar-container .nav .nav-item .nav-link svg {
    color: #d0d2d6;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .nav-link.bookmark-star i,
.dark-layout .header-navbar .navbar-container .nav .nav-item .nav-link.bookmark-star svg {
    color: #fff !important;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open {
    background-color: #283046;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .input {
    border-color: #3b4253;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .input::-moz-placeholder {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .input:-ms-input-placeholder {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .input,
.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .input::placeholder,
.dark-layout .header-navbar .navbar-container .nav .nav-item.nav-search .search-input.open .search-input-close {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .bookmark-input {
    background-color: #283046;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .bookmark-input .form-control {
    background-color: #283046;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .search-list {
    background-color: #283046;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .search-list li a {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .nav-item .search-list .auto-suggestion.current_item {
    background-color: #161d31;
}

.dark-layout .header-navbar .navbar-container .nav li i.ficon,
.dark-layout .header-navbar .navbar-container .nav li svg.ficon {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media {
    overflow: hidden;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .list-item,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .list-item {
    border-color: #3b4253;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .list-item .media-meta,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .list-item .media-meta {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .list-item:hover,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .list-item:hover {
    background-color: #161d31;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .dropdown-menu-header,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .dropdown-menu-header {
    border-bottom: 1px solid #3b4253;
    background-color: #0C0C18;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .dropdown-menu-footer,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .dropdown-menu-footer {
    background-color: #0C0C18;
    border-top: 1px solid #3b4253;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .dropdown-menu-footer .dropdown-item,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .dropdown-menu-footer .dropdown-item {
    border-color: #3b4253;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-menu.dropdown-menu-media .dropdown-menu-footer .dropdown-item:hover,
.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .dropdown-menu.dropdown-menu-media .dropdown-menu-footer .dropdown-item:hover {
    background-color: #283046;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart.empty-cart:before {
    background-color: #161d31;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart li {
    color: #fff;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .cart-item {
    color: #b4b7bd;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .dropdown-header span {
    color: #fff;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .list-item img {
    background-color: #161d31;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-cart .media-heading .cart-item-by {
    color: #676d7d;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .list-item-body .media-heading {
    color: #d0d2d6;
}

.dark-layout .header-navbar .navbar-container .nav .dropdown-notification .notification-text {
    color: #676d7d;
}

.dark-layout .header-navbar.navbar-static-top {
    background-color: transparent !important;
}

.dark-layout .header-navbar[class*=bg-] .navbar-nav .nav-item .nav-link {
    background-color: inherit;
}

.dark-layout .blank-page .card.bg-transparent h1,
.dark-layout .blank-page .card.bg-transparent h2,
.dark-layout .blank-page .card.bg-transparent h3,
.dark-layout .blank-page .card.bg-transparent h4,
.dark-layout .blank-page .card.bg-transparent h5,
.dark-layout .blank-page .card.bg-transparent h6 {
    color: #283046;
}

.dark-layout .blank-page .card.bg-transparent p {
    color: #161d31;
}

.dark-layout .main-menu {
    background-color: #27272B !important;
}

.dark-layout .main-menu .shadow-bottom {
    background: linear-gradient(180deg, #283046 44%, rgba(40, 48, 70, 0.51) 73%, rgba(40, 48, 70, 0));
}

.dark-layout .main-menu:not(.expanded) .navigation .sidebar-group-active > a {
    background-color: #161d31;
}

.dark-layout .main-menu.menu-dark .navigation > li.open:not(.menu-item-closing) > a {
    background: #1D2F72 !important;
    font-weight: 400;
    border-radius: 4px 4px 0 0;
}

.dark-layout .main-menu.menu-dark .navigation > li > ul li:not(.has-sub) {
    background: #1D2F72 !important;
    font-weight: 400;
}

.dark-layout .main-menu.menu-dark .navigation > li > ul li:not(.has-sub):last-child {
    border-radius: 0 0 4px 4px;
}

.dark-layout .main-menu.menu-dark .navigation > li.sidebar-group-active > a {
    background-color: rgba(22, 29, 49, 0);
}

.dark-layout .main-menu .collapse-toggle-icon {
    /*color: #7367f0 !important;*/
    color: #d0d2d1 !important;
}

.dark-layout .ps__thumb-x,
.dark-layout .ps__thumb-y {
    background-color: #b4b7bd;
}

.dark-layout .ps .ps__rail-x.ps--clicking,
.dark-layout .ps .ps__rail-x:focus,
.dark-layout .ps .ps__rail-x:hover,
.dark-layout .ps .ps__rail-y.ps--clicking,
.dark-layout .ps .ps__rail-y:focus,
.dark-layout .ps .ps__rail-y:hover {
    background-color: #3b4253;
}

.dark-layout .ps__rail-y.ps--clicking .ps__thumb-y,
.dark-layout .ps__rail-y:focus > .ps__thumb-y,
.dark-layout .ps__rail-y:hover > .ps__thumb-y {
    background-color: #b4b7bd;
}

.dark-layout .main-menu-content .navigation-main {
    /*background-color: #283046;*/
}

.dark-layout .main-menu-content .navigation-main .navigation-header {
    color: #676d7d;
}

.dark-layout .main-menu-content .navigation-main li a {
    color: #d0d2d6 !important;
    font-weight: bold;
}

.dark-layout .main-menu-content .navigation-main li ul .open > a,
.dark-layout .main-menu-content .navigation-main li ul .sidebar-group-active > a {
    background-color: #161d31;
}

.dark-layout .main-menu-content .navigation-main .nav-item i,
.dark-layout .main-menu-content .navigation-main .nav-item svg {
    color: #d0d2d6;
}

.dark-layout .main-menu-content .navigation-main .nav-item.open > a {
    background-color: rgba(29, 47, 114, 0) !important
}

.dark-layout .main-menu-content .navigation-main .nav-item a:after {
    color: #d0d2d6;
}

.dark-layout .main-menu-content .navigation-main .nav-item .menu-content {
    /*background-color: #283046;*/
}

.dark-layout .main-menu-content .navigation-main .nav-item .menu-content .active .menu-item {
    color: #fff;
}

.dark-layout .main-menu-content .navigation-main .active .menu-title,
.dark-layout .main-menu-content .navigation-main .active i,
.dark-layout .main-menu-content .navigation-main .active svg {
    color: #fff;
}

.dark-layout .main-menu-content .navigation-main .sidebar-group-active .menu-content {
    background-color: #1D2F72;
    margin-left: 15px !important;
    width: 230px;
    margin-top: -9px;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.dark-layout .main-menu-content .navigation-main .sidebar-group-active .menu-content .active {
    z-index: 1;
}

.dark-layout .main-menu-content .navigation-main .sidebar-group-active .menu-content .active a {
    background-color: transparent;
}

.dark-layout .menu-collapsed .main-menu:not(.expanded) .navigation-main li.active > a {
    background: #1D2F72 !important;
}

.dark-layout .accordion-item:not(:last-of-type) {
    border-color: #3b4253;
}

.dark-layout .accordion-item {
    background: #27272B;
    /*border: solid 1px #ADE1FB !important;*/
}

.dark-layout .border-color {
    border: solid 1px #ADE1FB !important;
}

.dark-layout .accordion-button {
    background: #1C1C24;
    color: white;
}

.dark-layout .accordion-button:after {
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-up'><polyline points='18 15 12 9 6 15'></polyline></svg>");
}

.dark-layout #show_towers_info {
    color: white !important;
}

.dark-layout a:not([href]):not([tabindex]) {
    color: var(--main-text-color-dark);
    text-decoration: none;
}

.dark-layout a:not([href]):not([tabindex]):focus,
.dark-layout a:not([href]):not([tabindex]):hover,
.dark-layout a:not([href]):not([tabindex]):hover {
    color: var(--main-blue-accent-dark);
    text-decoration: none;
}

.dark-layout .text_notification {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
}

.dark-layout p.style {
    color: white !important;
}

.dark-layout .accordion-border .accordion-item {
    border-color: #3b4253;
}

.dark-layout .accordion-margin .accordion-item {
    box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.24) !important;
}

.dark-layout .alert .alert-heading,
.dark-layout .alert p {
    color: inherit;
}

.dark-layout .alert.alert-dark .alert-heading,
.dark-layout .alert.alert-dark .alert-body,
.dark-layout .alert.alert-dark p {
    color: #b8c2cc;
}

.dark-layout .content-header-left .breadcrumbs-top .content-header-title {
    color: #d0d2d6;
    border-color: #3b4253;
}

.dark-layout .breadcrumb .breadcrumb-item {
    color: #b4b7bd;
}

.dark-layout .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    color: #b4b7bd;
}

.dark-layout .breadcrumb:not([class*=breadcrumb-]) .breadcrumb-item + .breadcrumb-item:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .breadcrumb-chevron.breadcrumb .breadcrumb-item + .breadcrumb-item:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .btn:not([class*=btn-]) {
    color: white;
}

.dark-layout .btn.btn-dark,
.dark-layout .btn.btn-outline-dark,
.dark-layout .btn.btn-flat-dark {
    color: #b8c2cc;
}

.dark-layout .btn.btn-dark {
    background-color: #4b4b4b !important;
}

.dark-layout .btn.btn-outline-dark {
    border-color: #4b4b4b;
    color: #b8c2cc;
}

.dark-layout .btn.btn-outline-dark:hover:not(.disabled):not(:disabled) {
    color: #b8c2cc;
}

.dark-layout .btn.btn-flat-dark:active,
.dark-layout .btn.btn-flat-dark:focus {
    background: #4b4b4b;
}

.dark-layout .btn-group label[class*=btn-outline-] {
    color: #7367f0 !important;
}

.dark-layout .dropdown-menu {
    background-color: #283046;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.24);
}

.dark-layout .dropdown-menu .dropdown-header {
    color: #d0d2d6 !important;
}

.dark-layout .dropdown-menu .dropdown-item {
    color: #b4b7bd;
}

.dark-layout .dropdown-menu .dropdown-item:hover,
.dark-layout .dropdown-menu .dropdown-item:focus {
    background: rgba(115, 103, 240, 0.12);
    color: #7367f0;
}

.dark-layout .dropdown-menu .dropdown-item:active,
.dark-layout .dropdown-menu .dropdown-item.active {
    color: #fff;
    background-color: #7367f0;
}

.dark-layout .dropdown-menu .dropdown-item.disabled,
.dark-layout .dropdown-menu .dropdown-item:disabled {
    color: #676d7d;
}

.dark-layout .dropdown-divider {
    border-color: #3b4253;
}

.dark-layout .modal .modal-header,
.dark-layout .modal .modal-header[class*=bg-] {
    background-color: #0C0C18;
}

.dark-layout .modal .modal-header .btn-close {
    text-shadow: none;
    background-color: #283046 !important;
    color: #b4b7bd;
    box-shadow: 0 3px 8px 0 rgba(11, 10, 25, 0.49) !important;
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23b4b7bd'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>");
}

.dark-layout .modal .modal-content,
.dark-layout.modal-body,
.dark-layout .modal .modal-footer {
    background-color: var(--main-bg-color-dark-card);
    border-color: var(--main-bg-color-dark-th);
}

.dark-layout .btn-close {
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23b4b7bd'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/0.75rem auto no-repeat;
}

.dark-layout .offcanvas {
    background-color: #283046;
    color: #b4b7bd;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item.active {
    background-color: #242b3d;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item.active .page-link {
    background-color: #7367f0;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item.active .page-link:hover {
    color: #fff;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item .page-link {
    background-color: #242b3d;
    color: #b4b7bd;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item .page-link:hover {
    color: #7367f0;
}

.dark-layout .pagination:not([class*=pagination-]) .page-item.prev-item .page-link:hover,
.dark-layout .pagination:not([class*=pagination-]) .page-item.next-item .page-link:hover {
    color: #fff;
}

.dark-layout .pagination[class*=pagination-] .page-item:not(.active) .page-link {
    background-color: #242b3d;
}

.dark-layout .pagination[class*=pagination-] .page-item:not(.active):not(:hover) .page-link {
    color: #fff;
}

.dark-layout .pagination[class*=pagination-] .page-item:not(.active) .page-link:hover {
    background-color: #242b3d;
}

.dark-layout .pagination[class*=pagination-] .page-item.active {
    background-color: #242b3d;
}

.dark-layout .pagination .page-item.prev-item .page-link:before,
.dark-layout .pagination .page-item.previous .page-link:before,
.dark-layout .pagination .page-item.prev .page-link:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .pagination .page-item.prev-item.disabled .page-link:before,
.dark-layout .pagination .page-item.previous.disabled .page-link:before,
.dark-layout .pagination .page-item.prev.disabled .page-link:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23676d7d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-left'%3E%3Cpolyline points='15 18 9 12 15 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .pagination .page-item.next-item .page-link::after,
.dark-layout .pagination .page-item.next .page-link::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .pagination .page-item.next-item.disabled .page-link:after,
.dark-layout .pagination .page-item.next.disabled .page-link:after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23676d7d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .pagination .page-item.disabled .page-link {
    color: #676d7d;
}

.dark-layout .nav-tabs .nav-item .nav-link,
.dark-layout .nav-pills .nav-item .nav-link,
.dark-layout .nav-tabs.nav-justified .nav-item .nav-link {
    color: #d0d2d6;
}

.dark-layout .nav-tabs .nav-item .nav-link.active,
.dark-layout .nav-pills .nav-item .nav-link.active,
.dark-layout .nav-tabs.nav-justified .nav-item .nav-link.active {
    background-color: #283046;
    color: #7367f0;
}

.dark-layout .nav-tabs .nav-item .nav-link.disabled,
.dark-layout .nav-pills .nav-item .nav-link.disabled,
.dark-layout .nav-tabs.nav-justified .nav-item .nav-link.disabled {
    color: #676d7d;
}

.dark-layout .nav.wrap-border {
    border-color: #3b4253;
}

.dark-layout .nav-pills .nav-item .nav-link.active {
    color: #fff;
    background-color: #7367f0;
}

.dark-layout .popover.bs-popover-top .popover-arrow:before {
    border-top-color: #3b4253;
}

.dark-layout .popover.bs-popover-top .popover-arrow:after {
    border-top-color: #283046;
}

.dark-layout .popover.bs-popover-start .popover-arrow:before {
    border-left-color: #3b4253;
}

.dark-layout .popover.bs-popover-start .popover-arrow:after {
    border-left-color: #283046;
}

.dark-layout .popover.bs-popover-end .popover-arrow:before {
    border-right-color: #3b4253;
}

.dark-layout .popover.bs-popover-end .popover-arrow:after {
    border-right-color: #283046;
}

.dark-layout .popover .popover-header {
    color: #fff;
}

.dark-layout .popover .popover-body {
    background-color: #283046;
    color: #b4b7bd;
    border-color: #3b4253 !important;
}

.dark-layout .toast {
    background-color: rgba(40, 48, 70, 0.85);
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);
    color: #b4b7bd;
}

.dark-layout .toast .toast-header {
    background-color: #283046;
    color: #b4b7bd;
}

.dark-layout .toast .toast-header .btn-close {
    background-color: transparent !important;
    background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23b4b7bd'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>");
    box-shadow: none !important;
    text-shadow: none;
}

.dark-layout .list-group .list-group-item:not([class*=list-group-item-]),
.dark-layout .list-group .list-group-item.list-group-item-action:not(.active):not(:active) {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .list-group .list-group-item:not([class*=list-group-item-]):not(.disabled),
.dark-layout .list-group .list-group-item.list-group-item-action:not(.active):not(:active):not(.disabled) {
    color: #b4b7bd;
}

.dark-layout .list-group .list-group-item.list-group-item-action:not(.active):not(:active):hover,
.dark-layout .list-group .list-group-item.list-group-item-action:not(.active):not(:active):focus {
    background-color: #161d31;
}

.dark-layout .list-group .list-group-item.list-group-item-action.active:hover,
.dark-layout .list-group .list-group-item.list-group-item-action.active:focus,
.dark-layout .list-group .list-group-item.list-group-item-action:active:hover,
.dark-layout .list-group .list-group-item.list-group-item-action:active:focus {
    color: #fff;
}

.dark-layout .list-group .list-group-item.active p,
.dark-layout .list-group .list-group-item.active small {
    color: #fff;
}

.dark-layout .list-group .list-group-item.active:hover {
    color: #fff;
}

.dark-layout .list-group .list-group-item.disabled {
    color: #676d7d;
}

.dark-layout .list-group.list-group-circle .list-group-item:after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-circle'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3C/svg%3E");
}

.dark-layout .spinner-border {
    border-color: currentColor !important;
    border-right-color: transparent !important;
}

.dark-layout .avatar {
    background-color: #161d31;
}

.dark-layout .avatar [class*=avatar-status-] {
    border-color: #283046;
}

.dark-layout .avatar-group .avatar img,
.dark-layout .avatar-group .avatar .avatar-content {
    box-shadow: 0 0 0 2px #283046, inset 0 0 0 1px rgba(40, 48, 70, 0.07);
}

.dark-layout .divider .divider-text {
    color: #b4b7bd;
}

.dark-layout .divider .divider-text::before,
.dark-layout .divider .divider-text::after {
    border-color: #3b4253;
}

.dark-layout .divider.divider-dark .divider-text::before,
.dark-layout .divider.divider-dark .divider-text::after {
    border-color: #3b4253 !important;
}

.dark-layout .timeline .timeline-item {
    border-color: #3b4253;
}

.dark-layout .timeline .timeline-item .timeline-point:not(.timeline-point-indicator) {
    background-color: #283046;
}

.dark-layout .timeline .timeline-item .timeline-event .timeline-event-time {
    color: #676d7d;
}

.dark-layout .timeline .timeline-item:last-of-type:after {
    background: linear-gradient(#3b4253, transparent);
}

.dark-layout .card {
    background-color: #27272B;
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);
}

.dark-layout .card .card-footer {
    border-color: #3b4253;
}

.dark-layout .card.overlay-img-card .card-img-overlay span,
.dark-layout .card.overlay-img-card .card-img-overlay p,
.dark-layout .card.overlay-img-card .card-img-overlay .card-body {
    color: #fff;
}

.dark-layout .card-developer-meetup .meetup-header .meetup-day {
    border-right-color: #404656;
}

.dark-layout .card-profile .profile-image-wrapper .profile-image {
    background-color: #161d31;
}

.dark-layout .business-card .business-items .business-item {
    border-color: #3b4253;
}

.dark-layout .card-app-design .design-planning-wrapper .design-planning {
    background-color: #161d31;
}

.dark-layout .card-tiny-line-stats .apexcharts-series-markers .apexcharts-marker {
    stroke: #00cfe8;
}

.dark-layout .card-tiny-line-stats .apexcharts-series-markers:last-child .apexcharts-marker {
    fill: #283046;
}

.dark-layout .card-revenue-budget .revenue-report-wrapper {
    border-right-color: #3b4253;
}

.dark-layout .card-revenue-budget .budget-wrapper .apexcharts-series:not(:first-child) path {
    stroke: #b4b7bd;
}

.dark-layout .earnings-card .apexcharts-canvas .apexcharts-pie .apexcharts-datalabel-label {
    fill: #b4b7bd !important;
}

.dark-layout .earnings-card .apexcharts-canvas .apexcharts-pie .apexcharts-datalabel-value {
    fill: #d0d2d6;
}

.dark-layout .card-company-table .avatar {
    background-color: #161d31;
}

.dark-layout input:-webkit-autofill,
.dark-layout textarea:-webkit-autofill,
.dark-layout select:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px #283046 inset !important;
    -webkit-text-fill-color: #b4b7bd !important;
}

.dark-layout input.form-control,
.dark-layout select.form-select,
.dark-layout textarea.form-control {
    background-color: #1C1C24;
    color: #b4b7bd;
    border-radius: 6px;
}

.dark-layout input.form-control:not(:focus):not(.select2-search__field),
.dark-layout select.form-select:not(:focus):not(.select2-search__field),
.dark-layout textarea.form-control:not(:focus):not(.select2-search__field) {
    border: 1px solid #3D3D42 !important;
    border-radius: 5px !important;
}

.dark-layout span ul li input.select2-search__field {
    color: white;
}

.dark-layout input.form-control::-moz-placeholder,
.dark-layout select.form-select::-moz-placeholder,
.dark-layout textarea.form-control::-moz-placeholder {
    color: #676d7d;
}

.dark-layout input.form-control:-ms-input-placeholder,
.dark-layout select.form-select:-ms-input-placeholder,
.dark-layout textarea.form-control:-ms-input-placeholder {
    color: #676d7d;
}

.dark-layout input.form-control::placeholder,
.dark-layout select.form-select::placeholder,
.dark-layout textarea.form-control::placeholder {
    color: #676d7d;
}

.dark-layout input.form-control:disabled,
.dark-layout input.form-control[readonly=readonly],
.dark-layout select.form-select:disabled,
.dark-layout select.form-select[readonly=readonly],
.dark-layout textarea.form-control:disabled,
.dark-layout textarea.form-control[readonly=readonly] {
    opacity: 0.5;
}

.dark-layout .form-floating textarea.form-control::-moz-placeholder {
    color: transparent;
}

.dark-layout .form-floating textarea.form-control:-ms-input-placeholder {
    color: transparent;
}

.dark-layout .form-floating textarea.form-control::placeholder {
    color: transparent;
}

.dark-layout .form-control::-webkit-file-upload-button {
    background-color: #283046;
    color: #d0d2d6;
}

.dark-layout .form-control::file-selector-button {
    background-color: #283046;
    color: #d0d2d6;
}

.dark-layout .form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
    background-color: #333a4f;
}

.dark-layout .form-control:hover:not(:disabled):not([readonly])::file-selector-button {
    background-color: #333a4f;
}

.dark-layout .char-textarea.active {
    color: #b4b7bd !important;
}

.dark-layout .char-textarea.max-limit {
    color: #ea5455 !important;
}

.dark-layout .form-check-input:not(:checked) {
    background-color: #27272B;
    border-color: #BABFC7;
}

.dark-layout .form-check-input:not(:checked):not(:indeterminate) {
    background-color: #283046;
}

.dark-layout a {
    color: #97C4FF;
}

.dark-layout .form-check-input:not(:checked):not(:indeterminate):not(:focus) {
    background-color: var(--main-color-dark);
    border-color: #BABFC7;
}

.dark-layout .form-check-input:not(:checked):not(:indeterminate):disabled {
    background-color: #444b60 !important;
    border-color: #444b60 !important;
}

.dark-layout .form-switch .form-check-input:not(:checked) {
    background-color: var(--main-color-dark);
}

.dark-layout .form-switch .form-check-input:disabled {
    background-color: #1b2337;
    border-color: #1b2337;
}

.dark-layout .form-switch .form-check-input:not(:checked):not(:focus) {
    background-image: url("data:image/svg+xml,<svg width='26px' height='26px' viewBox='0 0 26 27' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><defs><circle id='path-1' cx='8' cy='8' r='8'></circle><filter x='-40.6%' y='-21.9%' width='168.8%' height='168.8%' filterUnits='objectBoundingBox' id='filter-2'><feOffset dx='-1' dy='2' in='SourceAlpha' result='shadowOffsetOuter1'></feOffset><feGaussianBlur stdDeviation='1.5' in='shadowOffsetOuter1' result='shadowBlurOuter1'></feGaussianBlur><feColorMatrix values='0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0' type='matrix' in='shadowBlurOuter1'></feColorMatrix></filter></defs><g id='Artboard' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'><g id='switches-dot' transform='translate(5.000000, 5.000000)' fill-rule='nonzero'><g id='Oval'><use fill='black' fill-opacity='1' filter='url(%23filter-2)' xlink:href='%23path-1'></use><use fill='%23fff' xlink:href='%23path-1'></use></g></g></g></svg>");
}

.dark-layout select.form-select:not([multiple=multiple]) {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .was-validated .form-control:invalid,
.dark-layout .was-validated .form-control.is-invalid,
.dark-layout .was-validated .form-check-input:invalid,
.dark-layout .was-validated .form-check-input.is-invalid,
.dark-layout .was-validated select.form-select:not(:focus):invalid,
.dark-layout .was-validated select.form-select:not(:focus).is-valid {
    border-color: #ea5455 !important;
}

.dark-layout .was-validated .form-control:valid,
.dark-layout .was-validated .form-control.is-valid,
.dark-layout .was-validated .form-check-input:valid,
.dark-layout .was-validated .form-check-input.is-valid,
.dark-layout .was-validated select.form-select:not(:focus):valid,
.dark-layout .was-validated select.form-select:not(:focus).is-valid {
    border-color: #28c76f !important;
}

.dark-layout .wizard .steps ul .disabled a,
.dark-layout .wizard .steps ul .done a {
    color: #b4b7bd !important;
}

.dark-layout .wizard .steps ul .disabled a:hover,
.dark-layout .wizard .steps ul .done a:hover {
    color: #b4b7bd;
}

.dark-layout .wizard .steps ul .disabled a .step,
.dark-layout .wizard .steps ul .done a .step {
    background-color: #161d31 !important;
    color: #fff !important;
}

.dark-layout .input-group .input-group-text {
    background-color: #1C1C24;
    border-color: #3b4253;
    color: #b4b7bd;
}

.dark-layout .input-group:focus-within .form-control,
.dark-layout .input-group:focus-within .input-group-text {
    border-color: #7367f0;
    box-shadow: none;
}

.dark-layout .form-label-group > input:not(:focus):not(:-ms-input-placeholder) ~ label,
.dark-layout .form-label-group > textarea:not(:focus):not(:-ms-input-placeholder) ~ label {
    color: #b4b7bd !important;
}

.dark-layout .form-label-group > input:not(:focus):not(:placeholder-shown) ~ label,
.dark-layout .form-label-group > textarea:not(:focus):not(:placeholder-shown) ~ label {
    color: #b4b7bd !important;
}

.dark-layout .counter-value {
    color: #fff;
}

.dark-layout .custom-option-item-check:not(:checked) + .custom-option-item {
    border-color: #3b4253;
}

.dark-layout .table td,
.dark-layout .table th {
    background-color: #27272B;
    color: #27272B;
    border-color: #3b4253 !important;
}

.dark-layout .table .thead tr:not([class*=table-]) th,
.dark-layout .table .thead tr:not([class*=table-]) td,
.dark-layout .table tbody tr:not([class*=table-]) th,
.dark-layout .table tbody tr:not([class*=table-]) td {
    border-color: #3b4253;
    color: white;
}

.dark-layout .table .thead tr:not([class*=table-]) th code,
.dark-layout .table .thead tr:not([class*=table-]) td code,
.dark-layout .table tbody tr:not([class*=table-]) th code,
.dark-layout .table tbody tr:not([class*=table-]) td code {
    background-color: #283046;
}

.dark-layout .table thead tr th,
.dark-layout .table tfoot tr th {
    border-color: #3b4253;
    background-color: #343d55;
    color: white;
}

.dark-layout .table thead.table-dark th {
    color: #4b4b4b;
    background-color: #fff;
}

.dark-layout .table:not(.table-dark):not(.table-light) thead:not(.table-dark) th,
.dark-layout .table:not(.table-dark):not(.table-light) tfoot:not(.table-dark) th {
    background-color: #1C1C24;
}

.dark-layout .table.table-dark {
    background-color: #fff;
}

.dark-layout .table.table-dark.table-striped tbody tr:nth-of-type(odd) {
    --bs-table-accent-bg: rgba(75, 75, 75, 0.05);
}

.dark-layout .table.table-dark td,
.dark-layout .table.table-dark th {
    border-color: #f8f9fa !important;
    background-color: #fff;
}

.dark-layout .table.table-dark td .text-white,
.dark-layout .table.table-dark th .text-white {
    color: #4b4b4b !important;
}

.dark-layout .table tbody tr.table-dark td,
.dark-layout .table tbody tr.table-dark th {
    color: #fff;
}

.dark-layout .table tbody tr.table-active td,
.dark-layout .table tbody tr.table-active th {
    color: #b4b7bd;
    background-color: #696e7e;
}

.dark-layout .table.table-bordered {
    border-color: #3b4253;
}

.dark-layout .table.table-hover tbody tr:hover {
    --bs-table-accent-bg: #242b3d;
}

.dark-layout .table.table-hover tbody tr th,
.dark-layout .table.table-hover tbody tr td {
    background-color: unset;
}

.dark-layout .table.table-striped tbody tr:nth-of-type(odd) {
    /*    --bs-table-accent-bg: #242b3d;*/
}

.dark-layout .dataTables_wrapper .dt-buttons .buttons-copy,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-excel,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-pdf,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-print,
.dark-layout .dataTables_wrapper .dt-buttons .btn-secondary,
.dark-layout .dataTables_wrapper .dt-buttons .dt-button-collection [class*=buttons-] {
    color: #fff;
    background-color: #161d31 !important;
}

.dark-layout .dataTables_wrapper .dt-buttons .buttons-copy:active,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-excel:active,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-pdf:active,
.dark-layout .dataTables_wrapper .dt-buttons .buttons-print:active,
.dark-layout .dataTables_wrapper .dt-buttons .btn-secondary:active,
.dark-layout .dataTables_wrapper .dt-buttons .dt-button-collection [class*=buttons-]:active {
    background-color: #7367f0 !important;
    color: #fff;
}

.dark-layout .dataTables_wrapper .dt-button-collection > div[role=menu] {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);
}

.dark-layout .dataTables_wrapper .table.dataTable tr.group td {
    background-color: #242b3d;
    color: #d0d2d6;
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting:before,
.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_asc:before,
.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_desc:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-up'%3E%3Cpolyline points='18 15 12 9 6 15'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting:after,
.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_asc:after,
.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_desc:after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting:before,
.dark-layout .dataTables_wrapper .table.dataTable thead .sorting:after {
    opacity: 0.5;
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_asc:before {
    opacity: 1;
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_asc:after {
    opacity: 0.5;
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_desc:after {
    opacity: 1;
}

.dark-layout .dataTables_wrapper .table.dataTable thead .sorting_desc:before {
    opacity: 0.5;
}

.dark-layout .dtr-modal .dtr-modal-display {
    background-color: #161d31;
}

.dark-layout .dtr-modal div.dtr-modal-close {
    background-color: transparent;
    border-color: transparent;
}

.dark-layout .getting-started .clockCard p {
    color: #b4b7bd !important;
}

.dark-layout #user-profile .profile-header .navbar {
    background-color: #283046;
}

.dark-layout #user-profile .profile-header .navbar .navbar-toggler {
    color: #b4b7bd;
    border-color: #3b4253;
}

.dark-layout .search-bar .form-control {
    background-color: #283046;
}

.dark-layout .search-building-container .select2-container .select2-selection--single .select2-selection__rendered {
    color: var(--main-bg-color-dark-card) !important;
}

.dark-layout .blog-edit-wrapper .border {
    border-color: #3b4253 !important;
}

.dark-layout .kb-search-content-info .kb-search-content .card-img-top {
    background-color: #3f4860;
}

.dark-layout .list-group-circle .list-group-item:not([class*=list-group-item-]):hover,
.dark-layout .list-group-circle .list-group-item:not([class*=list-group-item-]):focus,
.dark-layout .list-group-circle .list-group-item:not([class*=list-group-item-]):active,
.dark-layout .list-group-circle .list-group-item-action:hover,
.dark-layout .list-group-circle .list-group-item-action:focus,
.dark-layout .list-group-circle .list-group-item-action:active {
    background-color: transparent !important;
}

.dark-layout .pricing-card .card.basic-pricing,
.dark-layout .pricing-card .card.enterprise-pricing,
.dark-layout .pricing-card .card.standard-pricing {
    border-color: #3b4253;
}

.dark-layout .content-area-wrapper {
    border-color: #3b4253 !important;
}

.dark-layout .content-area-wrapper .sidebar .sidebar-content {
    background-color: #283046 !important;
}

.dark-layout .content-area-wrapper .app-fixed-search {
    background-color: #283046 !important;
    border-color: #3b4253 !important;
}

.dark-layout .content-area-wrapper .content-right {
    border-color: #3b4253 !important;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .app-action {
    border-color: #3b4253;
    background-color: #283046;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .app-action .action-right .list-inline-item .dropdown-toggle {
    color: #b4b7bd;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail {
    border-color: #3b4253;
    background-color: #283046;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail:hover {
    box-shadow: 0 3px 10px 0 #283046;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail.selected-row-bg {
    background-color: rgba(115, 103, 240, 0.06);
    border-color: #3b4253;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail .user-details p,
.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail .user-details .mail-date,
.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail .mail-message p,
.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .user-mail .mail-message .mail-date {
    color: #676d7d;
}

.dark-layout .email-application .content-area-wrapper .email-app-list .email-user-list .mail-read {
    background-color: #242b3d;
}

.dark-layout .email-application .content-area-wrapper .email-app-details {
    border-color: #3b4253;
}

.dark-layout .email-application .content-area-wrapper .email-app-details .email-scroll-area {
    background-color: #161d31;
}

.dark-layout .email-application .content-area-wrapper .email-app-details .email-detail-header {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .email-application .content-area-wrapper .email-app-details .email-detail-header .email-header-right .list-inline-item .dropdown-toggle,
.dark-layout .email-application .content-area-wrapper .email-app-details .email-detail-header .email-header-right .list-inline-item .action-icon {
    color: #b4b7bd;
}

.dark-layout .email-application .content-area-wrapper .email-app-details .email-info-dropup .dropdown-toggle::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23676d7d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .email-application .content-area-wrapper .email-app-details .mail-message-wrapper,
.dark-layout .email-application .content-area-wrapper .email-app-details .email-detail-head {
    border-color: #3b4253 !important;
}

.dark-layout .email-application .content-area-wrapper #compose-mail .compose-mail-form-field,
.dark-layout .email-application .content-area-wrapper #compose-mail .ql-toolbar,
.dark-layout .email-application .content-area-wrapper #compose-mail .ql-container {
    border-color: #3b4253;
}

.dark-layout .email-application .content-area-wrapper #compose-mail label {
    color: #b4b7bd;
}

.dark-layout .email-application .content-area-wrapper #compose-mail .modal-body {
    border-bottom-left-radius: 0.357rem;
    border-bottom-right-radius: 0.357rem;
}

.dark-layout .chat-application .content-area-wrapper {
    border-color: #3b4253;
}

.dark-layout .chat-application .sidebar .chat-profile-sidebar {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .chat-application .sidebar .sidebar-content {
    border-color: #3b4253;
}

.dark-layout .chat-application .sidebar .sidebar-content .chat-fixed-search {
    border-color: #3b4253;
}

.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li:not(.active):hover {
    background: #161d31;
}

.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li .chat-info .card-text,
.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li .chat-time {
    color: #676d7d;
}

.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li.active .chat-info .card-text,
.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li.active .chat-time {
    color: #fff;
}

.dark-layout .chat-application .sidebar .sidebar-content .chat-user-list-wrapper .chat-users-list li img {
    border-color: #3b4253;
}

.dark-layout .chat-application .avatar-border img {
    border-color: #3b4253;
}

.dark-layout .chat-application .content-right .chat-app-window .start-chat-area {
    background-color: #283046;
}

.dark-layout .chat-application .content-right .chat-app-window .start-chat-area .start-chat-icon,
.dark-layout .chat-application .content-right .chat-app-window .start-chat-area .start-chat-text {
    background: #283046;
    color: #b4b7bd;
}

.dark-layout .chat-application .content-right .chat-app-window .start-chat-area,
.dark-layout .chat-application .content-right .chat-app-window .user-chats {
    background-image: url("data:image/svg+xml;base64,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");
    background-color: #1e232f;
}

.dark-layout .chat-application .content-right .chat-app-window .user-chats .divider .divider-text {
    background: #283046;
}

.dark-layout .chat-application .content-right .chat-app-window .user-chats .divider .divider-text:before,
.dark-layout .chat-application .content-right .chat-app-window .user-chats .divider .divider-text:after {
    border-color: #3b4253;
}

.dark-layout .chat-application .content-right .chat-app-window .user-chats .avatar img {
    border-color: #3b4253;
}

.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-left .chat-content {
    background-color: #283046;
}

.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-left .chat-content p {
    color: #b4b7bd;
}

.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-content p {
    color: #fff;
}

.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-header .dropdown-toggle {
    color: #b4b7bd;
}

.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-header,
.dark-layout .chat-application .content-right .chat-app-window .active-chat .chat-app-form {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .chat-application .content-right .user-profile-sidebar {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .chat-widget .user-chats {
    background-color: #384056 !important;
}

.dark-layout .chat-widget .card-header,
.dark-layout .chat-widget .chat-app-form {
    background-color: #283046;
}

.dark-layout .chat-widget .chat-app-window .user-chats .chat-left .chat-content {
    background-color: #283046 !important;
    color: #b4b7bd !important;
}

.dark-layout .chat-widget .chat-app-window .user-chats .avatar img {
    border-color: #161d31;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .kanban-title-board {
    color: #fff;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .kanban-title-board:hover,
.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-board-header .kanban-title-board:focus {
    background-color: #283046;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item {
    background-color: #283046;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item .item-dropdown i.dropdown-toggle,
.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item .item-dropdown svg.dropdown-toggle {
    stroke: #fff;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item .kanban-title-button {
    color: #b4b7bd;
}

.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item i,
.dark-layout .kanban-application .kanban-wrapper .kanban-container .kanban-board .kanban-item svg {
    stroke: #b4b7bd;
}

.dark-layout .kanban-application .update-item-sidebar .nav-tabs .nav-item .nav-link,
.dark-layout .kanban-application .update-item-sidebar .tab-content .tab-pane {
    background-color: transparent;
}

.dark-layout .kanban-item.gu-mirror {
    background-color: #283046;
}

.dark-layout .todo-application .content-area-wrapper .sidebar .todo-form .todo-item-action {
    color: #b4b7bd;
}

.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list-wrapper {
    background-color: #283046 !important;
    border-color: #3b4253;
}

.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list .todo-item:not(:first-child) {
    border-color: #3b4253;
}

.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list .todo-item.completed .todo-title {
    color: #676d7d;
}

.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list .todo-item:hover {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);
}

.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list .todo-item .todo-item-action .todo-item-favorite:not(.text-warning) i,
.dark-layout .todo-application .content-area-wrapper .content-right .todo-task-list .todo-item .todo-item-action .todo-item-favorite:not(.text-warning) svg {
    color: #b4b7bd;
}

.dark-layout .todo-item.gu-mirror {
    background-color: #283046;
    border-color: #3b4253;
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24);
}

.dark-layout .todo-item.gu-mirror.completed .todo-title {
    color: #676d7d;
}

.dark-layout .ecommerce-application .ecommerce-card .btn-light {
    background-color: #161d31 !important;
    color: #fff;
    border-color: transparent;
}

.dark-layout .ecommerce-application .content-right .ecommerce-header-items .result-toggler .search-results,
.dark-layout .ecommerce-application .content-body .ecommerce-header-items .result-toggler .search-results {
    color: #d0d2d6;
}

.dark-layout .ecommerce-application .content-right .ecommerce-header-items .result-toggler .shop-sidebar-toggler i,
.dark-layout .ecommerce-application .content-right .ecommerce-header-items .result-toggler .shop-sidebar-toggler svg,
.dark-layout .ecommerce-application .content-body .ecommerce-header-items .result-toggler .shop-sidebar-toggler i,
.dark-layout .ecommerce-application .content-body .ecommerce-header-items .result-toggler .shop-sidebar-toggler svg {
    color: #d0d2d6;
}

.dark-layout .ecommerce-application .content-right .grid-view .ecommerce-card .card-body,
.dark-layout .ecommerce-application .content-right .list-view .ecommerce-card .card-body,
.dark-layout .ecommerce-application .content-body .grid-view .ecommerce-card .card-body,
.dark-layout .ecommerce-application .content-body .list-view .ecommerce-card .card-body {
    border-color: #3b4253;
}

.dark-layout .ecommerce-application .content-right .grid-view .ecommerce-card .item-options .wishlist span,
.dark-layout .ecommerce-application .content-right .list-view .ecommerce-card .item-options .wishlist span,
.dark-layout .ecommerce-application .content-body .grid-view .ecommerce-card .item-options .wishlist span,
.dark-layout .ecommerce-application .content-body .list-view .ecommerce-card .item-options .wishlist span {
    color: #5e5873;
}

.dark-layout .ecommerce-application .content-right .grid-view .ecommerce-card .item-options .cart span,
.dark-layout .ecommerce-application .content-right .list-view .ecommerce-card .item-options .cart span,
.dark-layout .ecommerce-application .content-body .grid-view .ecommerce-card .item-options .cart span,
.dark-layout .ecommerce-application .content-body .list-view .ecommerce-card .item-options .cart span {
    color: #fff;
}

.dark-layout .ecommerce-application .grid-view .item-name a,
.dark-layout .ecommerce-application .grid-view .item-price {
    color: #b4b7bd;
}

.dark-layout .ecommerce-application .wishlist-items .ecommerce-card .move-cart .move-to-cart {
    color: #fff;
}

.dark-layout .ecommerce-application .product-checkout .checkout-options .coupons input {
    color: #b4b7bd;
}

.dark-layout .ecommerce-application .product-checkout .checkout-options .coupons input::-moz-placeholder {
    color: #b4b7bd;
}

.dark-layout .ecommerce-application .product-checkout .checkout-options .coupons input:-ms-input-placeholder {
    color: #b4b7bd;
}

.dark-layout .ecommerce-application .product-checkout .checkout-options .coupons input::placeholder {
    color: #b4b7bd;
}

.dark-layout .ecommerce-application .app-ecommerce-details .item-features,
.dark-layout .ecommerce-application .app-ecommerce-details .swiper-responsive-breakpoints.swiper-container .swiper-slide {
    background-color: #161d31;
}

.dark-layout .file-manager-application .sidebar-file-manager {
    background-color: #283046 !important;
}

.dark-layout .file-manager-application .sidebar-file-manager .jstree .jstree-container-ul .jstree-anchor {
    color: #b4b7bd;
}

.dark-layout .file-manager-application .sidebar-file-manager .storage-status i,
.dark-layout .file-manager-application .sidebar-file-manager .storage-status svg {
    color: #b4b7bd !important;
}

.dark-layout .file-manager-application .sidebar-file-manager .file-manager-title {
    color: #d0d2d6;
}

.dark-layout .file-manager-application .sidebar-file-manager.show {
    border: 1px solid #3b4253 !important;
}

.dark-layout .file-manager-application .content-area-wrapper {
    border: 1px solid #3b4253 !important;
}

.dark-layout .file-manager-application .file-manager-main-content {
    border: 1px solid #3b4253 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-app-searchbar {
    background-color: transparent !important;
    border-bottom: 1px solid #3b4253 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-app-searchbar .file-manager-toggler {
    color: #b4b7bd !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-header,
.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body {
    background-color: #283046 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-header {
    border-color: #3b4253 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body .drives .card {
    background-color: #283046;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body .view-container .file-manager-item .file-logo-wrapper {
    background-color: #161d31 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body .view-container .file-manager-item:not(.selected) {
    border-color: #3b4253 !important;
}

.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .file-size,
.dark-layout .file-manager-application .file-manager-main-content .file-manager-content-body .view-container:not(.list-view) .file-manager-item .file-accessed {
    color: #676d7d !important;
}

.dark-layout .file-manager-application #app-file-manager-info-sidebar .nav-tabs .nav-link,
.dark-layout .file-manager-application #app-file-manager-info-sidebar .tab-content .tab-pane {
    background-color: transparent;
}

.dark-layout .file-manager-application #app-file-manager-info-sidebar .nav-tabs .nav-item:not(.active) .nav-link {
    color: #b4b7bd;
}

.dark-layout .app-calendar .app-calendar-sidebar {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .app-calendar .app-calendar-sidebar .sidebar-content-title {
    color: #d0d2d6;
}

.dark-layout .app-calendar .app-calendar-sidebar .select-all ~ label,
.dark-layout .app-calendar .app-calendar-sidebar .input-filter ~ label {
    color: #d0d2d6;
}

.dark-layout .app-calendar .fc .fc-day-today {
    background: #161d31 !important;
    background-color: #161d31 !important;
}

.dark-layout .app-calendar .fc .fc-timegrid .fc-scrollgrid-section .fc-col-header-cell,
.dark-layout .app-calendar .fc .fc-timegrid .fc-scrollgrid-section .fc-timegrid-axis,
.dark-layout .app-calendar .fc .fc-timegrid .fc-scrollgrid-section .fc-daygrid-day {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc .fc-timegrid .fc-scrollgrid-section .fc-day-today {
    background-color: rgba(186, 191, 199, 0.12) !important;
}

.dark-layout .app-calendar .fc .fc-timegrid .fc-scrollgrid-section .fc-day-today.fc-col-header-cell {
    background-color: #283046 !important;
}

.dark-layout .app-calendar .fc .fc-popover {
    background: #283046;
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc .fc-popover-header .fc-popover-title,
.dark-layout .app-calendar .fc .fc-popover-header .fc-popover-close {
    color: #b4b7bd;
}

.dark-layout .app-calendar .fc tbody td,
.dark-layout .app-calendar .fc thead th {
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc .fc-scrollgrid {
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc .fc-list,
.dark-layout .app-calendar .fc th {
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc .fc-list .fc-list-day-cushion,
.dark-layout .app-calendar .fc th .fc-list-day-cushion {
    background: #161d31;
}

.dark-layout .app-calendar .fc .fc-list .fc-list-event:hover td,
.dark-layout .app-calendar .fc th .fc-list-event:hover td {
    background-color: #161d31;
}

.dark-layout .app-calendar .fc .fc-list .fc-list-event-time,
.dark-layout .app-calendar .fc th .fc-list-event-time {
    color: #b4b7bd;
}

.dark-layout .app-calendar .fc .fc-list .fc-list-event td,
.dark-layout .app-calendar .fc th .fc-list-event td {
    border-color: #3b4253;
}

.dark-layout .app-calendar .fc-timegrid-event-harness-inset .fc-timegrid-event,
.dark-layout .app-calendar .fc-timegrid-event.fc-event-mirror,
.dark-layout .app-calendar .fc-timegrid-more-link {
    box-shadow: 0 0 0 1px #3b4253;
}

.dark-layout .invoice-list-wrapper .dataTables_wrapper .invoice-list-dataTable-header {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .invoice-list-wrapper .dataTables_wrapper .invoice-data-table {
    background-color: #283046;
}

.dark-layout .invoice-list-wrapper .dataTables_wrapper .invoice-data-table tbody .selected-row-bg {
    background-color: #3b4253;
}

.dark-layout .invoice-list-wrapper .dataTables_wrapper .invoice-data-table td {
    background-color: inherit;
}

.dark-layout .invoice-edit .invoice-preview-card .invoice-product-details,
.dark-layout .invoice-add .invoice-preview-card .invoice-product-details {
    background-color: #161d31;
}

.dark-layout .invoice-edit .invoice-preview-card .invoice-product-details .product-details-border,
.dark-layout .invoice-add .invoice-preview-card .invoice-product-details .product-details-border {
    background-color: #283046;
    border-color: #3b4253;
}

.dark-layout .invoice-print .form-control {
    background-color: #283046;
}

.dark-layout .app-user-view .plan-card {
    border-color: #7367f0 !important;
}

.dark-layout .customizer {
    background-color: #283046;
}

.dark-layout .customizer .customizer-close i,
.dark-layout .customizer .customizer-close svg {
    color: #b4b7bd;
}

.dark-layout .footer-fixed .footer {
    background-color: #283046;
}

.dark-layout .horizontal-layout.navbar-sticky .horizontal-menu-wrapper .navbar-horizontal.header-navbar.fixed-top {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.75) !important;
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper {
    background: linear-gradient(to bottom, rgba(37, 43, 71, 0.76) 44%, rgba(56, 53, 53, 0.46) 73%, rgba(255, 255, 255, 0) 100%) !important;
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar {
    background: #283046 !important;
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar.navbar-shadow {
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.75);
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar.navbar-horizontal ul#main-menu-navigation > li:hover:not(.active) > a {
    background: #161d31;
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar.navbar-horizontal .active .nav-link.dropdown-toggle::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar.navbar-horizontal.navbar-dark {
    background: #283046 !important;
}

.dark-layout .horizontal-layout .horizontal-menu-wrapper .header-navbar.navbar-horizontal .nav-link.dropdown-toggle::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-submenu.show {
    background-color: #283046 !important;
    color: #b4b7bd;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu a:hover {
    color: #d0d2d6 !important;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu .disabled a {
    color: #676d7d;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu .dropdown-item {
    color: #d0d2d6;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu .dropdown-toggle::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23b4b7bd' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu .dropdown-toggle:hover::after,
.dark-layout .horizontal-layout .main-menu-content .navbar-nav .dropdown-menu .dropdown-toggle:active::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav > li.active > a > i,
.dark-layout .horizontal-layout .main-menu-content .navbar-nav > li.active > a > svg {
    color: #fff !important;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav > li.active .dropdown-menu li.active > a {
    background: #161d31 !important;
    color: #b4b7bd;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav > li.active .dropdown-menu li.active > a:hover {
    color: #7367f0 !important;
}

.dark-layout .horizontal-layout .main-menu-content .navbar-nav > li.active .dropdown-menu li.open.active > a {
    color: #b4b7bd !important;
}

.dark-layout .building-contacts-table a,
.dark-layout tbody .color-black {
    color: white;
}

.dark-layout .iconContact a {
    color: white;
}

.dark-layout #show_info_display {
    background-color: #1C1C24 !important;
    color: white !important;
}

.dark-layout #display_info_button {
    background-color: #1C1C24 !important;
    color: white !important;
}

.dark-layout #display_seguimiento_button {
    background-color: #1C1C24 !important;
    color: white !important;
}

.dark-layout .card-header-background {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(45deg, #223A8F 100%, #304FBE 100%) !important;
}

.dark-layout .selected-frase-in-comment-interaction {
    background-color: #97C4FF !important;
    color: black !important;
}

.dark-layout .class_celde_color {
    background-color: var(--main-bg-color-dark-th) !important;
}

.dark-layout .display_info_button_header_d {
    background-color: var(--main-bg-color-dark-th) !important;
}

.dark-layout .display_info_button_header_c {
    background-color: var(--main-bg-color-dark-th) !important;
}

.dark-layout table > tbody > tr > td > a,
.dark-layout table > tbody > tr > td > div > a {
    color: white !important;
}

.dark-layout .accordion .accordion-body {
    background-color: var(--main-bg-color-dark-th);
}

.dark-layout .create_edit_back_color {
    color: black !important;
    background-color: var(--main-blue-accent-dark) !important;
    font-style: normal;
}

.dark-layout .titleTables.btn-outline-primary-show.no-border {
    color: white !important;
}

.dark-layout .form-check-input:checked {
    background-color: var(--main-blue-accent-dark);
    border-color: var(--main-blue-accent-dark);
}

.dark-layout .titleTables,
.dark-layout ion-icon {
    color: var(--main-text-color-dark) !important;
}

.dark-layout .bool_fields_back_dark:checked {
    background-color: var(--main-blue-accent-dark);
}

.dark-layout .form-check-input:checked {
    background-color: var(--main-blue-accent-dark);
    border-color: var(--main-blue-accent-dark);
}

.dark-layout .white-field.form-control {
    background-color: var(--main-bg-color-dark-th) !important;
    border: 1px solid #3D3D42;
    border-radius: 5px;
}

.dark-layout .col-md-10.text-center.white-field {
    background-color: var(--main-bg-color-dark-th);
    color: var(--main-text-color-dark);
    border-color: var(--main-bg-color-dark-th);
}

.dark-layout .form-control {
    background-color: var(--main-bg-color-dark-th);
    color: var(--main-text-color-dark);
    border: 1px solid var(--main-border-color);
}

.dark-layout .custom-select.mr-sm-2.white-field {
    background-color: var(--main-bg-color-dark-th);
    color: var(--main-text-color-dark);
    border: 1px solid var(--main-border-color);
}

.dark-layout .backstrap-file-input:lang(en) ~ .backstrap-file-label::after {
    content: "Subir";
    background-color: var(--main-blue-accent-dark);
    border: 1px solid var(--main-border-color);
    padding: 0.2rem 1rem;
    font-weight: bold;
}

.dark-layout .comments_in_show_cases {
    background-color: var(--main-bg-color-dark-card);
    color: white;
}

.dark-layout .CodeMirror {
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout .CodeMirror-line {
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout .porter-svg path {
    fill: #97C4FF;
}

.dark-layout .editor-toolbar {
    background-color: #3D3D42;
}

.dark-layout .modal-body.bg-light {
    background-color: var(--main-bg-color-dark-card) !important;
}

.dark-layout .button.card-case-item {
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout button.card-case-item {
    background-color: var(--main-bg-color-dark-card) !important;
}

.dark-layout #foxsys-logo-in-menu {
    background-image: url("/img/foxsys-logo-green2.png");
}

.dark-layout .daterangepicker {
    background-color: var(--main-bg-color-dark-card) !important;
}

.dark-layout .daterangepicker .drp-calendar .calendar-table,
.dark-layout .swal-modal {
    background-color: var(--main-bg-color-dark-th);
}

.dark-layout .daterangepicker .drp-calendar .calendar-table .table-condensed .available {
    background-color: var(--main-border-color);
}

.dark-layout .daterangepicker .drp-calendar .calendar-table .table-condensed .in-range,
.dark-layout .daterangepicker .drp-calendar .calendar-table .table-condensed .start-date {
    background-color: var(--main-blue-accent-dark);
}

.dark-layout .table-striped > tbody > tr:nth-of-type(odd) {
    color: var(--bs-table-striped-color);
    background-color: var(--main-bg-color-dark-card) !important;
}

.dark-layout .table-striped > tbody > tr:nth-of-type(even) {
    color: var(--bs-table-striped-color);
    background-color: #3D3D42 !important;
}

.dark-layout .border-grey {
    border: 1px solid #27272B;
}

.dark-layout .clicked {
    background-color: #1C1C24;
}

.dark-layout .btn-casos {
    color: #ADE1FB !important;
}

.dark-layout .filter-select::placeholder {
    color: white;
}

.dark-layout #principal_phone_in_contact {
    color: white !important;
}

.dark-layout #principal_phone_in_contact2 {
    color: white !important;
}

.dark-layout .custom-dropdown-btn {
    border-color: var(--main-blue-accent-dark);
}

.dark-layout .custom-dropdown-btn > ion-icon {
    color: var(--main-blue-accent-dark) !important;
}

.dark-layout #comment_case_repetable[readonly] {
    opacity: 0.7;
    background-color: rgba(194, 188, 188, 0.1);
}

.dark-layout #target-modal-sechedule {
    color: white;
}

.dark-layout .buidling-state-span {
    color: white;
}

.dark-layout .transferred-calls-cont {
    background-color: var(--ColorBannerBlack);
}

.dark-layout #toggle-transferred > span {
    content: "";
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 1.1rem;
    height: 1rem;
    width: 1rem;
    display: inline-block;
    position: absolute;
    right: 20px;
    top: 14px;
    transform: rotate(270deg);
    transition: all 0.2s ease-out;
    margin-right: 15px;
}

.dark-layout .testing-sidebar-bg {
    background-color: #4F4F55 !important;
}


.dark-layout .custom-dropdown-content {
    background-color: #0d0d18 !important;
    cursor: pointer;
    z-index: 90;
}

.dark-layout .custom-dropdown-content a {
    color: white;
}

.dark-layout .custom-dropdown-content a:hover {
    color: rgba(255, 255, 255, 0.5);
}

.dark-layout #icon_close_date > ion-icon {
    color: black !important;
}

.dark-layout .title-access-people div > ion-icon,
.dark-layout .title-access-people div > label {
    color: var(--ColorGreenTitleDark) !important;
}


.dark-layout .building-contacts-table a,
.dark-layout .btn-link,
.dark-layout tbody .color-black {
    color: white !important;
}

.dark-layout .add-repeatable-element-button {
    color: var(--ColorBluelabelDark) !important;

}


.dark-layout .name-color {
    color: white !important;
}

.dark-layout .new-case-building-show {
    color: #97c4ff !important;
    background: transparent;
    border: 1px solid #97c4ff;
}

.dark-layout .search-container {
    background: transparent !important;
}

.dark-layout .search-input {
    background: transparent !important;
    color: white !important;
}

.dark-layout .filter-building {
    color: #97C4FF !important;
}

.dark-layout .people-access-container {
    border: 2px solid #97C4FF !important;
    background: #323740;
}

.dark-layout .cadete-container {
    border: 2px solid #97C4FF !important;
    background: #323740;
}

.dark-layout .people-rules-container {
    border: 2px solid #3D3D42;
}

.dark-layout .building-card-info-header-tools {
    & span.active {
        background-color: var(--main-bg-color-dark-card);;
        color: white;
    }
}

.dark-layout .building-card-info-container {
    background-color: var(--main-bg-color-dark-card);
}

.dark-layout .cont-3cx-call-to-unit {
    background: #A9F9B5;
    color: #4B4B4B;
}

.dark-layout .icon-3cx {
    color: #4B4B4B !important;
}

.dark-layout .accordion-item-dark {
    background: yellow !important;
}

.dark-layout .accordion-button {
    background: transparent !important;
}

.dark-layout .accordion-rules-buttons {
    background: #4B4B4B;
    color: white !important;
}

.dark-layout .accordion-information-equipments-buttons {
    background: #4B4B4B;
    color: white !important;
}

.dark-layout .accordion-header button {
    color: white !important;
}

.dark-layout .announcement-item {
    border-bottom: 1px solid #3D3D42;
    background: #4B4B4B;
}

.dark-layout .porter-cleaning-input-search {
    border: 1px solid #3D3D42;
    background-color: #27272B;
    color: white !important;
}

.dark-layout .building-schedule-title {
    color: #97C4FF !important;

    & ion-icon {
        color: #97C4FF !important;
    }
}

.dark-layout .porter-cleaning-item {
    background: #4B4B4B;
    color: white !important;
}

.porter-cleaning-item-name {
    color: white;
}

.dark-layout .no-data-porter-cleaning {
    background: #4B4B4B;
}

.dark-layout .porter-cleaning-container {
    border: 2px solid #3D3D42;
}

.dark-layout .cadete-container-general {
    border: 2px solid #3D3D42;
}

.dark-layout .building-information-container {
    border: 2px solid #3D3D42;
}

.dark-layout .people-access-container-general {
    border: 2px solid #3D3D42;
    border-radius: 12px;
    /*padding: 16px;*/
    width: 100%;
    max-height: 211px;
    margin-top: 10px;
}

.dark-layout .equipment-container {
    border-radius: 15px;
    border: 2px solid #3D3D42;
    width: 100%;
    margin-top: 10px;
}

.dark-layout .btn-new-case {
    color: #97c4ff !important;
}

.dark-layout .blue-text-dark-mode {
    color: #97c4ff !important;
    background: #343A44 !important;
}

.dark-layout #case_id_to_copy {
    color: #97c4ff !important;
}

.dark-layout .building-cases-table {
    background: #27272B !important;
}

.dark-layout .building-services-table {
    background: #27272B !important;
}

.dark-layout .cadete-hall {
    border: 1px solid #A9F9B5;
    background: #A9F9B526;
}

.dark-layout .cadete-up {
    color: #FFE4B3;
    border: 1px solid #FFE4B3;
    background: #FFE4B326;
}

.dark-layout .audio-valid {
    color: #A9F9B5;
}

.dark-layout .building-flats-apartments {
    color: #97C4FF;
}

.dark-layout .doors-list {
    background: transparent;
}

.dark-layout .doors-list-item {
    background-color: #4B4B4B;
}

.dark-layout .doors-list-item-tower {
    color: #97c4ff;
}

.dark-layout .doors-list-item-action {
    color: #97c4ff;
}

.dark-layout .doors-list-item-action ion-icon {
    color: #97c4ff !important;
}

.dark-layout .doors-button-item {
    background-color: #4B4B4B;
    color: #97c4ff !important;
}

.dark-layout .doors-button-item:hover {
    background-color: rgba(74, 74, 74, 0.6);
}

.dark-layout .doors-button-item ion-icon {
    color: #97c4ff !important;
}

.dark-layout .doors-list-border {
    border: 2px solid #3D3D42;
}

.dark-layout #buildingInfoModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
}

.dark-layout #buildingCardProductsModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
}

.dark-layout #peopleAccessInfoModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
}

.dark-layout #buildingAnnouncementsModal {
    background: #1C1C24;
    border: 1px solid #3D3D42;
    color: white;
}

.dark-layout #buildingCardInfoModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
}

.dark-layout #buildingCardEquipmentModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
}

.dark-layout #buildingCardServiceTypeModal {
    background: #1C1C24;
    border: 1px solid #DEE6EF;
    color: white;
    box-shadow: 8px 8px 10px 0px #0000000D;
}

.dark-layout #peopleAccessInfoModal span {
    color: white;
}

.dark-layout #buildingInfoModal span {
    color: white;
}

.dark-layout #buildingCardInfoModal span {
    color: white;
}

.dark-layout #buildingCardEquipmentModal span {
    color: white;
}

.dark-layout .buildingHoverModal {
    background: #1C1C24;
    border: 1px solid #3D3D42;
    color: white;
}

.dark-layout .buildingHoverModal span {
    color: white;
}

.dark-layout .direct-ringing {
    color: #292933 !important;
}

.dark-layout .direct-ringing span ion-icon {
    color: #292933 !important;
}

.dark-layout .products-icon-color-yes {
    color: #A9F9B5;
}

.dark-layout .products-icon-color-no {
    color: #F8A999;
}

.dark-layout .select2-container .select2-selection, .dark-layout .select2-container .select2-selection__placeholder {
    background: transparent !important;
    color: #1F1F1F;
}

.dark-layout .building-card-info-header-number {
    color: #223A8F;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #1F1F1F !important;
}

.dark-layout .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: white !important;
}

.dark-layout .select2-selection__clear {
    color: white;
}

.dark-layout .total-apartments-and-contacts {
    color: white;
}

.dark-layout .container-no-particularidades {
    background: #4B4B4B;
}

.dark-layout .accordion-transparent {
    background-color: transparent !important;
}

.dark-layout .cadete-icon-close {
    color: #B60200;
!important;
}

.dark-layout .cadete-icon-checkmark {
    color: #238D5A !important;
}

.dark-layout .cadete-icon-help {
    color: #FF9F43 !important;
}

.dark-layout .hovers-text {
    color: white !important;
}

.dark-layout .porter-cleaning-item-phone {
    color: #ADE1FB;
}

.dark-layout .particularidades-atc-accordion-container {
    background: #4B4B4B !important;
}

.dark-layout .select2-container .select2-search__field {
    background-color: #0C0C18;
    color: #fff;
}

.dark-layout .icon-service-urgency {
    color: red !important;
}

.dark-layout .icon-search-building {
    color: #3D3D42 !important;
    background: transparent !important;
}

.dark-layout .scroll-content::-webkit-scrollbar-thumb {
    background-color: #4B4B4B !important;
    border: 2px solid #4B4B4B !important;
}

.dark-layout .scroll-content::-webkit-scrollbar-track {
    background-color: #38393E !important;
}

.dark-layout .actions-buttons-color {
    color: #ADE1FB !important;
}

.dark-layout .icon-comments-info {
    color: #A9F9B5 !important;
}

.dark-layout .icon-comments-warning {
    color: #FFE4B3 !important;
}

.dark-layout .hover-separator {
    border-bottom: 1px solid #3D3D42 !important;
}

.dark-layout .announcements-modal-separator {
    border-bottom: 1px solid #3D3D42 !important;
}

.dark-layout .hr-particularidades {
    color: #3D3D42 !important;
}

.dark-layout .building-information-flats {
    color: #97C4FF !important;
}

.dark-layout .icon-package {
    color: #4B4B4B !important;
}

.dark-layout .contact-announcement-title {
    color: #FFFFFF !important;
}

.dark-layout .cadete-button-exception {
    background-color: rgba(255, 228, 179, 0.15);
    border-color: rgba(255, 228, 179, 1);

    ion-icon {
        color: rgba(255, 158, 67, 1) !important;
    }
}

.dark-layout .cadete-button-ok {
    background-color: rgba(169, 249, 181, 0.15);
    border-color: rgba(169, 249, 181, 1);

    ion-icon {
        color: #A9F9B5 !important;
    }
}

html .dark-layout .content.app-content {
    background: #0d0d18;
}

.dark-layout .cadete-button-ko {
    background-color: rgba(248, 169, 153, 0.15);
    border-color: rgba(248, 169, 153, 1);

    ion-icon {
        color: #F8A999 !important;
    }
}

.dark-layout .doors-list-item-multiple-address-container {
    background-color: #4B4B4B;
}

.dark-layout .doors-list-item-multiple-address {
    background-color: #4B4B4B;
}

.dark-layout .doors-list-item-details-text {
    color: white;
}

.dark-layout #service-type-icon-hibrido {
    content: url('/svg/hibrido-dark.svg');
    zoom: 1.1;
}

