@import "columnsFields.css";

.dark-layout .form-control[type="file"]::-webkit-file-upload-button {
    background-color: #283046;
}

.dark-layout .form-control[type="file"]::file-selector-button {
    background-color: #283046;
}

:root {
    --ColorRed: #ea5455;
    --ColorBlue: #3289da;
    --ColorOrange: #ff9f43;
    --ColorOrangeSec: #FF9F43A6;
    --ColorGreen: #28c76f;
    --ColorGrey: #82868b;
    --ColorDarkBlue: #223a8f;
    --ColorDarkGrey: #4b4b4b;
    --main-color: #6e6b7b;
    --main-bg-color: #f7f7f8;
    --ColorGreyTitle: #5e5873;
    --ColorBlueTitle: #223a8f;
    --ColorBlackTitle: #4b4b4b;
    --ColorBannerBlack: #313037;
    --ColorGreenTitle: #127155;
    --ColorGreenTitleDark: #A9F9B5;
    --ColorBluelabelDark: #ADE1FB;
    --ColorHeaderBuilding: #304FBE;
    --ColorHeaderBuildingNumber: #3B3B70FF;
    --Secundarios-Gris-claro-4: #DEE6EF;
    --Secundarios-Gris-claro-6: #F9FBFD;
    --Secundarios-Gris-claro-5: #F0F4F9;
    --Primarios-Azul-oscuro-Foxsys: #2F4094;
    --ColorGreyIcon: #A7ACCA;
}

.open-sans {
    font-family: "Open Sans";
    color: var(--ColorDarkGrey);
}

#header-cruds-list {
    padding: 0px 40px;
    margin-top: -35px;
    margin-bottom: 25px;
}

#tables-list-cruds {
    margin-top: 5px;
}

.main .container-fluid {
    padding: 0 0px;
}

.waves-effect {
    margin-bottom: 10px !important;
}

.white-letter-to-link {
    color: white;
}

.select2-container--default
.select2-selection--single
.select2-selection__arrow
b {
    border-color: #888 transparent transparent transparent;
    border-style: none !important;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}

.white-field {
    background-color: white;
    color: #7a7a7a;
    border-color: #d8d6de;
}

.dark-field {
    background-color: #1c1c24;
    color: #999da6;
    border-color: #3b4253;
}

.dark-layout .dark-field {
    border: 1px solid #3d3d42;
    border-radius: 5px;
}

.vertical-layout.vertical-menu-modern.menu-collapsed .navbar.fixed-top,
.vertical-layout.vertical-menu-modern.menu-collapsed .navbar.floating-nav {
    right: 20px !important;
    left: auto !important;
}

.dark-layout .title-colors {
    color: #97c4ff !important;
}

.hello_dashboard {
    position: absolute;
    color: #4b4b4b;
    font-size: xx-large !important;
    z-index: 12;
    font-weight: 600;
    padding-top: 25px;
    font-family: "IBM Plex Sans", sans-serif;
}

.main-menu,
.navigation.nav-item {
    background: white;
}

.modal-body.bg-light {
    background-color: var(--main-bg-color-dark-card);
}

.dev-bg {
    background: linear-gradient(
        to bottom right,
        rgba(32, 49, 111, 1) 0%,
        black
    ) !important;
}

a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):hover {
    color: #223a8f;
    text-decoration: none;
}

.modal-title {
    color: #3289da;
}

.testing-bg {
    background: linear-gradient(to bottom right, green, yellow) !important;
}

.pre-prod-bg {
    background: linear-gradient(to bottom right, red, yellow) !important;
}

.collapse-toggle-icon {
    color: #223a8f !important;
}

.background-color-mode {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.background-color-mode-building-tables {
    background-color: white;
    border-radius: 0px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.light-dark-card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.dark-layout .contenedor {
    background-color: #27272b !important;
}

@media (max-width: 400px) {
    .out-in-mobile-d {
        display: none;
    }
}

.select2-container--bootstrap
.select2-selection--multiple
.select2-selection__choice {
    color: #555;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: default;
    float: inherit;
    margin: 5px 0 0 6px;
    padding: 0 6px;
    white-space: normal;
}

tr td {
    max-height: 5px !important;
}

.contact_contactar {
    padding-top: 3%;
    flex-direction: row;
    display: flex;
    width: 93%;
    justify-content: space-between;
    margin: 0 auto;
}

.contGen {
    z-index: 11;
    top: 30px;
    width: 70%;
    padding-top: 2% !important;
    margin-bottom: 2%;
}

.cursor-pointer {
    cursor: pointer;
}

.card-building-card {
    width: 97%;
    padding: 10px 0px 0px 0px;
    margin-left: 0.5rem;
}

/**** HOME ****/

button.card-case-item {
    margin: 0px;
    display: flex;
    width: 100%;
    border: none;
    padding: 0;
    height: 32px;
    border-radius: 8px;
}

.c-skyblue {
    color: #75acff;
}

.card-building-card-label {
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 21px;
    position: absolute;
    left: 40px;
    top: 5px;
    width: 90%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
}

.eplipis-C {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.color-id-table-case {
    color: #223a8f;
}

.c-blue {
    color: #223a8f;
    fill: #223a8f;
}

.c-white {
    color: white;
}

.card-header-background {
    background: linear-gradient(45deg, #223a8f 100%, #304fbe 100%);
}

.open-sans {
    font-family: "Open Sans";
}

.bold {
    font-weight: bold;
}

.cases-table-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 5px;
    color: #4b4b4b;
}

.cases-table-header a {
    margin: 0;
    padding: 10px 10px 0px 10px;
}

.cases-table-header a::before {
    display: inline-block;
    content: "";
    border-radius: 0.375rem;
    height: 8px;
    width: 8px;
    margin-right: 0.5rem;
}

.cases-table-header a.color-blue-cases::before {
    background-color: var(--ColorBlue);
}

.cases-table-header a.color-red-cases::before {
    background-color: var(--ColorRed);
}

.cases-table-header a.color-orange-cases::before {
    background-color: var(--ColorOrange);
}

.cases-table-header a.color-green-cases::before {
    background-color: var(--ColorGreen);
}

.cases-table-header a.color-grey-cases::before {
    background-color: var(--ColorGrey);
}

.security_comments_w {
    margin-top: 10px;
    padding: 5px;
    color: #777483;
    background-color: rgba(34, 58, 143, 0.12);
    width: 95%;
    margin-left: 1.5%;
    border-radius: 5px;
}

.security_comments_d {
    margin-top: 10px;
    padding: 5px;
    color: #777483;
    background-color: #d3dff91f;
    width: 95%;
    margin-left: 1.5%;
    border-radius: 5px;
}

.showOptionsInfo-1-w {
    padding: 8px;
    border-radius: 8px 8px 0 0;
    margin-right: 0.5%;
    margin-left: 9px;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #f7f7f8;
    border-radius: 8px 8px 0px 0px;
}

.showOptionsInfo-2-w {
    padding: 8px;
    border-radius: 8px 8px 0 0;
    margin-right: 0.5%;
    width: 28%;
    margin-left: 3px;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #f7f7f8;
    border-radius: 8px 8px 0px 0px;
}

.showOptionsInfo-3-w {
    padding: 8px;
    width: 32.5%;
    border-radius: 8px 8px 0 0;
    margin-left: 3px;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #f7f7f8;
    border-radius: 8px 8px 0px 0px;
}

.showOptionsInfo-1-d {
    padding: 8px;
    margin-right: 0.5%;
    margin-left: 9px;
    background-color: #27272b !important;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #3d3d42;
    border-radius: 8px 8px 0px 0px;
}

.showOptionsInfo-2-d {
    color: grey;
    padding: 8px;
    margin-right: 0.5%;
    width: 28%;
    margin-left: 3px;
    background-color: #27272b !important;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #3d3d42;
    border-radius: 8px 8px 0px 0px;
}

.showOptionsInfo-3-d {
    color: grey;
    padding: 8px;
    width: 32.5%;
    margin-left: 3px;
    background-color: #27272b !important;
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #3d3d42;
    border-radius: 8px 8px 0px 0px;
}

.card-case-item-colors-w {
    background-color: white;
}

.card-case-item-colors-d {
    background-color: #4b4b4b;
}

c-grey {
    color: #5e5873 !important;
}

/******** DARK Mode ******/


.color-red-contacts {
    color: #EA5455 !important;
    font-weight: bold;
}

.dark-layout tbody .color-red-contacts {
    color: #EA5455 !important;
    font-weight: bold;
}

.color-red {
    color: red !important;
    font-weight: bold;
}

.dark-layout tbody .color-red {
    color: red !important;
    font-weight: bold;
}

.dark-layout .iconContact a {
    color: white !important;
}

.dark-layout .iconContact .filter-button-selected {
    color: white;
}

.dark-layout #show_info_display {
    background-color: #1c1c24 !important;
    color: white !important;
}

.light-layout #show_info_display {
    background-color: rgb(247, 247, 248);
}

table > tbody > tr > td > a,
table > tbody > tr > td > div > a {
    color: #23282c !important;
}

.btn-link,
tbody a .bold {
    font-weight: 600;
    padding-left: 0px;
}

.border-radius-bottom-5 {
    border-radius: 0 0 5px 5px;
}

.create_edit_back_color {
    color: white !important;
    background-color: #223a8f !important;
}

.icon-edit {
    background: rgba(34, 58, 143, 0.12);
    border-radius: 2px;
    color: #223a8f;
    padding: 4px;
    gap: 8px;
    width: 12px;
    height: 12px;
}

.create_edit_back_color_dark {
    color: white !important;
    background-color: #666c7d !important;
}

.container-copy-links-buttons {
    width: 22px;
    height: 25px;
    background-color: rgba(34, 58, 143, 0.12);
    border-radius: 5px;
    margin-left: 5px;
    margin-top: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px;
}

.titleTables {
    color: #223a8f;
    width: auto;
}

.dark-layout .titleTablesDarkMode {
    color: #97c4ff !important;
}

ion-icon.titleTables {
    width: 7% !important;
    padding-right: 0px;
}

.column-state {
    border-radius: 5px;
    padding-inline: 9px;
    padding-top: 1px;
    padding-bottom: 1px;
    width: fit-content;
    font-size: 14px;
}

.add-button-styles {
    border-radius: 25px;
    width: 115px;
    height: 31px;
    padding: 7px;
    border: 1px solid white;
}

.dark-layout .add-button-notification {
    color: #97c4ff !important;
    border: 1px solid #97c4ff !important;
}

.dark-layout .input-dark {
    color: white;
    border: none;
    background-color: #1c1c24;
}

.dark-layout .select-dark {
    background: #1c1c24;
    color: white;
    border: none;
}

.dark-layout .add-button-styles {
    border-radius: 25px;
    width: 115px;
    height: 31px;
    padding: 7px;
    color: #97c4ff !important;
    border: 1px solid #97c4ff !important;
}

.dark-layout .add-button-styles-2 {
    border-radius: 25px;
    width: 115px;
    padding: 7px;
    color: white !important;
    border: 1px solid #97c4ff !important;
    font-weight: bold;
}

.user_notification {
    width: 24px;
    height: 24px;
    background: #ffffff;
    border-radius: 24px;
}

.user_notification ion-icon {
    color: black;
    width: 24px;
    height: 24px;
}

.btn_notfication {
    display: flex;
    flex-direction: row-reverse;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 600;
    margin: 0 auto;
    padding: 8px;
    width: 586px;
}

.filter-button-selected {
    color: #223a8f !important;
}

.font-weight-bold a {
    font-weight: 600;
}

.create_edit_back_color {
    color: white !important;
    background-color: #3289da !important;
}

.icon-edit {
    background: rgba(34, 58, 143, 0.12);
    border-radius: 2px;
    color: #223a8f;
    padding: 4px;
    gap: 8px;
    width: 12px;
    height: 12px;
}

.show_case_notification {
    color: #ffffff !important;
    border: 0;
    background: var(--ColorDarkBlue);
    border-radius: 24px;
    margin-left: 5px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
}

.container-copy-links-buttons {
    width: 22px;
    height: 25px;
    background-color: rgba(34, 58, 143, 0.12);
    border-radius: 5px;
    margin-left: 5px;
    margin-top: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px;
}

.mark_notification {
    text-align: center;
    color: var(--ColorDarkBlue);
    background: transparent;
    border: 1px solid var(--ColorDarkBlue);
    border-radius: 24px;
    text-align: center;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 400;
}

.titleTables {
    color: #223a8f;
    width: auto;
}

.titleTablesCreate {
    color: #223a8f;
    width: auto;
    font-weight: 600;
    font-size: 28px;
}

.icon_notification {
    padding: 4px;
    width: 24px;
    height: 24px;
    background: var(--ColorDarkBlue);
    border-radius: 24px;
    margin-right: 8px;
}

.dark-layout .icon_notification {
    background: #97c4ff;
}

.icon_notification ion-icon {
    color: white;
    width: 16px;
    height: 16px;
}

.font-ibm {
    font-family: "IBM Plex Sans", sans-serif;
}

.f12 {
    font-size: 12px !important;
}

.accordion-notification {
}

.text_notification {
    align-items: center;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: var(--ColorDarkBlue);
    margin-bottom: 0;
    width: 440px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: inherit;
    height: 3rem;
}

.date_notfication {
    position: absolute;
    right: 40px;
    font-size: 10px;
    color: #5e5873;
}

.body_notfication {
    margin: 0 auto;
    padding: 8px;
    width: 570px;
    height: 52px;
    background: #ffffff;
    border-radius: 4px;
}

.dark-layout .body_notfication {
    color: white !important;
}

.noti_type_title {
    color: var(--ColorDarkBlue);
}

.dark-layout .noti_type_title {
    color: white;
}

.cont_notification {
    padding: 0px;
    background: #f7f7f8;
    border-radius: 4px;
}

.img_section_notification:first-child {
    margin: 10px auto 5px auto;
}

.img_section_notification {
    width: 94%;
    margin: 30px auto 5px auto;
    display: flex;
    flex-direction: row;
}

.img_section_notification small {
    line-height: 2;
    margin-left: 5px;
}

.dark-layout .img_section_notification small {
    line-height: 2;
    margin-left: 5px;
    color: white !important;
}

.img_section_notification img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    object-fit: cover;
}

ion-icon.titleTables {
    width: 7% !important;
    padding-right: 0px;
}

.column-state {
    border-radius: 5px;
    padding-inline: 9px;
    padding-top: 1px;
    padding-bottom: 1px;
    width: fit-content;
    font-size: 14px;
}

.navigation > li.open:not(.menu-item-closing) > a {
    background: #f7f7f8 !important;
    box-shadow: #f7f7f8;
    font-weight: 400;
}

.navigation > li > ul li:not(.has-sub) {
    background: #f7f7f8 !important;
    box-shadow: #f7f7f8;
    font-weight: 400;
}

.navigation > li > ul li:not(.has-sub):last-child {
    border-radius: 0 0 4px 4px;
}

.dark-layout
.main-menu.menu-dark
.navigation
> li
> ul
li:not(.has-sub):last-child {
    border-radius: 0 0 4px 4px;
}

.dark-layout .security_comments_w {
    background-color: rgba(211, 223, 249, 0.12) !important;
}

.dark-layout .create_edit_back_color_dark {
    color: black !important;
    background-color: #97c4ff !important;
}

.filter-button-selected {
    color: #223a8f !important;
}

.create_edit_back_color {
    color: white !important;
    background-color: #223a8f !important;
}

.create_edit_back_color_dark {
    color: white !important;
    background-color: #666c7d !important;
}

.container-copy-links-buttons {
    width: 22px;
    height: 25px;
    background-color: rgba(34, 58, 143, 0.12);
    border-radius: 5px;
    margin-left: 5px;
    margin-top: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px;
}

.titleTables {
    color: #223a8f;
    width: auto;
}

ion-icon.titleTables {
    width: 7% !important;
    padding-right: 0px;
}

.column-state {
    border-radius: 5px;
    padding-inline: 9px;
    padding-top: 1px;
    padding-bottom: 1px;
    width: fit-content;
    font-size: 14px;
}

.cases-top-table {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 24px;
    grid-column: 1 / span 3;
}

.cases-top-table-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
}

.cases-top-table-header div {
    display: flex;
    flex-direction: column;
}

.cases-top-table-header div span {
    font-family: "Open Sans";
    font-weight: 600;
    font-size: 14px;
    padding-bottom: 5px;
}

.cases-table-building-info {
    padding: 1.5rem;
    height: 18.25rem;
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    height: 100%;
}

.cases-table-building-info div {
    display: flex;
    justify-content: start;
    width: 100%;
}

.cases-table-building-info-header {
    display: flex;
    flex-direction: column;
}

.cases-table-building-info-header span {
    text-align: left;
    font-family: "Open Sans";
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 12px;
    text-transform: uppercase;
}

.cases-related-table {
    grid-column: 2 / span 2;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
    height: 100%;
}

.conteiner-cases {
    display: grid;
    grid-template-columns: 35rem auto auto;
    gap: 24px;
}

.cases-related-headder {
    display: flex;
    justify-content: space-between;
    padding: 24px 25px;
    width: 100%;
}

.cases-related-headder label {
    color: var(--ColorBlueTitle);
    font-weight: 600;
    font-size: 18px;
}

.cases-comments-table {
    background: white;
}

.cases-description-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
    padding: 50px;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 600;
    min-height: 50rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.cases-description-table label {
    text-transform: uppercase;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
}

.cases-description-table span {
    font-size: 14px;
    font-weight: 700;
}

.cases-description-table p {
    font-size: 14px;
    font-weight: 400;
    max-height: 187px;
    margin-bottom: 1.4rem;
}

.cases-description-img-conteiner {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
}

.cases-description-img-conteiner img {
    object-fit: cover;
    border-radius: 6px;
    width: 120px;
    height: 70px;
}

.cases-description-img-conteiner p {
    display: flex;
    align-items: center;
    margin-left: 15px;
    color: var(--ColorGrey);
    font-weight: 400;
}

.cases-description-createdby {
    display: flex;
}

.cases-description-createdby span {
    font-weight: 400;
    font-size: 14px;
}

.cases-description-createdby p {
    font-weight: 600;
    font-size: 14px;
    margin-left: 5px;
}

.cases-comments-table {
    grid-column: 2 / span 2;
    padding: 24px;
}

.cases-comments-table-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.cases-comments-table-header div {
    display: flex;
    align-items: center;
    color: var(--ColorBlueTitle);
}

.cases-comments-table-header div label {
    margin: 0 5px;
    padding: 0;
    font-weight: 600;
    font-style: normal;
    font-size: 18px;
}

.cases-comments-table-body {
    display: grid;
}

.cases-comments-table-headder textarea {
    padding: 8px 16px 16px;
    width: 50rem;
    height: 84px;
    border: 1px solid #d8d6de;
    border-radius: 8px;
    resize: none;
}

.cases-comments-table-headder {
    grid-row: 1;
}

.cases-comments-table-headder div {
    display: flex;
    flex-direction: column;
    margin-left: 20px;
}

.cases-comments-table-headder select {
    padding: 16px;
    width: 25rem;
    height: 38px;
    border: 1px solid #d8d6de;
    background: #ffffff;
}

.cases-comments-table-headder button {
    background: #b9b9c3;
    border-radius: 5px;
    width: 94px;
    height: 31px;
    color: white;
    font-weight: 600;
    border: none;
    font-size: 11px;
    margin-top: 14px;
}

.cases-comments-table-headder-btn {
    transform: translateX(66%);
}

.cases-comments-table-headder-btn button {
    background: #b9b9c3;
    border-radius: 5px;
    width: 94px;
    height: 31px;
    color: white;
    font-weight: 600;
    border: none;
    font-size: 11px;
    margin-top: 14px;
}

.conteiner-cases hr {
    border: 0px solid #babfc7;
}

.cases-comments-info {
    border: 1px solid #babfc7;
    border-radius: 4px;
    background: white;
    padding: 0;
    margin-bottom: 20px;
}

.cases-comments-info-container {
    height: 31.5rem;
    scroll-behavior: auto;
    overflow-y: auto;
}

.cases-comments-info-header {
    border-bottom: 1px solid #babfc7;
    padding: 16px;
    display: flex;
    justify-content: space-between;
}

.dark-layout label.backstrap-file-label {
    background-color: #1c1c24;
}

.dark-layout .label {
    background: black;
}

.cases-comments-info-text textarea {
    border: 0;
    resize: none;
}

.side-bar- ul li {
    margin-bottom: 8px;
}

.cases-comments-info-header-icon {
    display: flex;
    flex-direction: row;
}

.contain-content {
    contain: content;
}

.contact-icons {
    flex: 0 0 auto;
    width: 51%;
}

.general-info-building {
    --bs-gutter-x: 0;
}

#extra_building_selection {
    color: white;
    background-color: #2e4598;
}

.notification-title {
    font-weight: 700;
    font-size: 14px;
    color: #4b4b4b;
    font-family: "IBM Plex Sans", sans-serif;
}

.border-grey {
    border: 1px solid #ebe9f1;
}

.border-radius-0 {
    border-radius: 0 !important;
}

.clicked {
    background-color: #f7f7f8;
}

.contact-schedule {
    background: rgba(255, 146, 42, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 87%;
    margin-left: 40px;
    padding: 10px 0;
}

.contact-schedule > div > ion-icon {
    color: #ff9f43;
    padding: 10px;
    zoom: 1.5;
}

.contact-schedule-text > span {
    font-size: 14px;
    margin: 0;
    font-family: open sans;
    display: flex;
    text-align: left;
}

.dark-layout #show_times {
    color: white !important;
}

.cases-comments-info-header-icon div {
    width: 26px;
    height: 26px;
    background: rgba(186, 191, 199, 0.12);
    border-radius: 2px;
    display: flex;
    align-items: center;
    margin: 0 5px;
}

.cases-comments-info-header-icon ion-icon {
    width: 100%;
    height: 16px;
}

.cases-comments-table-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.cases-comments-table-filters select {
    width: 147px;
    height: 24px;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #5e5873;
}

.cases-comments-table-filters input {
    width: 147px;
    height: 32px;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #5e5873;
    border: 1px solid #d8d6de;
    border-radius: 8px;
}

.cases-comments-table-filters ion-icon {
    position: absolute;
    right: 10px;
    zoom: 1.2;
    bottom: 6px;
}

.cases-comments-table-filters select {
    width: 200px;
    border: 1px solid #d8d6de;
    border-radius: 8px;
    height: 32px;
}

.button-priority-medium {
    background-color: rgba(255, 146, 42, 0.2);
    color: #ff9f43 !important;
    cursor: pointer;
}

.button-priority-high {
    background-color: rgba(234, 84, 85, 0.12) !important;
    color: #ea5455 !important;
    cursor: pointer;
}

.button-priority-low {
    background-color: rgba(34, 58, 143, 0.12) !important;
    color: #223a8f !important;
    cursor: pointer;
}

.building-contacts-table-icons {
    margin-top: 8px;
    padding: 0;
    margin-left: 5px;
}

.building-contacts-table-icons ion-icon {
    zoom: 1.3;
}

.building-contacts-table-container-icons {
    margin-top: 20px;
}

.cases-buttons {
    grid-column: 2 / span 2;
}

.cases-comments-table-select {
    grid-row: 1;
    justify-self: end;
}

.cases-comments-table-select select {
    width: 20rem;
    height: 30px;
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #5e5873;
    border-radius: 8px;
}

.auto-size-text-area-description {
    resize: none;
    background: none;
    border: none;
}

.dark-layout .auto-size-text-area-description {
    color: white;
    border: none;
}

#crudTable_wrapper #crudTable tr td:first-child,
#crudTable_wrapper #crudTable tr th:first-child {
    display: table-cell;
}

.file-select {
    position: relative;
    display: inline-block;
}

.dark-layout .file-select::before {
    background: #1c1c24 !important;
    color: white;
}

.files-cases {
    position: absolute;
    left: 15px;
    right: 0;
    top: 0;
    bottom: 0;
}

.files-cases label {
    margin-top: 5px;
    font-weight: 600;
}

.dark-layout #click-input {
    background-color: #1c1c24 !important;
    color: white;
    border: 0;
}

#click-input {
    background-color: white !important;
    color: #223a8f;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    content: "Adjuntar archivo";
    width: 220px;
    height: 35px;
    border: 1px solid var(--ColorBlue);
}

.notfication_error {
    position: absolute;
    width: 382px;
    height: 116px;
    background: #ffffff;
    box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
}

.case-msg-category {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 12px;
    gap: 8px;
    width: auto;
    margin-top: 5px;
    min-height: 58px;
    background: rgba(24, 168, 249, 0.12);
    border-radius: 4px;
    flex: none;
    order: 6;
    align-self: stretch;
    flex-grow: 0;
}

.sub-cont-msg {
    display: flex;
    align-items: center;
}

.sub-cont-msg ion-icon {
    flex-shrink: 0;
    margin-right: 10px;
    transform: translateY(-2px);
}

.sub-cont-msg span {
    font-family: "Open Sans";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
}

.table-custom {
    width: auto;
    margin: 0 auto;
}

.table-custom th {
    background-color: #f3f2f7;
}

.title-create .titleTables {
    color: black;
}

.contact_clip_board {
    width: 20px;
    zoom: 1.4;
    margin-top: 3px;
}

.custom-dropdown {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #223a8f;
    border-radius: 50px;
    height: 31px;
    padding: 7px;
}

.custom-dropdown-btn {
    border-radius: 50%;
    background: none;
    border: 1px solid #223a8f;
    width: 31px;
    height: 31px;
    cursor: pointer !important;
}

.custom-dropdown-content {
    display: none;
    position: absolute;
    background-color: #fff;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    z-index: 90;
}

.custom-dropdown-content a {
    color: rgba(0, 0, 0, 0.6);
    padding: 2px 10px;
    text-decoration: none;
    display: block;
    font-size: 13px;
    border-bottom: 1px solid gray;
}

.custom-dropdown-content a:last-child {
    border-bottom: none;
}

.custom-dropdown-content a:hover {
    color: rgba(0, 0, 0, 1);
}

.custom-dropdown-btn:hover + .custom-dropdown-content,
.custom-dropdown-content:hover {
    display: block;
}

.custom-dropdown-content:hover {
    transition-delay: 0s;
}

.custom-dropdown-content a:focus {
    background-color: #ddd;
    outline: none;
}

.custom-dropdown > button {
    background: none;
}

.custom-dropdown-color {
    color: #223a8f;
    font-weight: 500;
}

.announcement-case {
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    cursor: pointer;
}

.announcement-case-default {
    border: 1.5px solid var(--ColorDarkBlue);
}

.announcement-case-active {
    background-color: var(--ColorDarkBlue);
}

.file-preview:first-child {
    margin-top: 40px;
    margin-left: -14px;
}

.file-preview {
    margin-left: -14px;
}

.color-black-table {
    color: #23282c !important;
}

.color-dark-blue {
    color: var(--ColorDarkBlue);
}


.colmuns-list {
    width: 31%;
    overflow-x: auto;
}

@media screen and (max-width: 768px) {
    .colmuns-list {
        width: 100%;
        overflow-x: auto;
    }

    .content-area-wrapper {
        height: 100% !important;
    }

    #dropdown-case-colors {
        margin: 0 !important;
    }

    .submit-btn-save-action {
        padding: 7px;
        font-size: 11px;
    }

    .container {
        min-width: 100%;
    }

}

.access-code-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-building {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    top: 100px;
    right: 15px;
}

.estimated-start-date-building {
    background-color: var(--ColorOrange);
    padding: 8px 12px 8px 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.estimated-start-date-building ion-icon {
    margin-right: 5px;
    color: var(--ColorDarkGrey);
}

.building-state-show button {
    width: 130px;
    height: 37px;
    padding: 0;
    margin: 0 !important;
    color: white;
    font-size: 14px;
}

.building-state-button {
    display: inline-block;
    font-weight: 400;
    line-height: 1;
    color: #6e6b7b;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.786rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.358rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background 0s, border 0s;
}

#target-modal-sechedule {
    color: #223A8F
}

.transferred-calls-cont {
    background-color: white;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
}

.transferred-header {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding-right: 70px;
}

#toggle-transferred {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: -13px;
}


#close-modal-traferred-calls {
    cursor: pointer;
    position: absolute;
    right: 0;
}

#toggle-transferred > span {
    content: "";
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='black' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 1.1rem;
    height: 1rem;
    width: 1rem;
    display: inline-block;
    position: absolute;
    right: 20px;
    top: 14px;
    transform: rotate(270deg);
    transition: all 0.2s ease-out;
    margin-right: 15px;
}

.alert-transferred {
    background: var(--ColorOrange);
    border-radius: 4px;
    padding: 1px 9px 1px 9px;
    height: 20px;
    color: #4B4B4B;
    font-weight: 600;
}

.transferred-calls-cont ion-icon {
    color: var(--ColorBlue) !important;
    zoom: 1.7;
    margin-right: 5px;
}

.transferred-body {
    padding: 5px 15px 0px 30px;
    font-family: "Open Sans";
    color: black;
    display: flex;
}

.transferred-body b {
    font-weight: 700;
}

.transferred-body span {
    font-size: 16px;
}

.transferred-body div {
    margin-top: 5px;
}

.dropdown-building {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    top: 100px;
    right: 15px;
}

.estimated-start-date-building {
    background-color: var(--ColorOrange);
    padding: 8px 12px 8px 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.estimated-start-date-building ion-icon {
    margin-right: 5px;
    color: var(--ColorDarkGrey);
}

.building-state-show button {
    width: 130px;
    height: 37px;
    padding: 0;
    margin: 0 !important;
    color: white;
    font-size: 14px;
}

.building-state-button {
    display: inline-block;
    font-weight: 400;
    line-height: 1;
    color: #6e6b7b;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.786rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.358rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background 0s, border 0s;
}

.buidling-state-span {
    color: #292933;
}


.colmuns-list {
    width: 30%;
}

.column-list-two-columns {
    width: 65%;
}

@media screen and (max-width: 768px) {
    .colmuns-list {
        width: 100%;
    }
}

.dark-layout .contact-temporal-icon-show {
    color: white;
}


.contact-temporal-icon-show {
    text-align: left;
    margin-left: 3rem;
    display: flex;
    align-items: center;
    color: #5E5873;
    font-size: 12px;
    font-family: "Open Sans";
}

.contact-schedule-date {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-family: Open Sans;
    font-style: normal;
}

.contact-schedule-date span b {
    font-weight: 700;
}

.contact-temporal-icon-show {
    text-align: left;
    margin-left: 3rem;
    display: flex;
    align-items: center;
    color: #5E5873;
    font-size: 12px;
    font-family: "Open Sans";
}

.contact-schedule-date {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-family: Open Sans;
    font-style: normal;
}

.contact-schedule-date span b {
    font-weight: 700;
}

.contact-ci-security_word {
    background-color: rgba(34, 58, 143, 0.12);
    color: #48494c;
    font-weight: bold;
    padding: 5px 12px;
    border-radius: 50px;
}

.dark-layout .contact-ci-security_word {
    background-color: #97C4FF;
    color: #1C1C24 !important;
}


.card-authorized-contact {
    background-color: #f7f7f8;
    color: black;
    padding: 15px 8px;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
}

.dark-layout .card-authorized-contact {
    background-color: rgba(0, 0, 0, 0.4);
}

.card-list-authorized-contact {
    background-color: #f7f7f8;
    color: black;
    padding: 15px 8px;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    flex: 1;
}

.dark-layout .card-list-authorized-contact {
    background-color: rgba(0, 0, 0, 0.4);
}

.row-reverse {
    display: flex;
    flex-direction: row-reverse;
}

.text-ellipsis-case {
    display: inline-block;
    max-width: 10ch;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.new-case-building-show {
    padding: 8px;
    border-radius: 50px;
    cursor: pointer;
    text-align: center;
    color: white;
    background: var(--Primarios-Azul-oscuro-Foxsys);
    min-width: 130px;
}

.filter-building {
    font-weight: bold;
    color: #304fbe;
}

.people-access-container {
    border-radius: 12px;
    border: 2px solid #304FBE !important;
    width: 100%;
    background: #F7F8FC;
    max-height: 211px;
}

.accordion-item {
    background: transparent !important;
}

.accordion-button {
    background: transparent !important;
}

.accordion-rules-buttons {
    background: #F0F4F9;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    color: #223A8F !important;
    width: 81px;
    height: 50px;
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.accordion-information-equipments-buttons {
    background: #F0F4F9;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    color: #223A8F !important;
    width: 140px;
    height: 60px;
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.people-rules-container {
    border-radius: 12px;
    border: 2px solid #DEE6EF;
    gap: 16px;
    width: 100%;
    margin-top: 10px;
    overflow: visible;
}

.equipment-container {
    border-radius: 15px;
    border: 2px solid #DEE6EF;
    width: 100%;
    margin-top: 10px;
    overflow: visible;
}

.building-information-container {
    border-radius: 15px;
    border: 2px solid #DEE6EF;
    width: 100%;
    margin-top: 10px;
    max-height: 300px;
}

.cadete-container {
    border-radius: 12px;
    border: 2px solid #304FBE !important;
    width: 100%;
    background: #F7F8FC;
    max-height: 211px;

}

.cadete-container-general {
    border-radius: 12px;
    border: 2px solid #DEE6EF;
    width: 100%;
    margin-top: 10px;
    max-height: 211px;
}

.people-access-container-general {
    border-radius: 12px;
    border: 2px solid #DEE6EF;
    width: 100%;
    margin-top: 10px;
    max-height: 211px;
}

.accordion-header button {
    color: #223A8F !important;
}

.building-announcements-icon {
    background: #FF9F43;
    color: white !important;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
    padding: 11px;
}

.announcement-item {
    display: flex;
    width: 100%;
    align-content: space-between;
    align-items: center;
    border-bottom: 1px solid #F7F7F8;
    margin-right: 10px;
    background: #F9FBFD;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    justify-content: space-between;

}

.announcement-title {
    padding-left: 5px;
    padding-right: 5px;
    font-weight: bold;
    flex-grow: 1;
}

.scroll-content::-webkit-scrollbar {
    -webkit-appearance: none;
}

.scroll-content::-webkit-scrollbar:vertical {
    width: 10px;
}

.scroll-content::-webkit-scrollbar-button:increment, .scroll-content::-webkit-scrollbar-button {
    display: none;
}

.scroll-content::-webkit-scrollbar:horizontal {
    height: 10px;
}

.scroll-content::-webkit-scrollbar-thumb {
    background-color: #D8D6DE !important;
    border-radius: 20px;
    border: 2px solid #D8D6DE !important;
}

.scroll-content::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #F7F7F8 !important;
}

.porter-cleaning-input-search {
    border: 1px solid #C0C8D2;
    outline: none;
    padding: 8px 15px;
    border-radius: 10px;
}

.porter-cleaning-container {
    border-radius: 15px;
    border: 2px solid #DEE6EF;
    width: 100%;
    margin-top: 10px;
    max-height: 450px;
}

.porter-cleaning-item {
    padding: 16px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    background: #F9FBFD;
    text-align: left;
}

.porter-cleaning-item-name {
    color: black;
    gap: 8px;
}

.porter-cleaning-item-phone {
    color: #3289DA;
    text-decoration: underline;
    cursor: pointer;
}

.porter-cleaning-item-phone-text-icon {
    gap: 8px;
}

.porter-cleaning-item-info-text {
    color: #313037;
    gap: 8px
}

.no-data-porter-cleaning {
    padding: 16px;
    border-radius: 10px;
    display: flex;
    background: #F0F4F9;
}

.cadete-button {
    padding: 8px;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    border-radius: 40px;
    gap: 4px;
    width: 80px;
    height: 32px;
    color: #313037;
}

.cadete-button-ok {
    border: 1px solid #238D5A;
    background: rgba(35, 141, 90, 0.15);
}

.cadete-button-exception {
    background: #FF9F4326;
    border: 1px solid #FF9F43;
}

.cadete-button-ko {
    border: 1px solid #B60200;
    background: #B6020026;
}

.audio-valid {
    color: #238D5A;
}

.audio-invalid {
    color: #B60200;
}

.building-flats-apartments {
    color: #304FBE;
    font-weight: bold;
}

#buildingInfoModal {
    background: white;
    padding: 10px;
    width: 461px;
    border-radius: 5px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
}

#peopleAccessInfoModal {
    background: white;
    padding: 10px;
    width: 461px;
    border-radius: 5px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
}

#buildingCardServiceTypeModal {
    background: white;
    padding: 8px 16px;
    width: 461px;
    border-radius: 8px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
    display: none;
    position: absolute;
    z-index: 9999;
    gap: 16px;
    box-shadow: 8px 8px 10px 0px #0000000D;
}

.building-schedule-item-text {
    padding: 8px 0px;
}

#buildingCardInfoModal {
    background: white;
    padding: 10px;
    width: 461px;
    border-radius: 5px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
}

#buildingCardEquipmentModal {
    background: white;
    padding: 10px;
    width: 461px;
    border-radius: 5px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
}

.buildingHoverModal {
    background: white;
    padding: 8px 16px;
    width: 461px;
    border-radius: 8px;
    border: 1px solid #DEE6EF;
    color: var(--ColorBannerBlack);
    display: none;
    position: absolute;
    z-index: 9999;
}

.buildingHoverModal ul {
    margin: 0;
}

.buildingHoverModal ul li {
    margin-bottom: 8px;
}

.buildingHoverModal ul li:last-child {
    margin-bottom: 0;
}

.announcements-modal-separator {
    border-bottom: 1px solid #DEE6EF;
    margin-bottom: 15px;
    margin-top: 5px;
}

.modalHoverContainer {
    gap: 16px;
    maring-top: 20px;
}

.door-available-container {
    display: flex;
    gap: 0.5rem;
    border-radius: 8px;
}

.doors-list {
    flex: 3;
    overflow-y: hidden;
    background-color: #ffffff;
    border-radius: 16px;
}

.doors-list-border {
    border: 2px solid var(--Secundarios-Gris-claro-4);
}

.doors-list-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    gap: 16px;
    background-color: #F9FBFD;
    border-radius: 8px;
    color: var(--ColorBlueTitle);
}

.doors-list-item-multiple-address-container {
    display: flex;
    flex-direction: column;
    padding: 12px;
    background-color: #F9FBFD;
    border-radius: 8px;
    gap: 8px;
    color: var(--ColorBlueTitle);
}

.doors-list-item-multiple-address {
    display: flex;
    align-items: center;
    gap: 16px;
    background-color: #F9FBFD;
    border-radius: 8px;
    color: var(--ColorBlueTitle);
}

.doors-list-item:last-child {
    border-bottom: none;
}

.doors-list-item-tower {
    font-weight: bold;
    font-size: 14px;
    color: #223A8F;
}

.doors-list-item-info {
    flex: 1;
}

.doors-list-item-address {
    font-size: 11px;
    font-weight: bolder;
    line-height: 16px;
}

.doors-list-item-address-text {
    color: #313037;
}

.doors-list-item-details {
    font-size: 11px;
    font-weight: 400;
    line-height: 18px;
}

.doors-list-item-details-text {
    color: #4B4B4B;
}

.doors-list-item-action {
    display: flex;
    font-size: 11px;
    align-items: center;
    flex-direction: column-reverse;
    color: var(--ColorBlueTitle);
}

.doors-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.doors-button-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #F0F4F9;
    border-radius: 8px;
    border: 0px;
    cursor: pointer;
    font-size: 1rem;
    color: #1e3a8a;
}

.doors-button-item:hover {
    background-color: #e3efff;
}

.doors-button-item .icon img {
    width: 20px;
    height: 20px;
}

.direct-ringing {
    background: #EADCFD;
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    letter-spacing: 0;
    text-align: left;
    border-radius: 4px;
    padding: 8px 12px 8px 12px;
    margin: 0 auto 16px auto;
    color: #292933;

    ion-icon {
        color: #292933;
    }
}

#buildingCardProductsModal {
    background: white;
    padding: 8px 16px;
    width: 231px;
    border-radius: 8px;
    border: 1px solid var(--ColorGreyIcon);
    color: var(--ColorBannerBlack);
    display: none;
    position: absolute;
    z-index: 9999;
    padding-left: 20px;
}

#buildingAnnouncementsModal {
    background: white;
    padding: 24px 16px;
    width: 461px;
    border-radius: 8px;
    border: 1px solid #DEE6EF;
    color: var(--ColorBannerBlack);
    display: none;
    position: absolute;
    z-index: 9999;
    padding-left: 20px;
}

.products-icon-color-yes {
    color: #238D5A;
}

.products-icon-color-no {
    color: #CD1528;
}

.total-apartments-and-contacts {
    color: #313037;
}

.modal-package-description-position {
    margin-top: -30px;
}

.container-no-particularidades {
    gap: 8px;
    border-radius: 8px;
    padding: 8px;
    background: #F9FBFD;
    color: #727D8A;
}

.buildingHoverModalTitle {
    font-family: Open Sans;
    font-weight: 600;
    font-size: 16px;
    line-height: 120%;
    letter-spacing: 0px;
}

.cadete-icon-close {
    color: #B60200;
}

.cadete-icon-checkmark {
    color: #238D5A;
}

.porter-cleaning-icon-type {
    align-self: start;
    pointer-events: none;
    min-width: 20px;
    margin-top: 3px;
}

.cadete-icon-help {
    color: #FF9F43;
}

.hovers-text {
    color: #313037;
    font-size: 12px;
    line-height: 18px;
    font-weight: 400;
}

.buildingHoverModal .hovers-text:only-child {
    margin-bottom: 8px;
    display: block;
}

.building-schedule-title {
    color: #304FBE;
    font-family: Open Sans;
    font-weight: 600;
    font-size: 14px;
    line-height: 150%;
    letter-spacing: 0px;
    gap: 8px;
}

.building-schedule-text {
    font-family: Open Sans;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    letter-spacing: 0px;
    padding: 8px 0px;
}

.particularidades-atc-accordion-container {
    padding: 16px;
    border-radius: 8px;
    gap: 16px;
    background: #F9FBFD;
}

.icon-service-urgency {
    color: red !important;
}

#fav-show-image-contact {
    height: 55px;
    width: 55px;
    border-radius: 50%;
    border: 3px solid white;
    object-fit: cover;
}

.icon-comments-info {
    color: #28c76f;
    min-width: 20px;
    align-self: start;
    margin-top: 1px;
}

.icon-comments-warning {
    color: #FF9F43;
    min-width: 20px;
}

.hover-separator {
    border-bottom: 1px solid #F0F4F9;
    margin-bottom: 15px;
    margin-top: 5px;
}

.building-information-flats {
    color: #304FBE;;
}

.contacts-text {
    font-weight: 600;
}

.porter-svg{
    zoom: 0.8;
}

.lbl-no-content-building-tables {
    color: #313037;
}

.truncate {
    white-space: nowrap; /* Evita saltos de línea */
    overflow: hidden; /* Oculta el texto que excede el contenedor */
    text-overflow: ellipsis; /* Agrega los puntos suspensivos */
    max-width: 450px; /* Define el ancho máximo del contenedor */
}

.contact-type-icon-color {
    color: #4B4B4B;
    --ionicon-stroke-width: 48px;
}

#service-type-icon-hibrido {
    content: url('/svg/hibrido.svg');
    zoom: 0.7
}

li.total-apartments-and-contacts {
    list-style-position: outside;
    padding-left: 30px;
    margin-bottom: 0.25rem;
    text-indent: -20px;
    padding-right: 10px;
}

.doors-list-item-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
