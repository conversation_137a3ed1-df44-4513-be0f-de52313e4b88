/*!
 * 
 * Super simple WYSIWYG editor v0.8.20
 * https://summernote.org
 *
 *
 * Copyright 2013- <PERSON> and contributors
 * Summernote may be freely distributed under the MIT license.
 *
 * Date: 2021-10-14T21:15Z
 *
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function() {
return /******/ (() => { // webpackBootstrap
var __webpack_exports__ = {};
(function ($) {
  $.extend($.summernote.lang, {
    'hr-HR': {
      font: {
        bold: 'Podebljano',
        italic: 'Kurziv',
        underline: 'Podvučeno',
        clear: 'Ukloni stilove fonta',
        height: 'Visina linije',
        name: '<PERSON>ont <PERSON>',
        strikethrough: 'Precrtano',
        subscript: 'Subscript',
        superscript: 'Superscript',
        size: 'Veličina fonta'
      },
      image: {
        image: 'Slika',
        insert: 'Ubaci sliku',
        resizeFull: 'Puna veličina',
        resizeHalf: 'Umanji na 50%',
        resizeQuarter: 'Umanji na 25%',
        floatLeft: 'Poravnaj lijevo',
        floatRight: 'Poravnaj desno',
        floatNone: 'Bez poravnanja',
        shapeRounded: 'Shape: Rounded',
        shapeCircle: 'Shape: Circle',
        shapeThumbnail: 'Shape: Thumbnail',
        shapeNone: 'Shape: None',
        dragImageHere: 'Povuci sliku ovdje',
        dropImage: 'Drop image or Text',
        selectFromFiles: 'Izaberi iz datoteke',
        maximumFileSize: 'Maximum file size',
        maximumFileSizeError: 'Maximum file size exceeded.',
        url: 'Adresa slike',
        remove: 'Ukloni sliku',
        original: 'Original'
      },
      video: {
        video: 'Video',
        videoLink: 'Veza na video',
        insert: 'Ubaci video',
        url: 'URL video',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ili Youku)'
      },
      link: {
        link: 'Veza',
        insert: 'Ubaci vezu',
        unlink: 'Ukloni vezu',
        edit: 'Uredi',
        textToDisplay: 'Tekst za prikaz',
        url: 'Internet adresa',
        openInNewWindow: 'Otvori u novom prozoru'
      },
      table: {
        table: 'Tablica',
        addRowAbove: 'Add row above',
        addRowBelow: 'Add row below',
        addColLeft: 'Add column left',
        addColRight: 'Add column right',
        delRow: 'Delete row',
        delCol: 'Delete column',
        delTable: 'Delete table'
      },
      hr: {
        insert: 'Ubaci horizontalnu liniju'
      },
      style: {
        style: 'Stil',
        p: 'pni',
        blockquote: 'Citat',
        pre: 'Kôd',
        h1: 'Naslov 1',
        h2: 'Naslov 2',
        h3: 'Naslov 3',
        h4: 'Naslov 4',
        h5: 'Naslov 5',
        h6: 'Naslov 6'
      },
      lists: {
        unordered: 'Obična lista',
        ordered: 'Numerirana lista'
      },
      options: {
        help: 'Pomoć',
        fullscreen: 'Preko cijelog ekrana',
        codeview: 'Izvorni kôd'
      },
      paragraph: {
        paragraph: 'Paragraf',
        outdent: 'Smanji uvlačenje',
        indent: 'Povećaj uvlačenje',
        left: 'Poravnaj lijevo',
        center: 'Centrirano',
        right: 'Poravnaj desno',
        justify: 'Poravnaj obostrano'
      },
      color: {
        recent: 'Posljednja boja',
        more: 'Više boja',
        background: 'Boja pozadine',
        foreground: 'Boja teksta',
        transparent: 'Prozirna',
        setTransparent: 'Prozirna',
        reset: 'Poništi',
        resetToDefault: 'Podrazumijevana'
      },
      shortcut: {
        shortcuts: 'Prečice s tipkovnice',
        close: 'Zatvori',
        textFormatting: 'Formatiranje teksta',
        action: 'Akcija',
        paragraphFormatting: 'Formatiranje paragrafa',
        documentStyle: 'Stil dokumenta',
        extraKeys: 'Dodatne kombinacije'
      },
      help: {
        'insertParagraph': 'Insert Paragraph',
        'undo': 'Undoes the last command',
        'redo': 'Redoes the last command',
        'tab': 'Tab',
        'untab': 'Untab',
        'bold': 'Set a bold style',
        'italic': 'Set a italic style',
        'underline': 'Set a underline style',
        'strikethrough': 'Set a strikethrough style',
        'removeFormat': 'Clean a style',
        'justifyLeft': 'Set left align',
        'justifyCenter': 'Set center align',
        'justifyRight': 'Set right align',
        'justifyFull': 'Set full align',
        'insertUnorderedList': 'Toggle unordered list',
        'insertOrderedList': 'Toggle ordered list',
        'outdent': 'Outdent on current paragraph',
        'indent': 'Indent on current paragraph',
        'formatPara': 'Change current block\'s format as a paragraph(P tag)',
        'formatH1': 'Change current block\'s format as H1',
        'formatH2': 'Change current block\'s format as H2',
        'formatH3': 'Change current block\'s format as H3',
        'formatH4': 'Change current block\'s format as H4',
        'formatH5': 'Change current block\'s format as H5',
        'formatH6': 'Change current block\'s format as H6',
        'insertHorizontalRule': 'Insert horizontal rule',
        'linkDialog.show': 'Show Link Dialog'
      },
      history: {
        undo: 'Poništi',
        redo: 'Ponovi'
      },
      specialChar: {
        specialChar: 'SPECIAL CHARACTERS',
        select: 'Select Special characters'
      }
    }
  });
})(jQuery);
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=summernote-hr-HR.js.map