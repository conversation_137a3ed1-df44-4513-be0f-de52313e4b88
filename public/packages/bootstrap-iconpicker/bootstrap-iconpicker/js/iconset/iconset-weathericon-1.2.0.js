/*!========================================================================
 * Bootstrap: iconset-weathericon-1.2.0.js by @recktoner
 * https://victor-valencia.github.com/bootstrap-iconpicker
 *
 * Iconset: Weather Icons 1.2.0
 * http://erikflowers.github.io/weather-icons/
 * ========================================================================
 * Copyright 2013-2017 Victor Valencia Rico.
 * Licensed under MIT license.
 * https://github.com/victor-valencia/bootstrap-iconpicker/blob/master/LICENSE
 * ======================================================================== */

;(function($){

    $.iconset_weathericon = {
        iconClass: 'wi',
        iconClassFix: 'wi-',
        icons: [
            '',
            'alien',
            'celsius',
            'cloud',
            'cloud-down',
            'cloud-refresh',
            'cloud-up',
            'cloudy',
            'cloudy-gusts',
            'cloudy-windy',
            'day-cloudy',
            'day-cloudy-gusts',
            'day-cloudy-windy',
            'day-fog',
            'day-hail',
            'day-lightning',
            'day-rain',
            'day-rain-mix',
            'day-rain-wind',
            'day-showers',
            'day-sleet-storm',
            'day-snow',
            'day-snow-thunderstorm',
            'day-snow-wind',
            'day-sprinkle',
            'day-storm-showers',
            'day-sunny',
            'day-sunny-overcast',
            'day-thunderstorm',
            'degrees',
            'down',
            'down-left',
            'dust',
            'fahrenheit',
            'fog',
            'hail',
            'horizon',
            'horizon-alt',
            'hot',
            'hurricane',
            'left',
            'lightning',
            'lunar-eclipse',
            'meteor',
            'moon-full',
            'moon-new',
            'moon-old',
            'moon-waning-crescent',
            'moon-waning-gibbous',
            'moon-waning-quarter',
            'moon-waxing-crescent',
            'moon-waxing-gibbous',
            'moon-waxing-quarter',
            'moon-young',
            'night-alt-cloudy-gusts',
            'night-alt-cloudy-windy',
            'night-alt-hail',
            'night-alt-lightning',
            'night-alt-rain',
            'night-alt-rain-mix',
            'night-alt-rain-wind',
            'night-alt-showers',
            'night-alt-sleet-storm',
            'night-alt-snow',
            'night-alt-snow-thunderstorm',
            'night-alt-snow-wind',
            'night-alt-sprinkle',
            'night-alt-storm-showers',
            'night-alt-thunderstorm',
            'night-clear',
            'night-cloudy',
            'night-cloudy-gusts',
            'night-cloudy-windy',
            'night-fog',
            'night-hail',
            'night-lightning',
            'night-partly-cloudy',
            'night-rain',
            'night-rain-mix',
            'night-rain-wind',
            'night-showers',
            'night-sleet-storm',
            'night-snow',
            'night-snow-thunderstorm',
            'night-snow-wind',
            'night-sprinkle',
            'night-storm-showers',
            'night-thunderstorm',
            'rain',
            'rain-mix',
            'rain-wind',
            'refresh',
            'refresh-alt',
            'right',
            'showers',
            'smog',
            'smoke',
            'snow',
            'snow-wind',
            'snowflake-cold',
            'solar-eclipse',
            'sprinkle',
            'sprinkles',
            'stars',
            'storm-showers',
            'strong-wind',
            'sunrise',
            'sunset',
            'thermometer',
            'thermometer-exterior',
            'thermometer-internal',
            'thunderstorm',
            'tornado',
            'up',
            'up-right',
            'wind-east',
            'wind-north',
            'wind-north-east',
            'wind-north-west',
            'wind-south',
            'wind-south-east',
            'wind-south-west',
            'wind-west',
            'windy'
        ]
    };

})(jQuery);
