{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 13c137d60d3e439c3a2f", "webpack:///./src/utils.js", "webpack:///./src/api.js", "webpack:///./src/button.js", "webpack:///./src/push.js", "webpack:///./~/es6-promise/dist/es6-promise.js", "webpack:///./src/noty.scss", "webpack:///./src/index.js", "webpack:///./~/process/browser.js", "webpack:///(webpack)/buildin/global.js", "webpack:///vertx (ignored)"], "names": ["inArray", "stopPropagation", "generateID", "outerHeight", "addListener", "hasClass", "addClass", "removeClass", "remove", "classList", "visibilityChangeFlow", "createAudioElements", "API", "animationEndEvents", "needle", "haystack", "argStrict", "key", "strict", "hasOwnProperty", "evt", "window", "event", "cancelBubble", "deepExtend", "out", "i", "arguments", "length", "obj", "Array", "isArray", "prefix", "id", "replace", "c", "r", "Math", "random", "v", "toString", "el", "height", "offsetHeight", "style", "getComputedStyle", "parseInt", "marginTop", "marginBottom", "css", "cssPrefixes", "cssProps", "camelCase", "string", "match", "letter", "toUpperCase", "getVendorProp", "name", "document", "body", "capName", "char<PERSON>t", "slice", "vendorName", "getStyleProp", "applyCss", "element", "prop", "value", "properties", "args", "undefined", "events", "cb", "useCapture", "split", "addEventListener", "attachEvent", "list", "indexOf", "oldList", "newList", "className", "substring", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hidden", "visibilityChange", "msHidden", "webkitHidden", "onVisibilityChange", "PageHidden", "handleVisibilityChange", "onBlur", "onFocus", "stopAll", "resumeAll", "setTimeout", "Object", "keys", "Store", "for<PERSON>ach", "options", "visibilityControl", "stop", "resume", "queueRenderAll", "ref", "hasSound", "audioElement", "createElement", "sounds", "sources", "source", "src", "s", "type", "getExtension", "append<PERSON><PERSON><PERSON>", "barDom", "querySelector", "volume", "soundPlayed", "play", "onended", "fileName", "getQueueCounts", "addToQueue", "removeFromQueue", "queueRender", "ghostFix", "build", "hasButtons", "handleModal", "handleModalClose", "queueClose", "dequeueClose", "fire", "openFlow", "closeFlow", "Utils", "DocModalCount", "DocTitleProps", "originalTitle", "count", "changed", "timer", "doc<PERSON><PERSON><PERSON>", "increment", "_update", "decrement", "_clear", "title", "DefaultMaxVisible", "Queues", "global", "maxVisible", "queue", "De<PERSON>ults", "layout", "theme", "text", "timeout", "progressBar", "closeWith", "animation", "open", "close", "force", "killer", "container", "buttons", "callbacks", "beforeShow", "onShow", "afterShow", "onClose", "afterClose", "onClick", "onHover", "onTemplate", "conditions", "titleCount", "modal", "queueName", "max", "closed", "current", "push", "noty", "shift", "show", "ghostID", "ghost", "setAttribute", "insertAdjacentHTML", "outerHTML", "getElementById", "findOrCreateContainer", "markup", "buildButtons", "innerHTML", "dom", "btn", "createModal", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "layoutDom", "layoutID", "progressDom", "transition", "width", "clearTimeout", "closeTimer", "eventName", "listeners", "apply", "closing", "querySelectorAll", "NotyButton", "html", "classes", "attributes", "propertyName", "<PERSON><PERSON>", "worker<PERSON><PERSON>", "subData", "onPermissionGranted", "onPermissionDenied", "onSubscriptionSuccess", "onSubscriptionCancel", "onWorkerError", "onWorkerSuccess", "onWorkerNotSupported", "params", "console", "log", "result", "Notification", "webkitNotifications", "navigator", "mozNotification", "external", "msIsSiteMode", "e", "perm", "permissionLevel", "checkPermission", "permission", "toLowerCase", "subscription", "endpoint", "subscriptionId", "serviceWorker", "controller", "state", "self", "getRegistrations", "then", "registrations", "registration", "unregister", "userVisibleOnly", "getPermissionStatus", "register", "ready", "serviceWorkerRegistration", "pushManager", "subscribe", "<PERSON><PERSON><PERSON>", "token", "getEndpoint", "p256dh", "btoa", "String", "fromCharCode", "Uint8Array", "auth", "catch", "err", "unregisterWorker", "requestPermission", "<PERSON><PERSON>", "showing", "shown", "killable", "promises", "on", "closeAll", "queueCounts", "closeButton", "resolve", "bind", "_t", "ms", "optionsOverride", "amount", "innerHtml"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA,mDAA2C,cAAc;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;;QC5DgBA,O,GAAAA,O;QAoBAC,e,GAAAA,e;QAkCAC,U,GAAAA,U;QAYAC,W,GAAAA,W;QAkEAC,W,GAAAA,W;QAWAC,Q,GAAAA,Q;QAKAC,Q,GAAAA,Q;QAUAC,W,GAAAA,W;QAaAC,M,GAAAA,M;QAMAC,S,GAAAA,S;QAOAC,oB,GAAAA,oB;QA0EAC,mB,GAAAA,mB;;AAtQhB;;IAAYC,G;;;;AAEL,IAAMC,kDAAqB,8EAA3B;;AAEA,SAASb,OAAT,CAAkBc,MAAlB,EAA0BC,QAA1B,EAAoCC,SAApC,EAA+C;AACpD,MAAIC,YAAJ;AACA,MAAIC,SAAS,CAAC,CAACF,SAAf;;AAEA,MAAIE,MAAJ,EAAY;AACV,SAAKD,GAAL,IAAYF,QAAZ,EAAsB;AACpB,UAAIA,SAASI,cAAT,CAAwBF,GAAxB,KAAgCF,SAASE,GAAT,MAAkBH,MAAtD,EAA8D;AAC5D,eAAO,IAAP;AACD;AACF;AACF,GAND,MAMO;AACL,SAAKG,GAAL,IAAYF,QAAZ,EAAsB;AACpB,UAAIA,SAASI,cAAT,CAAwBF,GAAxB,KAAgCF,SAASE,GAAT,MAAkBH,MAAtD,EAA8D;AAC5D,eAAO,IAAP;AACD;AACF;AACF;AACD,SAAO,KAAP;AACD;;AAEM,SAASb,eAAT,CAA0BmB,GAA1B,EAA+B;AACpCA,QAAMA,OAAOC,OAAOC,KAApB;;AAEA,MAAI,OAAOF,IAAInB,eAAX,KAA+B,WAAnC,EAAgD;AAC9CmB,QAAInB,eAAJ;AACD,GAFD,MAEO;AACLmB,QAAIG,YAAJ,GAAmB,IAAnB;AACD;AACF;;AAEM,IAAMC,kCAAa,SAAbA,UAAa,CAAUC,GAAV,EAAe;AACvCA,QAAMA,OAAO,EAAb;;AAEA,OAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIC,UAAUC,MAA9B,EAAsCF,GAAtC,EAA2C;AACzC,QAAIG,MAAMF,UAAUD,CAAV,CAAV;;AAEA,QAAI,CAACG,GAAL,EAAU;;AAEV,SAAK,IAAIZ,GAAT,IAAgBY,GAAhB,EAAqB;AACnB,UAAIA,IAAIV,cAAJ,CAAmBF,GAAnB,CAAJ,EAA6B;AAC3B,YAAIa,MAAMC,OAAN,CAAcF,IAAIZ,GAAJ,CAAd,CAAJ,EAA6B;AAC3BQ,cAAIR,GAAJ,IAAWY,IAAIZ,GAAJ,CAAX;AACD,SAFD,MAEO,IAAI,QAAOY,IAAIZ,GAAJ,CAAP,MAAoB,QAApB,IAAgCY,IAAIZ,GAAJ,MAAa,IAAjD,EAAuD;AAC5DQ,cAAIR,GAAJ,IAAWO,WAAWC,IAAIR,GAAJ,CAAX,EAAqBY,IAAIZ,GAAJ,CAArB,CAAX;AACD,SAFM,MAEA;AACLQ,cAAIR,GAAJ,IAAWY,IAAIZ,GAAJ,CAAX;AACD;AACF;AACF;AACF;;AAED,SAAOQ,GAAP;AACD,CAtBM;;AAwBA,SAASvB,UAAT,GAAkC;AAAA,MAAb8B,MAAa,uEAAJ,EAAI;;AACvC,MAAIC,eAAaD,MAAb,MAAJ;;AAEAC,QAAM,uCAAuCC,OAAvC,CAA+C,OAA/C,EAAwD,UAAUC,CAAV,EAAa;AACzE,QAAIC,IAAIC,KAAKC,MAAL,KAAgB,EAAhB,GAAqB,CAA7B;AACA,QAAIC,IAAIJ,MAAM,GAAN,GAAYC,CAAZ,GAAgBA,IAAI,GAAJ,GAAU,GAAlC;AACA,WAAOG,EAAEC,QAAF,CAAW,EAAX,CAAP;AACD,GAJK,CAAN;;AAMA,SAAOP,EAAP;AACD;;AAEM,SAAS9B,WAAT,CAAsBsC,EAAtB,EAA0B;AAC/B,MAAIC,SAASD,GAAGE,YAAhB;AACA,MAAIC,QAAQvB,OAAOwB,gBAAP,CAAwBJ,EAAxB,CAAZ;;AAEAC,YAAUI,SAASF,MAAMG,SAAf,IAA4BD,SAASF,MAAMI,YAAf,CAAtC;AACA,SAAON,MAAP;AACD;;AAEM,IAAIO,oBAAO,YAAY;AAC5B,MAAIC,cAAc,CAAC,QAAD,EAAW,GAAX,EAAgB,KAAhB,EAAuB,IAAvB,CAAlB;AACA,MAAIC,WAAW,EAAf;;AAEA,WAASC,SAAT,CAAoBC,MAApB,EAA4B;AAC1B,WAAOA,OACJnB,OADI,CACI,OADJ,EACa,KADb,EAEJA,OAFI,CAEI,cAFJ,EAEoB,UAAUoB,KAAV,EAAiBC,MAAjB,EAAyB;AAChD,aAAOA,OAAOC,WAAP,EAAP;AACD,KAJI,CAAP;AAKD;;AAED,WAASC,aAAT,CAAwBC,IAAxB,EAA8B;AAC5B,QAAId,QAAQe,SAASC,IAAT,CAAchB,KAA1B;AACA,QAAIc,QAAQd,KAAZ,EAAmB,OAAOc,IAAP;;AAEnB,QAAIhC,IAAIwB,YAAYtB,MAApB;AACA,QAAIiC,UAAUH,KAAKI,MAAL,CAAY,CAAZ,EAAeN,WAAf,KAA+BE,KAAKK,KAAL,CAAW,CAAX,CAA7C;AACA,QAAIC,mBAAJ;;AAEA,WAAOtC,GAAP,EAAY;AACVsC,mBAAad,YAAYxB,CAAZ,IAAiBmC,OAA9B;AACA,UAAIG,cAAcpB,KAAlB,EAAyB,OAAOoB,UAAP;AAC1B;;AAED,WAAON,IAAP;AACD;;AAED,WAASO,YAAT,CAAuBP,IAAvB,EAA6B;AAC3BA,WAAON,UAAUM,IAAV,CAAP;AACA,WAAOP,SAASO,IAAT,MAAmBP,SAASO,IAAT,IAAiBD,cAAcC,IAAd,CAApC,CAAP;AACD;;AAED,WAASQ,QAAT,CAAmBC,OAAnB,EAA4BC,IAA5B,EAAkCC,KAAlC,EAAyC;AACvCD,WAAOH,aAAaG,IAAb,CAAP;AACAD,YAAQvB,KAAR,CAAcwB,IAAd,IAAsBC,KAAtB;AACD;;AAED,SAAO,UAAUF,OAAV,EAAmBG,UAAnB,EAA+B;AACpC,QAAIC,OAAO5C,SAAX;AACA,QAAIyC,aAAJ;AACA,QAAIC,cAAJ;;AAEA,QAAIE,KAAK3C,MAAL,KAAgB,CAApB,EAAuB;AACrB,WAAKwC,IAAL,IAAaE,UAAb,EAAyB;AACvB,YAAIA,WAAWnD,cAAX,CAA0BiD,IAA1B,CAAJ,EAAqC;AACnCC,kBAAQC,WAAWF,IAAX,CAAR;AACA,cAAIC,UAAUG,SAAV,IAAuBF,WAAWnD,cAAX,CAA0BiD,IAA1B,CAA3B,EAA4D;AAC1DF,qBAASC,OAAT,EAAkBC,IAAlB,EAAwBC,KAAxB;AACD;AACF;AACF;AACF,KATD,MASO;AACLH,eAASC,OAAT,EAAkBI,KAAK,CAAL,CAAlB,EAA2BA,KAAK,CAAL,CAA3B;AACD;AACF,GAjBD;AAkBD,CAxDgB,EAAV;;AA0DA,SAASnE,WAAT,CAAsBqC,EAAtB,EAA0BgC,MAA1B,EAAkCC,EAAlC,EAA0D;AAAA,MAApBC,UAAoB,uEAAP,KAAO;;AAC/DF,WAASA,OAAOG,KAAP,CAAa,GAAb,CAAT;AACA,OAAK,IAAIlD,IAAI,CAAb,EAAgBA,IAAI+C,OAAO7C,MAA3B,EAAmCF,GAAnC,EAAwC;AACtC,QAAIiC,SAASkB,gBAAb,EAA+B;AAC7BpC,SAAGoC,gBAAH,CAAoBJ,OAAO/C,CAAP,CAApB,EAA+BgD,EAA/B,EAAmCC,UAAnC;AACD,KAFD,MAEO,IAAIhB,SAASmB,WAAb,EAA0B;AAC/BrC,SAAGqC,WAAH,CAAe,OAAOL,OAAO/C,CAAP,CAAtB,EAAiCgD,EAAjC;AACD;AACF;AACF;;AAEM,SAASrE,QAAT,CAAmB8D,OAAnB,EAA4BT,IAA5B,EAAkC;AACvC,MAAIqB,OAAO,OAAOZ,OAAP,KAAmB,QAAnB,GAA8BA,OAA9B,GAAwC1D,UAAU0D,OAAV,CAAnD;AACA,SAAOY,KAAKC,OAAL,CAAa,MAAMtB,IAAN,GAAa,GAA1B,KAAkC,CAAzC;AACD;;AAEM,SAASpD,QAAT,CAAmB6D,OAAnB,EAA4BT,IAA5B,EAAkC;AACvC,MAAIuB,UAAUxE,UAAU0D,OAAV,CAAd;AACA,MAAIe,UAAUD,UAAUvB,IAAxB;;AAEA,MAAIrD,SAAS4E,OAAT,EAAkBvB,IAAlB,CAAJ,EAA6B;;AAE7B;AACAS,UAAQgB,SAAR,GAAoBD,QAAQE,SAAR,CAAkB,CAAlB,CAApB;AACD;;AAEM,SAAS7E,WAAT,CAAsB4D,OAAtB,EAA+BT,IAA/B,EAAqC;AAC1C,MAAIuB,UAAUxE,UAAU0D,OAAV,CAAd;AACA,MAAIe,gBAAJ;;AAEA,MAAI,CAAC7E,SAAS8D,OAAT,EAAkBT,IAAlB,CAAL,EAA8B;;AAE9B;AACAwB,YAAUD,QAAQ/C,OAAR,CAAgB,MAAMwB,IAAN,GAAa,GAA7B,EAAkC,GAAlC,CAAV;;AAEA;AACAS,UAAQgB,SAAR,GAAoBD,QAAQE,SAAR,CAAkB,CAAlB,EAAqBF,QAAQtD,MAAR,GAAiB,CAAtC,CAApB;AACD;;AAEM,SAASpB,MAAT,CAAiB2D,OAAjB,EAA0B;AAC/B,MAAIA,QAAQkB,UAAZ,EAAwB;AACtBlB,YAAQkB,UAAR,CAAmBC,WAAnB,CAA+BnB,OAA/B;AACD;AACF;;AAEM,SAAS1D,SAAT,CAAoB0D,OAApB,EAA6B;AAClC,SAAO,CAAC,OAAQA,WAAWA,QAAQgB,SAApB,IAAkC,EAAzC,IAA+C,GAAhD,EAAqDjD,OAArD,CACL,OADK,EAEL,GAFK,CAAP;AAID;;AAEM,SAASxB,oBAAT,GAAiC;AACtC,MAAI6E,eAAJ;AACA,MAAIC,yBAAJ;AACA,MAAI,OAAO7B,SAAS4B,MAAhB,KAA2B,WAA/B,EAA4C;AAC1C;AACAA,aAAS,QAAT;AACAC,uBAAmB,kBAAnB;AACD,GAJD,MAIO,IAAI,OAAO7B,SAAS8B,QAAhB,KAA6B,WAAjC,EAA8C;AACnDF,aAAS,UAAT;AACAC,uBAAmB,oBAAnB;AACD,GAHM,MAGA,IAAI,OAAO7B,SAAS+B,YAAhB,KAAiC,WAArC,EAAkD;AACvDH,aAAS,cAAT;AACAC,uBAAmB,wBAAnB;AACD;;AAED,WAASG,kBAAT,GAA+B;AAC7B/E,QAAIgF,UAAJ,GAAiBjC,SAAS4B,MAAT,CAAjB;AACAM;AACD;;AAED,WAASC,MAAT,GAAmB;AACjBlF,QAAIgF,UAAJ,GAAiB,IAAjB;AACAC;AACD;;AAED,WAASE,OAAT,GAAoB;AAClBnF,QAAIgF,UAAJ,GAAiB,KAAjB;AACAC;AACD;;AAED,WAASA,sBAAT,GAAmC;AACjC,QAAIjF,IAAIgF,UAAR,EAAoBI,UAApB,KACKC;AACN;;AAED,WAASD,OAAT,GAAoB;AAClBE,eACE,YAAY;AACVC,aAAOC,IAAP,CAAYxF,IAAIyF,KAAhB,EAAuBC,OAAvB,CAA+B,cAAM;AACnC,YAAI1F,IAAIyF,KAAJ,CAAUlF,cAAV,CAAyBc,EAAzB,CAAJ,EAAkC;AAChC,cAAIrB,IAAIyF,KAAJ,CAAUpE,EAAV,EAAcsE,OAAd,CAAsBC,iBAA1B,EAA6C;AAC3C5F,gBAAIyF,KAAJ,CAAUpE,EAAV,EAAcwE,IAAd;AACD;AACF;AACF,OAND;AAOD,KATH,EAUE,GAVF;AAYD;;AAED,WAASR,SAAT,GAAsB;AACpBC,eACE,YAAY;AACVC,aAAOC,IAAP,CAAYxF,IAAIyF,KAAhB,EAAuBC,OAAvB,CAA+B,cAAM;AACnC,YAAI1F,IAAIyF,KAAJ,CAAUlF,cAAV,CAAyBc,EAAzB,CAAJ,EAAkC;AAChC,cAAIrB,IAAIyF,KAAJ,CAAUpE,EAAV,EAAcsE,OAAd,CAAsBC,iBAA1B,EAA6C;AAC3C5F,gBAAIyF,KAAJ,CAAUpE,EAAV,EAAcyE,MAAd;AACD;AACF;AACF,OAND;AAOA9F,UAAI+F,cAAJ;AACD,KAVH,EAWE,GAXF;AAaD;;AAED,MAAInB,gBAAJ,EAAsB;AACpBpF,gBAAYuD,QAAZ,EAAsB6B,gBAAtB,EAAwCG,kBAAxC;AACD;;AAEDvF,cAAYiB,MAAZ,EAAoB,MAApB,EAA4ByE,MAA5B;AACA1F,cAAYiB,MAAZ,EAAoB,OAApB,EAA6B0E,OAA7B;AACD;;AAEM,SAASpF,mBAAT,CAA8BiG,GAA9B,EAAmC;AACxC,MAAIA,IAAIC,QAAR,EAAkB;AAChB,QAAMC,eAAenD,SAASoD,aAAT,CAAuB,OAAvB,CAArB;;AAEAH,QAAIL,OAAJ,CAAYS,MAAZ,CAAmBC,OAAnB,CAA2BX,OAA3B,CAAmC,aAAK;AACtC,UAAMY,SAASvD,SAASoD,aAAT,CAAuB,QAAvB,CAAf;AACAG,aAAOC,GAAP,GAAaC,CAAb;AACAF,aAAOG,IAAP,cAAuBC,aAAaF,CAAb,CAAvB;AACAN,mBAAaS,WAAb,CAAyBL,MAAzB;AACD,KALD;;AAOA,QAAIN,IAAIY,MAAR,EAAgB;AACdZ,UAAIY,MAAJ,CAAWD,WAAX,CAAuBT,YAAvB;AACD,KAFD,MAEO;AACLnD,eAAS8D,aAAT,CAAuB,MAAvB,EAA+BF,WAA/B,CAA2CT,YAA3C;AACD;;AAEDA,iBAAaY,MAAb,GAAsBd,IAAIL,OAAJ,CAAYS,MAAZ,CAAmBU,MAAzC;;AAEA,QAAI,CAACd,IAAIe,WAAT,EAAsB;AACpBb,mBAAac,IAAb;AACAhB,UAAIe,WAAJ,GAAkB,IAAlB;AACD;;AAEDb,iBAAae,OAAb,GAAuB,YAAY;AACjCrH,aAAOsG,YAAP;AACD,KAFD;AAGD;AACF;;AAED,SAASQ,YAAT,CAAuBQ,QAAvB,EAAiC;AAC/B,SAAOA,SAASxE,KAAT,CAAe,YAAf,EAA6B,CAA7B,CAAP;AACD,C;;;;;;;;;;;;;QC5LeyE,c,GAAAA,c;QAqBAC,U,GAAAA,U;QAYAC,e,GAAAA,e;QAgBAC,W,GAAAA,W;QAWAvB,c,GAAAA,c;QAUAwB,Q,GAAAA,Q;QAsBAC,K,GAAAA,K;QAqBAC,U,GAAAA,U;QA6BAC,W,GAAAA,W;QAcAC,gB,GAAAA,gB;QA4DAC,U,GAAAA,U;QAwBAC,Y,GAAAA,Y;QAmBAC,I,GAAAA,I;QAcAC,Q,GAAAA,Q;QAiBAC,S,GAAAA,S;;AA5YhB;;IAAYC,K;;;;AAEL,IAAIjD,kCAAa,KAAjB;AACA,IAAIkD,wCAAgB,CAApB;;AAEP,IAAMC,gBAAgB;AACpBC,iBAAe,IADK;AAEpBC,SAAO,CAFa;AAGpBC,WAAS,KAHW;AAIpBC,SAAO,CAAC;AAJY,CAAtB;;AAOO,IAAMC,8BAAW;AACtBC,aAAW,qBAAM;AACfN,kBAAcE,KAAd;;AAEAG,aAASE,OAAT;AACD,GALqB;;AAOtBC,aAAW,qBAAM;AACfR,kBAAcE,KAAd;;AAEA,QAAIF,cAAcE,KAAd,IAAuB,CAA3B,EAA8B;AAC5BG,eAASI,MAAT;AACA;AACD;;AAEDJ,aAASE,OAAT;AACD,GAhBqB;;AAkBtBA,WAAS,mBAAM;AACb,QAAIG,QAAQ9F,SAAS8F,KAArB;;AAEA,QAAI,CAACV,cAAcG,OAAnB,EAA4B;AAC1BH,oBAAcC,aAAd,GAA8BS,KAA9B;AACA9F,eAAS8F,KAAT,SAAqBV,cAAcE,KAAnC,UAA6CQ,KAA7C;AACAV,oBAAcG,OAAd,GAAwB,IAAxB;AACD,KAJD,MAIO;AACLvF,eAAS8F,KAAT,SAAqBV,cAAcE,KAAnC,UAA6CF,cAAcC,aAA3D;AACD;AACF,GA5BqB;;AA8BtBQ,UAAQ,kBAAM;AACZ,QAAIT,cAAcG,OAAlB,EAA2B;AACzBH,oBAAcE,KAAd,GAAsB,CAAtB;AACAtF,eAAS8F,KAAT,GAAiBV,cAAcC,aAA/B;AACAD,oBAAcG,OAAd,GAAwB,KAAxB;AACD;AACF;AApCqB,CAAjB;;AAuCA,IAAMQ,gDAAoB,CAA1B;;AAEA,IAAMC,0BAAS;AACpBC,UAAQ;AACNC,gBAAYH,iBADN;AAENI,WAAO;AAFD;AADY,CAAf;;AAOA,IAAMzD,wBAAQ,EAAd;;AAEA,IAAI0D,8BAAW;AACpB1C,QAAM,OADc;AAEpB2C,UAAQ,UAFY;AAGpBC,SAAO,MAHa;AAIpBC,QAAM,EAJc;AAKpBC,WAAS,KALW;AAMpBC,eAAa,IANO;AAOpBC,aAAW,CAAC,OAAD,CAPS;AAQpBC,aAAW;AACTC,UAAM,mBADG;AAETC,WAAO;AAFE,GARS;AAYpBvI,MAAI,KAZgB;AAapBwI,SAAO,KAba;AAcpBC,UAAQ,KAdY;AAepBZ,SAAO,QAfa;AAgBpBa,aAAW,KAhBS;AAiBpBC,WAAS,EAjBW;AAkBpBC,aAAW;AACTC,gBAAY,IADH;AAETC,YAAQ,IAFC;AAGTC,eAAW,IAHF;AAITC,aAAS,IAJA;AAKTC,gBAAY,IALH;AAMTC,aAAS,IANA;AAOTC,aAAS,IAPA;AAQTC,gBAAY;AARH,GAlBS;AA4BpBrE,UAAQ;AACNC,aAAS,EADH;AAENS,YAAQ,CAFF;AAGN4D,gBAAY;AAHN,GA5BY;AAiCpBC,cAAY;AACVD,gBAAY;AADF,GAjCQ;AAoCpBE,SAAO,KApCa;AAqCpBhF,qBAAmB;;AAGrB;;;;AAxCsB,CAAf,CA4CA,SAASuB,cAAT,GAA+C;AAAA,MAAtB0D,SAAsB,uEAAV,QAAU;;AACpD,MAAIxC,QAAQ,CAAZ;AACA,MAAIyC,MAAMhC,iBAAV;;AAEA,MAAIC,OAAOxI,cAAP,CAAsBsK,SAAtB,CAAJ,EAAsC;AACpCC,UAAM/B,OAAO8B,SAAP,EAAkB5B,UAAxB;AACA1D,WAAOC,IAAP,CAAYC,KAAZ,EAAmBC,OAAnB,CAA2B,aAAK;AAC9B,UAAID,MAAM3E,CAAN,EAAS6E,OAAT,CAAiBuD,KAAjB,KAA2B2B,SAA3B,IAAwC,CAACpF,MAAM3E,CAAN,EAASiK,MAAtD,EAA8D1C;AAC/D,KAFD;AAGD;;AAED,SAAO;AACL2C,aAAS3C,KADJ;AAELY,gBAAY6B;AAFP,GAAP;AAID;;AAED;;;;AAIO,SAAS1D,UAAT,CAAqBpB,GAArB,EAA0B;AAC/B,MAAI,CAAC+C,OAAOxI,cAAP,CAAsByF,IAAIL,OAAJ,CAAYuD,KAAlC,CAAL,EAA+C;AAC7CH,WAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,IAA4B,EAACD,YAAYH,iBAAb,EAAgCI,OAAO,EAAvC,EAA5B;AACD;;AAEDH,SAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,EAA0BA,KAA1B,CAAgC+B,IAAhC,CAAqCjF,GAArC;AACD;;AAED;;;;AAIO,SAASqB,eAAT,CAA0BrB,GAA1B,EAA+B;AACpC,MAAI+C,OAAOxI,cAAP,CAAsByF,IAAIL,OAAJ,CAAYuD,KAAlC,CAAJ,EAA8C;AAC5C,QAAMA,QAAQ,EAAd;AACA3D,WAAOC,IAAP,CAAYuD,OAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,EAA0BA,KAAtC,EAA6CxD,OAA7C,CAAqD,aAAK;AACxD,UAAIqD,OAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,EAA0BA,KAA1B,CAAgCpI,CAAhC,EAAmCO,EAAnC,KAA0C2E,IAAI3E,EAAlD,EAAsD;AACpD6H,cAAM+B,IAAN,CAAWlC,OAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,EAA0BA,KAA1B,CAAgCpI,CAAhC,CAAX;AACD;AACF,KAJD;AAKAiI,WAAO/C,IAAIL,OAAJ,CAAYuD,KAAnB,EAA0BA,KAA1B,GAAkCA,KAAlC;AACD;AACF;;AAED;;;;AAIO,SAAS5B,WAAT,GAA4C;AAAA,MAAtBuD,SAAsB,uEAAV,QAAU;;AACjD,MAAI9B,OAAOxI,cAAP,CAAsBsK,SAAtB,CAAJ,EAAsC;AACpC,QAAMK,OAAOnC,OAAO8B,SAAP,EAAkB3B,KAAlB,CAAwBiC,KAAxB,EAAb;;AAEA,QAAID,IAAJ,EAAUA,KAAKE,IAAL;AACX;AACF;;AAED;;;AAGO,SAASrF,cAAT,GAA2B;AAChCR,SAAOC,IAAP,CAAYuD,MAAZ,EAAoBrD,OAApB,CAA4B,qBAAa;AACvC4B,gBAAYuD,SAAZ;AACD,GAFD;AAGD;;AAED;;;;AAIO,SAAStD,QAAT,CAAmBvB,GAAnB,EAAwB;AAC7B,MAAMqF,UAAUpD,MAAM3I,UAAN,CAAiB,OAAjB,CAAhB;AACA,MAAIgM,QAAQvI,SAASoD,aAAT,CAAuB,KAAvB,CAAZ;AACAmF,QAAMC,YAAN,CAAmB,IAAnB,EAAyBF,OAAzB;AACApD,QAAM5F,GAAN,CAAUiJ,KAAV,EAAiB;AACfxJ,YAAQmG,MAAM1I,WAAN,CAAkByG,IAAIY,MAAtB,IAAgC;AADzB,GAAjB;;AAIAZ,MAAIY,MAAJ,CAAW4E,kBAAX,CAA8B,UAA9B,EAA0CF,MAAMG,SAAhD;;AAEAxD,QAAMrI,MAAN,CAAaoG,IAAIY,MAAjB;AACA0E,UAAQvI,SAAS2I,cAAT,CAAwBL,OAAxB,CAAR;AACApD,QAAMvI,QAAN,CAAe4L,KAAf,EAAsB,yBAAtB;AACArD,QAAMzI,WAAN,CAAkB8L,KAAlB,EAAyBrD,MAAMhI,kBAA/B,EAAmD,YAAM;AACvDgI,UAAMrI,MAAN,CAAa0L,KAAb;AACD,GAFD;AAGD;;AAED;;;;AAIO,SAAS9D,KAAT,CAAgBxB,GAAhB,EAAqB;AAC1B2F,wBAAsB3F,GAAtB;;AAEA,MAAM4F,qCAAmC5F,IAAIL,OAAJ,CAAY2D,IAA/C,cAA4DuC,aAAa7F,GAAb,CAA5D,yCAAN;;AAEAA,MAAIY,MAAJ,GAAa7D,SAASoD,aAAT,CAAuB,KAAvB,CAAb;AACAH,MAAIY,MAAJ,CAAW2E,YAAX,CAAwB,IAAxB,EAA8BvF,IAAI3E,EAAlC;AACA4G,QAAMvI,QAAN,CACEsG,IAAIY,MADN,2BAEyBZ,IAAIL,OAAJ,CAAYc,IAFrC,qBAEyDT,IAAIL,OAAJ,CAAY0D,KAFrE;;AAKArD,MAAIY,MAAJ,CAAWkF,SAAX,GAAuBF,MAAvB;;AAEA9D,OAAK9B,GAAL,EAAU,YAAV;AACD;;AAED;;;;AAIO,SAASyB,UAAT,CAAqBzB,GAArB,EAA0B;AAC/B,SAAO,CAAC,EAAEA,IAAIL,OAAJ,CAAYqE,OAAZ,IAAuBzE,OAAOC,IAAP,CAAYQ,IAAIL,OAAJ,CAAYqE,OAAxB,EAAiChJ,MAA1D,CAAR;AACD;;AAED;;;;AAIA,SAAS6K,YAAT,CAAuB7F,GAAvB,EAA4B;AAC1B,MAAIyB,WAAWzB,GAAX,CAAJ,EAAqB;AACnB,QAAIgE,UAAUjH,SAASoD,aAAT,CAAuB,KAAvB,CAAd;AACA8B,UAAMvI,QAAN,CAAesK,OAAf,EAAwB,cAAxB;;AAEAzE,WAAOC,IAAP,CAAYQ,IAAIL,OAAJ,CAAYqE,OAAxB,EAAiCtE,OAAjC,CAAyC,eAAO;AAC9CsE,cAAQrD,WAAR,CAAoBX,IAAIL,OAAJ,CAAYqE,OAAZ,CAAoB3J,GAApB,EAAyB0L,GAA7C;AACD,KAFD;;AAIA/F,QAAIL,OAAJ,CAAYqE,OAAZ,CAAoBtE,OAApB,CAA4B,eAAO;AACjCsE,cAAQrD,WAAR,CAAoBqF,IAAID,GAAxB;AACD,KAFD;AAGA,WAAO/B,QAAQyB,SAAf;AACD;AACD,SAAO,EAAP;AACD;;AAED;;;;AAIO,SAAS/D,WAAT,CAAsB1B,GAAtB,EAA2B;AAChC,MAAIA,IAAIL,OAAJ,CAAYiF,KAAhB,EAAuB;AACrB,QAAI1C,kBAAkB,CAAtB,EAAyB;AACvB+D,kBAAYjG,GAAZ;AACD;;AAED,YA3POkC,aA2PP;AACD;AACF;;AAED;;;;AAIO,SAASP,gBAAT,CAA2B3B,GAA3B,EAAgC;AACrC,MAAIA,IAAIL,OAAJ,CAAYiF,KAAZ,IAAqB1C,gBAAgB,CAAzC,EAA4C;AAC1C,YArQOA,aAqQP;;AAEA,QAAIA,iBAAiB,CAArB,EAAwB;AACtB,UAAM0C,QAAQ7H,SAAS8D,aAAT,CAAuB,aAAvB,CAAd;;AAEA,UAAI+D,KAAJ,EAAW;AACT3C,cAAMtI,WAAN,CAAkBiL,KAAlB,EAAyB,iBAAzB;AACA3C,cAAMvI,QAAN,CAAekL,KAAf,EAAsB,kBAAtB;AACA3C,cAAMzI,WAAN,CAAkBoL,KAAlB,EAAyB3C,MAAMhI,kBAA/B,EAAmD,YAAM;AACvDgI,gBAAMrI,MAAN,CAAagL,KAAb;AACD,SAFD;AAGD;AACF;AACF;AACF;;AAED;;;AAGA,SAASqB,WAAT,GAAwB;AACtB,MAAMjJ,OAAOD,SAAS8D,aAAT,CAAuB,MAAvB,CAAb;AACA,MAAM+D,QAAQ7H,SAASoD,aAAT,CAAuB,KAAvB,CAAd;AACA8B,QAAMvI,QAAN,CAAekL,KAAf,EAAsB,YAAtB;AACA5H,OAAKkJ,YAAL,CAAkBtB,KAAlB,EAAyB5H,KAAKmJ,UAA9B;AACAlE,QAAMvI,QAAN,CAAekL,KAAf,EAAsB,iBAAtB;;AAEA3C,QAAMzI,WAAN,CAAkBoL,KAAlB,EAAyB3C,MAAMhI,kBAA/B,EAAmD,YAAM;AACvDgI,UAAMtI,WAAN,CAAkBiL,KAAlB,EAAyB,iBAAzB;AACD,GAFD;AAGD;;AAED;;;;AAIA,SAASe,qBAAT,CAAgC3F,GAAhC,EAAqC;AACnC,MAAIA,IAAIL,OAAJ,CAAYoE,SAAhB,EAA2B;AACzB/D,QAAIoG,SAAJ,GAAgBrJ,SAAS8D,aAAT,CAAuBb,IAAIL,OAAJ,CAAYoE,SAAnC,CAAhB;AACA;AACD;;AAED,MAAMsC,6BAA2BrG,IAAIL,OAAJ,CAAYyD,MAA7C;AACApD,MAAIoG,SAAJ,GAAgBrJ,SAAS8D,aAAT,UAA8BwF,QAA9B,CAAhB;;AAEA,MAAI,CAACrG,IAAIoG,SAAT,EAAoB;AAClBpG,QAAIoG,SAAJ,GAAgBrJ,SAASoD,aAAT,CAAuB,KAAvB,CAAhB;AACAH,QAAIoG,SAAJ,CAAcb,YAAd,CAA2B,IAA3B,EAAiCc,QAAjC;AACArG,QAAIoG,SAAJ,CAAcb,YAAd,CAA2B,MAA3B,EAAmC,OAAnC;AACAvF,QAAIoG,SAAJ,CAAcb,YAAd,CAA2B,WAA3B,EAAwC,QAAxC;AACAtD,UAAMvI,QAAN,CAAesG,IAAIoG,SAAnB,EAA8B,aAA9B;AACArJ,aAAS8D,aAAT,CAAuB,MAAvB,EAA+BF,WAA/B,CAA2CX,IAAIoG,SAA/C;AACD;AACF;;AAED;;;;AAIO,SAASxE,UAAT,CAAqB5B,GAArB,EAA0B;AAC/B,MAAIA,IAAIL,OAAJ,CAAY4D,OAAhB,EAAyB;AACvB,QAAIvD,IAAIL,OAAJ,CAAY6D,WAAZ,IAA2BxD,IAAIsG,WAAnC,EAAgD;AAC9CrE,YAAM5F,GAAN,CAAU2D,IAAIsG,WAAd,EAA2B;AACzBC,+BAAqBvG,IAAIL,OAAJ,CAAY4D,OAAjC,cADyB;AAEzBiD,eAAO;AAFkB,OAA3B;AAID;;AAEDC,iBAAazG,IAAI0G,UAAjB;;AAEA1G,QAAI0G,UAAJ,GAAiBpH,WACf,YAAM;AACJU,UAAI4D,KAAJ;AACD,KAHc,EAIf5D,IAAIL,OAAJ,CAAY4D,OAJG,CAAjB;AAMD;AACF;;AAED;;;;AAIO,SAAS1B,YAAT,CAAuB7B,GAAvB,EAA4B;AACjC,MAAIA,IAAIL,OAAJ,CAAY4D,OAAZ,IAAuBvD,IAAI0G,UAA/B,EAA2C;AACzCD,iBAAazG,IAAI0G,UAAjB;AACA1G,QAAI0G,UAAJ,GAAiB,CAAC,CAAlB;;AAEA,QAAI1G,IAAIL,OAAJ,CAAY6D,WAAZ,IAA2BxD,IAAIsG,WAAnC,EAAgD;AAC9CrE,YAAM5F,GAAN,CAAU2D,IAAIsG,WAAd,EAA2B;AACzBC,oBAAY,kBADa;AAEzBC,eAAO;AAFkB,OAA3B;AAID;AACF;AACF;;AAED;;;;;AAKO,SAAS1E,IAAT,CAAe9B,GAAf,EAAoB2G,SAApB,EAA+B;AACpC,MAAI3G,IAAI4G,SAAJ,CAAcrM,cAAd,CAA6BoM,SAA7B,CAAJ,EAA6C;AAC3C3G,QAAI4G,SAAJ,CAAcD,SAAd,EAAyBjH,OAAzB,CAAiC,cAAM;AACrC,UAAI,OAAO5B,EAAP,KAAc,UAAlB,EAA8B;AAC5BA,WAAG+I,KAAH,CAAS7G,GAAT;AACD;AACF,KAJD;AAKD;AACF;;AAED;;;;AAIO,SAAS+B,QAAT,CAAmB/B,GAAnB,EAAwB;AAC7B8B,OAAK9B,GAAL,EAAU,WAAV;AACA4B,aAAW5B,GAAX;;AAEAiC,QAAMzI,WAAN,CAAkBwG,IAAIY,MAAtB,EAA8B,YAA9B,EAA4C,YAAM;AAChDiB,iBAAa7B,GAAb;AACD,GAFD;;AAIAiC,QAAMzI,WAAN,CAAkBwG,IAAIY,MAAtB,EAA8B,YAA9B,EAA4C,YAAM;AAChDgB,eAAW5B,GAAX;AACD,GAFD;AAGD;;AAED;;;;AAIO,SAASgC,SAAT,CAAoBhC,GAApB,EAAyB;AAC9B,SAAOP,MAAMO,IAAI3E,EAAV,CAAP;AACA2E,MAAI8G,OAAJ,GAAc,KAAd;AACAhF,OAAK9B,GAAL,EAAU,YAAV;;AAEAiC,QAAMrI,MAAN,CAAaoG,IAAIY,MAAjB;;AAEA,MACEZ,IAAIoG,SAAJ,CAAcW,gBAAd,CAA+B,WAA/B,EAA4C/L,MAA5C,KAAuD,CAAvD,IACA,CAACgF,IAAIL,OAAJ,CAAYoE,SAFf,EAGE;AACA9B,UAAMrI,MAAN,CAAaoG,IAAIoG,SAAjB;AACD;;AAED,MACEnE,MAAM7I,OAAN,CAAc,YAAd,EAA4B4G,IAAIL,OAAJ,CAAYgF,UAAZ,CAAuBD,UAAnD,KACAzC,MAAM7I,OAAN,CAAc,WAAd,EAA2B4G,IAAIL,OAAJ,CAAYgF,UAAZ,CAAuBD,UAAlD,CAFF,EAGE;AACAlC,aAASG,SAAT;AACD;;AAEDrB,cAAYtB,IAAIL,OAAJ,CAAYuD,KAAxB;AACD,C;;;;;;;;;;;;;;AClaD;;IAAYjB,K;;;;;;IAEC+E,U,WAAAA,U,GACX,oBAAaC,IAAb,EAAmBC,OAAnB,EAA4BpJ,EAA5B,EAAiD;AAAA;;AAAA,MAAjBqJ,UAAiB,uEAAJ,EAAI;;AAAA;;AAC/C,OAAKpB,GAAL,GAAWhJ,SAASoD,aAAT,CAAuB,QAAvB,CAAX;AACA,OAAK4F,GAAL,CAASD,SAAT,GAAqBmB,IAArB;AACA,OAAK5L,EAAL,GAAW8L,WAAW9L,EAAX,GAAgB8L,WAAW9L,EAAX,IAAiB4G,MAAM3I,UAAN,CAAiB,QAAjB,CAA5C;AACA,OAAKwE,EAAL,GAAUA,EAAV;AACAyB,SAAOC,IAAP,CAAY2H,UAAZ,EAAwBzH,OAAxB,CAAgC,wBAAgB;AAC9C,UAAKqG,GAAL,CAASR,YAAT,CAAsB6B,YAAtB,EAAoCD,WAAWC,YAAX,CAApC;AACD,GAFD;AAGAnF,QAAMvI,QAAN,CAAe,KAAKqM,GAApB,EAAyBmB,WAAW,UAApC;;AAEA,SAAO,IAAP;AACD,C;;;;;;;;;;;;;;;;;ICdUG,I,WAAAA,I;AACX,kBAAgD;AAAA,QAAnCC,UAAmC,uEAAtB,oBAAsB;;AAAA;;AAC9C,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKD,UAAL,GAAkBA,UAAlB;AACA,SAAKV,SAAL,GAAiB;AACfY,2BAAqB,EADN;AAEfC,0BAAoB,EAFL;AAGfC,6BAAuB,EAHR;AAIfC,4BAAsB,EAJP;AAKfC,qBAAe,EALA;AAMfC,uBAAiB,EANF;AAOfC,4BAAsB;AAPP,KAAjB;AASA,WAAO,IAAP;AACD;;AAED;;;;;;;;;uBAKInB,S,EAA0B;AAAA,UAAf7I,EAAe,uEAAV,YAAM,CAAE,CAAE;;AAC5B,UAAI,OAAOA,EAAP,KAAc,UAAd,IAA4B,KAAK8I,SAAL,CAAerM,cAAf,CAA8BoM,SAA9B,CAAhC,EAA0E;AACxE,aAAKC,SAAL,CAAeD,SAAf,EAA0B1B,IAA1B,CAA+BnH,EAA/B;AACD;;AAED,aAAO,IAAP;AACD;;;yBAEK6I,S,EAAwB;AAAA;;AAAA,UAAboB,MAAa,uEAAJ,EAAI;;AAC5B,UAAI,KAAKnB,SAAL,CAAerM,cAAf,CAA8BoM,SAA9B,CAAJ,EAA8C;AAC5C,aAAKC,SAAL,CAAeD,SAAf,EAA0BjH,OAA1B,CAAkC,cAAM;AACtC,cAAI,OAAO5B,EAAP,KAAc,UAAlB,EAA8B;AAC5BA,eAAG+I,KAAH,QAAekB,MAAf;AACD;AACF,SAJD;AAKD;AACF;;;6BAES;AACRC,cAAQC,GAAR,CAAY,qBAAZ;AACD;;AAED;;;;;;kCAGe;AACb,UAAIC,SAAS,KAAb;;AAEA,UAAI;AACFA,iBAASzN,OAAO0N,YAAP,IACP1N,OAAO2N,mBADA,IAEPC,UAAUC,eAFH,IAGN7N,OAAO8N,QAAP,IAAmB9N,OAAO8N,QAAP,CAAgBC,YAAhB,OAAmC5K,SAHzD;AAID,OALD,CAKE,OAAO6K,CAAP,EAAU,CAAE;;AAEd,aAAOP,MAAP;AACD;;AAED;;;;;;0CAGuB;AACrB,UAAIQ,OAAO,SAAX;;AAEA,UAAIjO,OAAO0N,YAAP,IAAuB1N,OAAO0N,YAAP,CAAoBQ,eAA/C,EAAgE;AAC9DD,eAAOjO,OAAO0N,YAAP,CAAoBQ,eAA3B;AACD,OAFD,MAEO,IACLlO,OAAO2N,mBAAP,IAA8B3N,OAAO2N,mBAAP,CAA2BQ,eADpD,EAEL;AACA,gBAAQnO,OAAO2N,mBAAP,CAA2BQ,eAA3B,EAAR;AACE,eAAK,CAAL;AACEF,mBAAO,SAAP;AACA;AACF,eAAK,CAAL;AACEA,mBAAO,SAAP;AACA;AACF;AACEA,mBAAO,QAAP;AARJ;AAUD,OAbM,MAaA,IAAIjO,OAAO0N,YAAP,IAAuB1N,OAAO0N,YAAP,CAAoBU,UAA/C,EAA2D;AAChEH,eAAOjO,OAAO0N,YAAP,CAAoBU,UAA3B;AACD,OAFM,MAEA,IAAIR,UAAUC,eAAd,EAA+B;AACpCI,eAAO,SAAP;AACD,OAFM,MAEA,IACLjO,OAAO8N,QAAP,IAAmB9N,OAAO8N,QAAP,CAAgBC,YAAhB,OAAmC5K,SADjD,EAEL;AACA8K,eAAOjO,OAAO8N,QAAP,CAAgBC,YAAhB,KAAiC,SAAjC,GAA6C,SAApD;AACD;;AAED,aAAOE,KAAK9M,QAAL,GAAgBkN,WAAhB,EAAP;AACD;;AAED;;;;;;gCAGaC,Y,EAAc;AACzB,UAAIC,WAAWD,aAAaC,QAA5B;AACA,UAAMC,iBAAiBF,aAAaE,cAApC;;AAEA;AACA,UAAIA,kBAAkBD,SAAS5K,OAAT,CAAiB6K,cAAjB,MAAqC,CAAC,CAA5D,EAA+D;AAC7DD,oBAAY,MAAMC,cAAlB;AACD;;AAED,aAAOD,QAAP;AACD;;AAED;;;;;;qCAGkB;AAChB,UAAI;AACF,eAAOX,UAAUa,aAAV,CAAwBC,UAAxB,CAAmCC,KAAnC,KAA6C,WAApD;AACD,OAFD,CAEE,OAAOX,CAAP,EAAU;AACV,eAAO,KAAP;AACD;AACF;;AAED;;;;;;uCAGoB;AAClB,UAAMY,OAAO,IAAb;AACA,UAAI,mBAAmBhB,SAAvB,EAAkC;AAChCA,kBAAUa,aAAV,CAAwBI,gBAAxB,GAA2CC,IAA3C,CAAgD,UAAUC,aAAV,EAAyB;AAAA;AAAA;AAAA;;AAAA;AACvE,iCAAyBA,aAAzB,8HAAwC;AAAA,kBAA/BC,YAA+B;;AACtCA,2BAAaC,UAAb;AACAL,mBAAKvH,IAAL,CAAU,sBAAV;AACD;AAJsE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKxE,SALD;AAMD;AACF;;AAED;;;;;;0CAG6C;AAAA;;AAAA,UAAxB6H,eAAwB,uEAAN,IAAM;;AAC3C,UAAMN,OAAO,IAAb;AACA,UAAMrE,UAAU,KAAK4E,mBAAL,EAAhB;AACA,UAAM9L,KAAK,SAALA,EAAK,SAAU;AACnB,YAAIoK,WAAW,SAAf,EAA0B;AACxB,iBAAKpG,IAAL,CAAU,qBAAV;;AAEA,cAAI,mBAAmBuG,SAAvB,EAAkC;AAChCA,sBAAUa,aAAV,CAAwBW,QAAxB,CAAiC,OAAKvC,UAAtC,EAAkDiC,IAAlD,CAAuD,YAAY;AACjElB,wBAAUa,aAAV,CAAwBY,KAAxB,CAA8BP,IAA9B,CACE,UAAUQ,yBAAV,EAAqC;AACnCV,qBAAKvH,IAAL,CAAU,iBAAV;AACAiI,0CAA0BC,WAA1B,CACGC,SADH,CACa;AACTN,mCAAiBA;AADR,iBADb,EAIGJ,IAJH,CAIQ,UAAUR,YAAV,EAAwB;AAC5B,sBAAM1O,MAAM0O,aAAamB,MAAb,CAAoB,QAApB,CAAZ;AACA,sBAAMC,QAAQpB,aAAamB,MAAb,CAAoB,MAApB,CAAd;;AAEAb,uBAAK9B,OAAL,GAAe;AACbyB,8BAAUK,KAAKe,WAAL,CAAiBrB,YAAjB,CADG;AAEbsB,4BAAQhQ,MACJI,OAAO6P,IAAP,CACEC,OAAOC,YAAP,CAAoB3D,KAApB,CAA0B,IAA1B,EAAgC,IAAI4D,UAAJ,CAAepQ,GAAf,CAAhC,CADF,CADI,GAIJ,IANS;AAObqQ,0BAAMP,QACF1P,OAAO6P,IAAP,CACEC,OAAOC,YAAP,CAAoB3D,KAApB,CACE,IADF,EAEE,IAAI4D,UAAJ,CAAeN,KAAf,CAFF,CADF,CADE,GAOF;AAdS,mBAAf;;AAiBAd,uBAAKvH,IAAL,CAAU,uBAAV,EAAmC,CAACuH,KAAK9B,OAAN,CAAnC;AACD,iBA1BH,EA2BGoD,KA3BH,CA2BS,UAAUC,GAAV,EAAe;AACpBvB,uBAAKvH,IAAL,CAAU,eAAV,EAA2B,CAAC8I,GAAD,CAA3B;AACD,iBA7BH;AA8BD,eAjCH;AAmCD,aApCD;AAqCD,WAtCD,MAsCO;AACLvB,iBAAKvH,IAAL,CAAU,sBAAV;AACD;AACF,SA5CD,MA4CO,IAAIoG,WAAW,QAAf,EAAyB;AAC9B,iBAAKpG,IAAL,CAAU,oBAAV;AACA,iBAAK+I,gBAAL;AACD;AACF,OAjDD;;AAmDA,UAAI7F,YAAY,SAAhB,EAA2B;AACzB,YAAIvK,OAAO0N,YAAP,IAAuB1N,OAAO0N,YAAP,CAAoB2C,iBAA/C,EAAkE;AAChErQ,iBAAO0N,YAAP,CAAoB2C,iBAApB,CAAsChN,EAAtC;AACD,SAFD,MAEO,IACLrD,OAAO2N,mBAAP,IAA8B3N,OAAO2N,mBAAP,CAA2BQ,eADpD,EAEL;AACAnO,iBAAO2N,mBAAP,CAA2B0C,iBAA3B,CAA6ChN,EAA7C;AACD;AACF,OARD,MAQO;AACLA,WAAGkH,OAAH;AACD;AACF;;;;;;;;;;uDC1MH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC,qBAAqB;;AAEtB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,iFAAiF;;AAEjF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,0BAA0B,sBAAsB;;AAEhD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,IAAI;AACd;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,wBAAwB;AACzC;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,6CAA6C;AAC9D;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU,MAAM;AAChB;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA,qBAAqB,YAAY;AACjC;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,IAAI;AACd;AACA,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;AACA,UAAU,SAAS;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA;AACA,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA,YAAY,SAAS;AACrB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,CAAC;;AAED;;;;;;;;ACpoCA,yC;;;;;;;;;;;;;qjBCAA;;AAEA;;AACA;;;;AACA;;IAAY/C,K;;AACZ;;IAAYjI,G;;AACZ;;AACA;;;;;;;;IAEqB+Q,I;AACnB;;;;AAIA,kBAA2B;AAAA,QAAdpL,OAAc,uEAAJ,EAAI;;AAAA;;AACzB,SAAKA,OAAL,GAAesC,MAAMrH,UAAN,CAAiB,EAAjB,EAAqBZ,IAAImJ,QAAzB,EAAmCxD,OAAnC,CAAf;;AAEA,QAAI3F,IAAIyF,KAAJ,CAAU,KAAKE,OAAL,CAAatE,EAAvB,CAAJ,EAAgC;AAC9B,aAAOrB,IAAIyF,KAAJ,CAAU,KAAKE,OAAL,CAAatE,EAAvB,CAAP;AACD;;AAED,SAAKA,EAAL,GAAU,KAAKsE,OAAL,CAAatE,EAAb,IAAmB4G,MAAM3I,UAAN,CAAiB,KAAjB,CAA7B;AACA,SAAKoN,UAAL,GAAkB,CAAC,CAAnB;AACA,SAAK9F,MAAL,GAAc,IAAd;AACA,SAAKwF,SAAL,GAAiB,IAAjB;AACA,SAAKE,WAAL,GAAmB,IAAnB;AACA,SAAK0E,OAAL,GAAe,KAAf;AACA,SAAKC,KAAL,GAAa,KAAb;AACA,SAAKlG,MAAL,GAAc,KAAd;AACA,SAAK+B,OAAL,GAAe,KAAf;AACA,SAAKoE,QAAL,GAAgB,KAAKvL,OAAL,CAAa4D,OAAb,IAAwB,KAAK5D,OAAL,CAAa8D,SAAb,CAAuBzI,MAAvB,GAAgC,CAAxE;AACA,SAAKiF,QAAL,GAAgB,KAAKN,OAAL,CAAaS,MAAb,CAAoBC,OAApB,CAA4BrF,MAA5B,GAAqC,CAArD;AACA,SAAK+F,WAAL,GAAmB,KAAnB;AACA,SAAK6F,SAAL,GAAiB;AACf1C,kBAAY,EADG;AAEfC,cAAQ,EAFO;AAGfC,iBAAW,EAHI;AAIfC,eAAS,EAJM;AAKfC,kBAAY,EALG;AAMfC,eAAS,EANM;AAOfC,eAAS,EAPM;AAQfC,kBAAY;AARG,KAAjB;AAUA,SAAK0G,QAAL,GAAgB;AACd/F,YAAM,IADQ;AAEdxB,aAAO;AAFO,KAAhB;AAIA,SAAKwH,EAAL,CAAQ,YAAR,EAAsB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBC,UAA7C;AACA,SAAKkH,EAAL,CAAQ,QAAR,EAAkB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBE,MAAzC;AACA,SAAKiH,EAAL,CAAQ,WAAR,EAAqB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBG,SAA5C;AACA,SAAKgH,EAAL,CAAQ,SAAR,EAAmB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBI,OAA1C;AACA,SAAK+G,EAAL,CAAQ,YAAR,EAAsB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBK,UAA7C;AACA,SAAK8G,EAAL,CAAQ,SAAR,EAAmB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBM,OAA1C;AACA,SAAK6G,EAAL,CAAQ,SAAR,EAAmB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBO,OAA1C;AACA,SAAK4G,EAAL,CAAQ,YAAR,EAAsB,KAAKzL,OAAL,CAAasE,SAAb,CAAuBQ,UAA7C;;AAEA,WAAO,IAAP;AACD;;AAED;;;;;;;;;uBAKIkC,S,EAA0B;AAAA,UAAf7I,EAAe,uEAAV,YAAM,CAAE,CAAE;;AAC5B,UAAI,OAAOA,EAAP,KAAc,UAAd,IAA4B,KAAK8I,SAAL,CAAerM,cAAf,CAA8BoM,SAA9B,CAAhC,EAA0E;AACxE,aAAKC,SAAL,CAAeD,SAAf,EAA0B1B,IAA1B,CAA+BnH,EAA/B;AACD;;AAED,aAAO,IAAP;AACD;;AAED;;;;;;2BAGQ;AAAA;;AACN,UAAI,KAAKkN,OAAL,IAAgB,KAAKC,KAAzB,EAAgC;AAC9B,eAAO,IAAP,CAD8B,CAClB;AACb;;AAED,UAAI,KAAKtL,OAAL,CAAamE,MAAb,KAAwB,IAA5B,EAAkC;AAChCiH,aAAKM,QAAL;AACD,OAFD,MAEO,IAAI,OAAO,KAAK1L,OAAL,CAAamE,MAApB,KAA+B,QAAnC,EAA6C;AAClDiH,aAAKM,QAAL,CAAc,KAAK1L,OAAL,CAAamE,MAA3B;AACD;;AAED,UAAIwH,cAActR,IAAImH,cAAJ,CAAmB,KAAKxB,OAAL,CAAauD,KAAhC,CAAlB;;AAEA,UACEoI,YAAYtG,OAAZ,IAAuBsG,YAAYrI,UAAnC,IACCjJ,IAAIgF,UAAJ,IAAkB,KAAKW,OAAL,CAAaC,iBAFlC,EAGE;AACA5F,YAAIoH,UAAJ,CAAe,IAAf;;AAEA,YACEpH,IAAIgF,UAAJ,IACA,KAAKiB,QADL,IAEAgC,MAAM7I,OAAN,CAAc,WAAd,EAA2B,KAAKuG,OAAL,CAAaS,MAAb,CAAoBsE,UAA/C,CAHF,EAIE;AACAzC,gBAAMlI,mBAAN,CAA0B,IAA1B;AACD;;AAED,YACEC,IAAIgF,UAAJ,IACAiD,MAAM7I,OAAN,CAAc,WAAd,EAA2B,KAAKuG,OAAL,CAAagF,UAAb,CAAwBD,UAAnD,CAFF,EAGE;AACA1K,cAAIwI,QAAJ,CAAaC,SAAb;AACD;;AAED,eAAO,IAAP;AACD;;AAEDzI,UAAIyF,KAAJ,CAAU,KAAKpE,EAAf,IAAqB,IAArB;;AAEArB,UAAI8H,IAAJ,CAAS,IAAT,EAAe,YAAf;;AAEA,WAAKkJ,OAAL,GAAe,IAAf;;AAEA,UAAI,KAAKlE,OAAT,EAAkB;AAChB,aAAKkE,OAAL,GAAe,KAAf;AACA,eAAO,IAAP;AACD;;AAEDhR,UAAIwH,KAAJ,CAAU,IAAV;AACAxH,UAAI0H,WAAJ,CAAgB,IAAhB;;AAEA,UAAI,KAAK/B,OAAL,CAAakE,KAAjB,EAAwB;AACtB,aAAKuC,SAAL,CAAeF,YAAf,CAA4B,KAAKtF,MAAjC,EAAyC,KAAKwF,SAAL,CAAeD,UAAxD;AACD,OAFD,MAEO;AACL,aAAKC,SAAL,CAAezF,WAAf,CAA2B,KAAKC,MAAhC;AACD;;AAED,UACE,KAAKX,QAAL,IACA,CAAC,KAAKc,WADN,IAEAkB,MAAM7I,OAAN,CAAc,YAAd,EAA4B,KAAKuG,OAAL,CAAaS,MAAb,CAAoBsE,UAAhD,CAHF,EAIE;AACAzC,cAAMlI,mBAAN,CAA0B,IAA1B;AACD;;AAED,UAAIkI,MAAM7I,OAAN,CAAc,YAAd,EAA4B,KAAKuG,OAAL,CAAagF,UAAb,CAAwBD,UAApD,CAAJ,EAAqE;AACnE1K,YAAIwI,QAAJ,CAAaC,SAAb;AACD;;AAED,WAAKwI,KAAL,GAAa,IAAb;AACA,WAAKlG,MAAL,GAAc,KAAd;;AAEA;AACA,UAAI/K,IAAIyH,UAAJ,CAAe,IAAf,CAAJ,EAA0B;AACxBlC,eAAOC,IAAP,CAAY,KAAKG,OAAL,CAAaqE,OAAzB,EAAkCtE,OAAlC,CAA0C,eAAO;AAC/C,cAAMsG,MAAM,MAAKpF,MAAL,CAAYC,aAAZ,OACN,MAAKlB,OAAL,CAAaqE,OAAb,CAAqB3J,GAArB,EAA0BgB,EADpB,CAAZ;AAGA4G,gBAAMzI,WAAN,CAAkBwM,GAAlB,EAAuB,OAAvB,EAAgC,aAAK;AACnC/D,kBAAM5I,eAAN,CAAsBoP,CAAtB;AACA,kBAAK9I,OAAL,CAAaqE,OAAb,CAAqB3J,GAArB,EAA0ByD,EAA1B;AACD,WAHD;AAID,SARD;AASD;;AAED,WAAKwI,WAAL,GAAmB,KAAK1F,MAAL,CAAYC,aAAZ,CAA0B,mBAA1B,CAAnB;;AAEA,UAAIoB,MAAM7I,OAAN,CAAc,OAAd,EAAuB,KAAKuG,OAAL,CAAa8D,SAApC,CAAJ,EAAoD;AAClDxB,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,uBAA5B;AACAqB,cAAMzI,WAAN,CACE,KAAKoH,MADP,EAEE,OAFF,EAGE,aAAK;AACHqB,gBAAM5I,eAAN,CAAsBoP,CAAtB;AACAzO,cAAI8H,IAAJ,QAAe,SAAf;AACA,gBAAK8B,KAAL;AACD,SAPH,EAQE,KARF;AAUD;;AAED3B,YAAMzI,WAAN,CACE,KAAKoH,MADP,EAEE,YAFF,EAGE,YAAM;AACJ5G,YAAI8H,IAAJ,QAAe,SAAf;AACD,OALH,EAME,KANF;;AASA,UAAI,KAAKnC,OAAL,CAAa4D,OAAjB,EAA0BtB,MAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,kBAA5B;AAC1B,UAAI,KAAKjB,OAAL,CAAa6D,WAAjB,EAA8B;AAC5BvB,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,sBAA5B;AACD;;AAED,UAAIqB,MAAM7I,OAAN,CAAc,QAAd,EAAwB,KAAKuG,OAAL,CAAa8D,SAArC,CAAJ,EAAqD;AACnDxB,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,wBAA5B;;AAEA,YAAM2K,cAAcxO,SAASoD,aAAT,CAAuB,KAAvB,CAApB;AACA8B,cAAMvI,QAAN,CAAe6R,WAAf,EAA4B,mBAA5B;AACAA,oBAAYzF,SAAZ,GAAwB,GAAxB;AACA,aAAKlF,MAAL,CAAYD,WAAZ,CAAwB4K,WAAxB;;AAEAtJ,cAAMzI,WAAN,CACE+R,WADF,EAEE,OAFF,EAGE,aAAK;AACHtJ,gBAAM5I,eAAN,CAAsBoP,CAAtB;AACA,gBAAK7E,KAAL;AACD,SANH,EAOE,KAPF;AASD;;AAED5J,UAAI8H,IAAJ,CAAS,IAAT,EAAe,QAAf;;AAEA,UAAI,KAAKnC,OAAL,CAAa+D,SAAb,CAAuBC,IAAvB,KAAgC,IAApC,EAA0C;AACxC,aAAKwH,QAAL,CAAc/F,IAAd,GAAqB,yBAAY,mBAAW;AAC1CoG;AACD,SAFoB,CAArB;AAGD,OAJD,MAIO,IAAI,OAAO,KAAK7L,OAAL,CAAa+D,SAAb,CAAuBC,IAA9B,KAAuC,UAA3C,EAAuD;AAC5D,aAAKwH,QAAL,CAAc/F,IAAd,GAAqB,yBAAY,KAAKzF,OAAL,CAAa+D,SAAb,CAAuBC,IAAvB,CAA4B8H,IAA5B,CAAiC,IAAjC,CAAZ,CAArB;AACD,OAFM,MAEA;AACLxJ,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,KAAKjB,OAAL,CAAa+D,SAAb,CAAuBC,IAAnD;AACA,aAAKwH,QAAL,CAAc/F,IAAd,GAAqB,yBAAY,mBAAW;AAC1CnD,gBAAMzI,WAAN,CAAkB,MAAKoH,MAAvB,EAA+BqB,MAAMhI,kBAArC,EAAyD,YAAM;AAC7DgI,kBAAMtI,WAAN,CAAkB,MAAKiH,MAAvB,EAA+B,MAAKjB,OAAL,CAAa+D,SAAb,CAAuBC,IAAtD;AACA6H;AACD,WAHD;AAID,SALoB,CAArB;AAMD;;AAED,WAAKL,QAAL,CAAc/F,IAAd,CAAmBmE,IAAnB,CAAwB,YAAM;AAC5B,YAAMmC,UAAN;AACApM,mBACE,YAAM;AACJtF,cAAI+H,QAAJ,CAAa2J,EAAb;AACD,SAHH,EAIE,GAJF;AAMD,OARD;;AAUA,aAAO,IAAP;AACD;;AAED;;;;;;2BAGQ;AACN1R,UAAI6H,YAAJ,CAAiB,IAAjB;AACA,aAAO,IAAP;AACD;;AAED;;;;;;6BAGU;AACR7H,UAAI4H,UAAJ,CAAe,IAAf;AACA,aAAO,IAAP;AACD;;AAED;;;;;;;;;;;;;;;;;gBAIY+J,E,EAAI;AACd,WAAK9L,IAAL;AACA,WAAKF,OAAL,CAAa4D,OAAb,GAAuBoI,EAAvB;;AAEA,UAAI,KAAK/K,MAAT,EAAiB;AACf,YAAI,KAAKjB,OAAL,CAAa4D,OAAjB,EAA0B;AACxBtB,gBAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,kBAA5B;AACD,SAFD,MAEO;AACLqB,gBAAMtI,WAAN,CAAkB,KAAKiH,MAAvB,EAA+B,kBAA/B;AACD;;AAED,YAAM8K,KAAK,IAAX;AACApM,mBACE,YAAY;AACV;AACAoM,aAAG5L,MAAH;AACD,SAJH,EAKE,GALF;AAOD;;AAED,aAAO,IAAP;AACD,K;;AAED;;;;;;;;4BAKSmH,I,EAA+B;AAAA,UAAzB2E,eAAyB,uEAAP,KAAO;;AACtC,UAAI,KAAKhL,MAAT,EAAiB;AACf,aAAKA,MAAL,CAAYC,aAAZ,CAA0B,YAA1B,EAAwCiF,SAAxC,GAAoDmB,IAApD;AACD;;AAED,UAAI2E,eAAJ,EAAqB,KAAKjM,OAAL,CAAa2D,IAAb,GAAoB2D,IAApB;;AAErB,aAAO,IAAP;AACD;;AAED;;;;;;;;4BAKSxG,I,EAA+B;AAAA;;AAAA,UAAzBmL,eAAyB,uEAAP,KAAO;;AACtC,UAAI,KAAKhL,MAAT,EAAiB;AACf,YAAI/G,YAAYoI,MAAMpI,SAAN,CAAgB,KAAK+G,MAArB,EAA6B5C,KAA7B,CAAmC,GAAnC,CAAhB;;AAEAnE,kBAAU6F,OAAV,CAAkB,aAAK;AACrB,cAAInE,EAAEiD,SAAF,CAAY,CAAZ,EAAe,EAAf,MAAuB,aAA3B,EAA0C;AACxCyD,kBAAMtI,WAAN,CAAkB,OAAKiH,MAAvB,EAA+BrF,CAA/B;AACD;AACF,SAJD;;AAMA0G,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,kBAA0CH,IAA1C;AACD;;AAED,UAAImL,eAAJ,EAAqB,KAAKjM,OAAL,CAAac,IAAb,GAAoBA,IAApB;;AAErB,aAAO,IAAP;AACD;;AAED;;;;;;;;6BAKU4C,K,EAAgC;AAAA;;AAAA,UAAzBuI,eAAyB,uEAAP,KAAO;;AACxC,UAAI,KAAKhL,MAAT,EAAiB;AACf,YAAI/G,YAAYoI,MAAMpI,SAAN,CAAgB,KAAK+G,MAArB,EAA6B5C,KAA7B,CAAmC,GAAnC,CAAhB;;AAEAnE,kBAAU6F,OAAV,CAAkB,aAAK;AACrB,cAAInE,EAAEiD,SAAF,CAAY,CAAZ,EAAe,EAAf,MAAuB,cAA3B,EAA2C;AACzCyD,kBAAMtI,WAAN,CAAkB,OAAKiH,MAAvB,EAA+BrF,CAA/B;AACD;AACF,SAJD;;AAMA0G,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,mBAA2CyC,KAA3C;AACD;;AAED,UAAIuI,eAAJ,EAAqB,KAAKjM,OAAL,CAAa0D,KAAb,GAAqBA,KAArB;;AAErB,aAAO,IAAP;AACD;;AAED;;;;;;4BAGS;AAAA;;AACP,UAAI,KAAK0B,MAAT,EAAiB,OAAO,IAAP;;AAEjB,UAAI,CAAC,KAAKkG,KAAV,EAAiB;AACf;AACAjR,YAAIqH,eAAJ,CAAoB,IAApB;AACA,eAAO,IAAP;AACD;;AAEDrH,UAAI8H,IAAJ,CAAS,IAAT,EAAe,SAAf;;AAEA,WAAKgF,OAAL,GAAe,IAAf;;AAEA,UAAI,KAAKnH,OAAL,CAAa+D,SAAb,CAAuBE,KAAvB,KAAiC,IAAjC,IAAyC,KAAKjE,OAAL,CAAa+D,SAAb,CAAuBE,KAAvB,KAAiC,KAA9E,EAAqF;AACnF,aAAKuH,QAAL,CAAcvH,KAAd,GAAsB,yBAAY,mBAAW;AAC3C4H;AACD,SAFqB,CAAtB;AAGD,OAJD,MAIO,IAAI,OAAO,KAAK7L,OAAL,CAAa+D,SAAb,CAAuBE,KAA9B,KAAwC,UAA5C,EAAwD;AAC7D,aAAKuH,QAAL,CAAcvH,KAAd,GAAsB,yBACpB,KAAKjE,OAAL,CAAa+D,SAAb,CAAuBE,KAAvB,CAA6B6H,IAA7B,CAAkC,IAAlC,CADoB,CAAtB;AAGD,OAJM,MAIA;AACLxJ,cAAMvI,QAAN,CAAe,KAAKkH,MAApB,EAA4B,KAAKjB,OAAL,CAAa+D,SAAb,CAAuBE,KAAnD;AACA,aAAKuH,QAAL,CAAcvH,KAAd,GAAsB,yBAAY,mBAAW;AAC3C3B,gBAAMzI,WAAN,CAAkB,OAAKoH,MAAvB,EAA+BqB,MAAMhI,kBAArC,EAAyD,YAAM;AAC7D,gBAAI,OAAK0F,OAAL,CAAakE,KAAjB,EAAwB;AACtB5B,oBAAMrI,MAAN,CAAa,OAAKgH,MAAlB;AACD,aAFD,MAEO;AACL5G,kBAAIuH,QAAJ;AACD;AACDiK;AACD,WAPD;AAQD,SATqB,CAAtB;AAUD;;AAED,WAAKL,QAAL,CAAcvH,KAAd,CAAoB2F,IAApB,CAAyB,YAAM;AAC7BvP,YAAIgI,SAAJ;AACAhI,YAAI2H,gBAAJ;AACD,OAHD;;AAKA,WAAKoD,MAAL,GAAc,IAAd;;AAEA,aAAO,IAAP;AACD;;AAED;;AAEA;;;;;;;+BAIoC;AAAA,UAAnBF,SAAmB,uEAAP,KAAO;;AAClCtF,aAAOC,IAAP,CAAYxF,IAAIyF,KAAhB,EAAuBC,OAAvB,CAA+B,cAAM;AACnC,YAAImF,SAAJ,EAAe;AACb,cACE7K,IAAIyF,KAAJ,CAAUpE,EAAV,EAAcsE,OAAd,CAAsBuD,KAAtB,KAAgC2B,SAAhC,IAA6C7K,IAAIyF,KAAJ,CAAUpE,EAAV,EAAc6P,QAD7D,EAEE;AACAlR,gBAAIyF,KAAJ,CAAUpE,EAAV,EAAcuI,KAAd;AACD;AACF,SAND,MAMO,IAAI5J,IAAIyF,KAAJ,CAAUpE,EAAV,EAAc6P,QAAlB,EAA4B;AACjClR,cAAIyF,KAAJ,CAAUpE,EAAV,EAAcuI,KAAd;AACD;AACF,OAVD;AAWA,aAAO,IAAP;AACD;;AAED;;;;;;;iCAIyC;AAAA,UAAtBiB,SAAsB,uEAAV,QAAU;;AACvC,UAAI7K,IAAI+I,MAAJ,CAAWxI,cAAX,CAA0BsK,SAA1B,CAAJ,EAA0C;AACxC7K,YAAI+I,MAAJ,CAAW8B,SAAX,EAAsB3B,KAAtB,GAA8B,EAA9B;AACD;AACD,aAAO,IAAP;AACD;;AAED;;;;;;;;AAcA;;;;qCAIyBjI,G,EAAK;AAC5BjB,UAAImJ,QAAJ,GAAelB,MAAMrH,UAAN,CAAiB,EAAjB,EAAqBZ,IAAImJ,QAAzB,EAAmClI,GAAnC,CAAf;AACA,aAAO,IAAP;AACD;;AAED;;;;;;;;oCAK4E;AAAA,UAAtD4Q,MAAsD,uEAA7C7R,IAAI8I,iBAAyC;AAAA,UAAtB+B,SAAsB,uEAAV,QAAU;;AAC1E,UAAI,CAAC7K,IAAI+I,MAAJ,CAAWxI,cAAX,CAA0BsK,SAA1B,CAAL,EAA2C;AACzC7K,YAAI+I,MAAJ,CAAW8B,SAAX,IAAwB,EAAC5B,YAAY4I,MAAb,EAAqB3I,OAAO,EAA5B,EAAxB;AACD;;AAEDlJ,UAAI+I,MAAJ,CAAW8B,SAAX,EAAsB5B,UAAtB,GAAmC4I,MAAnC;AACA,aAAO,IAAP;AACD;;AAED;;;;;;;;;;2BAOeC,S,EAAgD;AAAA,UAArC5E,OAAqC,uEAA3B,IAA2B;AAAA,UAArBpJ,EAAqB;AAAA,UAAjBqJ,UAAiB,uEAAJ,EAAI;;AAC7D,aAAO,uBAAe2E,SAAf,EAA0B5E,OAA1B,EAAmCpJ,EAAnC,EAAuCqJ,UAAvC,CAAP;AACD;;AAED;;;;;;8BAGkB;AAChB,aAAO,YAAP;AACD;;AAED;;;;;;;yBAIaG,U,EAAY;AACvB,aAAO,eAASA,UAAT,CAAP;AACD;;;wBA1DoB;AACnB,aAAOtN,IAAI+I,MAAX;AACD;;AAED;;;;;;wBAGyB;AACvB,aAAO/I,IAAIgF,UAAX;AACD;;;;;;AAoDH;;;kBAheqB+L,I;AAierB,IAAI,OAAOtQ,MAAP,KAAkB,WAAtB,EAAmC;AACjCwH,QAAMnI,oBAAN;AACD;;;;;;;AC5eD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;ACvLtC;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;ACpBA,e", "file": "noty.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Noty\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Noty\"] = factory();\n\telse\n\t\troot[\"Noty\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 6);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 13c137d60d3e439c3a2f", "import * as API from 'api'\r\n\r\nexport const animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend'\r\n\r\nexport function inArray (needle, haystack, argStrict) {\r\n  let key\r\n  let strict = !!argStrict\r\n\r\n  if (strict) {\r\n    for (key in haystack) {\r\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\r\n        return true\r\n      }\r\n    }\r\n  } else {\r\n    for (key in haystack) {\r\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\r\n        return true\r\n      }\r\n    }\r\n  }\r\n  return false\r\n}\r\n\r\nexport function stopPropagation (evt) {\r\n  evt = evt || window.event\r\n\r\n  if (typeof evt.stopPropagation !== 'undefined') {\r\n    evt.stopPropagation()\r\n  } else {\r\n    evt.cancelBubble = true\r\n  }\r\n}\r\n\r\nexport const deepExtend = function (out) {\r\n  out = out || {}\r\n\r\n  for (let i = 1; i < arguments.length; i++) {\r\n    let obj = arguments[i]\r\n\r\n    if (!obj) continue\r\n\r\n    for (let key in obj) {\r\n      if (obj.hasOwnProperty(key)) {\r\n        if (Array.isArray(obj[key])) {\r\n          out[key] = obj[key]\r\n        } else if (typeof obj[key] === 'object' && obj[key] !== null) {\r\n          out[key] = deepExtend(out[key], obj[key])\r\n        } else {\r\n          out[key] = obj[key]\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return out\r\n}\r\n\r\nexport function generateID (prefix = '') {\r\n  let id = `noty_${prefix}_`\r\n\r\n  id += 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n    let r = Math.random() * 16 | 0\r\n    let v = c === 'x' ? r : r & 0x3 | 0x8\r\n    return v.toString(16)\r\n  })\r\n\r\n  return id\r\n}\r\n\r\nexport function outerHeight (el) {\r\n  let height = el.offsetHeight\r\n  let style = window.getComputedStyle(el)\r\n\r\n  height += parseInt(style.marginTop) + parseInt(style.marginBottom)\r\n  return height\r\n}\r\n\r\nexport let css = (function () {\r\n  let cssPrefixes = ['Webkit', 'O', 'Moz', 'ms']\r\n  let cssProps = {}\r\n\r\n  function camelCase (string) {\r\n    return string\r\n      .replace(/^-ms-/, 'ms-')\r\n      .replace(/-([\\da-z])/gi, function (match, letter) {\r\n        return letter.toUpperCase()\r\n      })\r\n  }\r\n\r\n  function getVendorProp (name) {\r\n    let style = document.body.style\r\n    if (name in style) return name\r\n\r\n    let i = cssPrefixes.length\r\n    let capName = name.charAt(0).toUpperCase() + name.slice(1)\r\n    let vendorName\r\n\r\n    while (i--) {\r\n      vendorName = cssPrefixes[i] + capName\r\n      if (vendorName in style) return vendorName\r\n    }\r\n\r\n    return name\r\n  }\r\n\r\n  function getStyleProp (name) {\r\n    name = camelCase(name)\r\n    return cssProps[name] || (cssProps[name] = getVendorProp(name))\r\n  }\r\n\r\n  function applyCss (element, prop, value) {\r\n    prop = getStyleProp(prop)\r\n    element.style[prop] = value\r\n  }\r\n\r\n  return function (element, properties) {\r\n    let args = arguments\r\n    let prop\r\n    let value\r\n\r\n    if (args.length === 2) {\r\n      for (prop in properties) {\r\n        if (properties.hasOwnProperty(prop)) {\r\n          value = properties[prop]\r\n          if (value !== undefined && properties.hasOwnProperty(prop)) {\r\n            applyCss(element, prop, value)\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      applyCss(element, args[1], args[2])\r\n    }\r\n  }\r\n})()\r\n\r\nexport function addListener (el, events, cb, useCapture = false) {\r\n  events = events.split(' ')\r\n  for (let i = 0; i < events.length; i++) {\r\n    if (document.addEventListener) {\r\n      el.addEventListener(events[i], cb, useCapture)\r\n    } else if (document.attachEvent) {\r\n      el.attachEvent('on' + events[i], cb)\r\n    }\r\n  }\r\n}\r\n\r\nexport function hasClass (element, name) {\r\n  let list = typeof element === 'string' ? element : classList(element)\r\n  return list.indexOf(' ' + name + ' ') >= 0\r\n}\r\n\r\nexport function addClass (element, name) {\r\n  let oldList = classList(element)\r\n  let newList = oldList + name\r\n\r\n  if (hasClass(oldList, name)) return\r\n\r\n  // Trim the opening space.\r\n  element.className = newList.substring(1)\r\n}\r\n\r\nexport function removeClass (element, name) {\r\n  let oldList = classList(element)\r\n  let newList\r\n\r\n  if (!hasClass(element, name)) return\r\n\r\n  // Replace the class name.\r\n  newList = oldList.replace(' ' + name + ' ', ' ')\r\n\r\n  // Trim the opening and closing spaces.\r\n  element.className = newList.substring(1, newList.length - 1)\r\n}\r\n\r\nexport function remove (element) {\r\n  if (element.parentNode) {\r\n    element.parentNode.removeChild(element)\r\n  }\r\n}\r\n\r\nexport function classList (element) {\r\n  return (' ' + ((element && element.className) || '') + ' ').replace(\r\n    /\\s+/gi,\r\n    ' '\r\n  )\r\n}\r\n\r\nexport function visibilityChangeFlow () {\r\n  let hidden\r\n  let visibilityChange\r\n  if (typeof document.hidden !== 'undefined') {\r\n    // Opera 12.10 and Firefox 18 and later support\r\n    hidden = 'hidden'\r\n    visibilityChange = 'visibilitychange'\r\n  } else if (typeof document.msHidden !== 'undefined') {\r\n    hidden = 'msHidden'\r\n    visibilityChange = 'msvisibilitychange'\r\n  } else if (typeof document.webkitHidden !== 'undefined') {\r\n    hidden = 'webkitHidden'\r\n    visibilityChange = 'webkitvisibilitychange'\r\n  }\r\n\r\n  function onVisibilityChange () {\r\n    API.PageHidden = document[hidden]\r\n    handleVisibilityChange()\r\n  }\r\n\r\n  function onBlur () {\r\n    API.PageHidden = true\r\n    handleVisibilityChange()\r\n  }\r\n\r\n  function onFocus () {\r\n    API.PageHidden = false\r\n    handleVisibilityChange()\r\n  }\r\n\r\n  function handleVisibilityChange () {\r\n    if (API.PageHidden) stopAll()\r\n    else resumeAll()\r\n  }\r\n\r\n  function stopAll () {\r\n    setTimeout(\r\n      function () {\r\n        Object.keys(API.Store).forEach(id => {\r\n          if (API.Store.hasOwnProperty(id)) {\r\n            if (API.Store[id].options.visibilityControl) {\r\n              API.Store[id].stop()\r\n            }\r\n          }\r\n        })\r\n      },\r\n      100\r\n    )\r\n  }\r\n\r\n  function resumeAll () {\r\n    setTimeout(\r\n      function () {\r\n        Object.keys(API.Store).forEach(id => {\r\n          if (API.Store.hasOwnProperty(id)) {\r\n            if (API.Store[id].options.visibilityControl) {\r\n              API.Store[id].resume()\r\n            }\r\n          }\r\n        })\r\n        API.queueRenderAll()\r\n      },\r\n      100\r\n    )\r\n  }\r\n\r\n  if (visibilityChange) {\r\n    addListener(document, visibilityChange, onVisibilityChange)\r\n  }\r\n\r\n  addListener(window, 'blur', onBlur)\r\n  addListener(window, 'focus', onFocus)\r\n}\r\n\r\nexport function createAudioElements (ref) {\r\n  if (ref.hasSound) {\r\n    const audioElement = document.createElement('audio')\r\n\r\n    ref.options.sounds.sources.forEach(s => {\r\n      const source = document.createElement('source')\r\n      source.src = s\r\n      source.type = `audio/${getExtension(s)}`\r\n      audioElement.appendChild(source)\r\n    })\r\n\r\n    if (ref.barDom) {\r\n      ref.barDom.appendChild(audioElement)\r\n    } else {\r\n      document.querySelector('body').appendChild(audioElement)\r\n    }\r\n\r\n    audioElement.volume = ref.options.sounds.volume\r\n\r\n    if (!ref.soundPlayed) {\r\n      audioElement.play()\r\n      ref.soundPlayed = true\r\n    }\r\n\r\n    audioElement.onended = function () {\r\n      remove(audioElement)\r\n    }\r\n  }\r\n}\r\n\r\nfunction getExtension (fileName) {\r\n  return fileName.match(/\\.([^.]+)$/)[1]\r\n}\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/utils.js", "import * as Utils from 'utils'\r\n\r\nexport let PageHidden = false\r\nexport let DocModalCount = 0\r\n\r\nconst DocTitleProps = {\r\n  originalTitle: null,\r\n  count: 0,\r\n  changed: false,\r\n  timer: -1\r\n}\r\n\r\nexport const docTitle = {\r\n  increment: () => {\r\n    DocTitleProps.count++\r\n\r\n    docTitle._update()\r\n  },\r\n\r\n  decrement: () => {\r\n    DocTitleProps.count--\r\n\r\n    if (DocTitleProps.count <= 0) {\r\n      docTitle._clear()\r\n      return\r\n    }\r\n\r\n    docTitle._update()\r\n  },\r\n\r\n  _update: () => {\r\n    let title = document.title\r\n\r\n    if (!DocTitleProps.changed) {\r\n      DocTitleProps.originalTitle = title\r\n      document.title = `(${DocTitleProps.count}) ${title}`\r\n      DocTitleProps.changed = true\r\n    } else {\r\n      document.title = `(${DocTitleProps.count}) ${DocTitleProps.originalTitle}`\r\n    }\r\n  },\r\n\r\n  _clear: () => {\r\n    if (DocTitleProps.changed) {\r\n      DocTitleProps.count = 0\r\n      document.title = DocTitleProps.originalTitle\r\n      DocTitleProps.changed = false\r\n    }\r\n  }\r\n}\r\n\r\nexport const DefaultMaxVisible = 5\r\n\r\nexport const Queues = {\r\n  global: {\r\n    maxVisible: DefaultMaxVisible,\r\n    queue: []\r\n  }\r\n}\r\n\r\nexport const Store = {}\r\n\r\nexport let Defaults = {\r\n  type: 'alert',\r\n  layout: 'topRight',\r\n  theme: 'mint',\r\n  text: '',\r\n  timeout: false,\r\n  progressBar: true,\r\n  closeWith: ['click'],\r\n  animation: {\r\n    open: 'noty_effects_open',\r\n    close: 'noty_effects_close'\r\n  },\r\n  id: false,\r\n  force: false,\r\n  killer: false,\r\n  queue: 'global',\r\n  container: false,\r\n  buttons: [],\r\n  callbacks: {\r\n    beforeShow: null,\r\n    onShow: null,\r\n    afterShow: null,\r\n    onClose: null,\r\n    afterClose: null,\r\n    onClick: null,\r\n    onHover: null,\r\n    onTemplate: null\r\n  },\r\n  sounds: {\r\n    sources: [],\r\n    volume: 1,\r\n    conditions: []\r\n  },\r\n  titleCount: {\r\n    conditions: []\r\n  },\r\n  modal: false,\r\n  visibilityControl: false\r\n}\r\n\r\n/**\r\n * @param {string} queueName\r\n * @return {object}\r\n */\r\nexport function getQueueCounts (queueName = 'global') {\r\n  let count = 0\r\n  let max = DefaultMaxVisible\r\n\r\n  if (Queues.hasOwnProperty(queueName)) {\r\n    max = Queues[queueName].maxVisible\r\n    Object.keys(Store).forEach(i => {\r\n      if (Store[i].options.queue === queueName && !Store[i].closed) count++\r\n    })\r\n  }\r\n\r\n  return {\r\n    current: count,\r\n    maxVisible: max\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function addToQueue (ref) {\r\n  if (!Queues.hasOwnProperty(ref.options.queue)) {\r\n    Queues[ref.options.queue] = {maxVisible: DefaultMaxVisible, queue: []}\r\n  }\r\n\r\n  Queues[ref.options.queue].queue.push(ref)\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function removeFromQueue (ref) {\r\n  if (Queues.hasOwnProperty(ref.options.queue)) {\r\n    const queue = []\r\n    Object.keys(Queues[ref.options.queue].queue).forEach(i => {\r\n      if (Queues[ref.options.queue].queue[i].id !== ref.id) {\r\n        queue.push(Queues[ref.options.queue].queue[i])\r\n      }\r\n    })\r\n    Queues[ref.options.queue].queue = queue\r\n  }\r\n}\r\n\r\n/**\r\n * @param {string} queueName\r\n * @return {void}\r\n */\r\nexport function queueRender (queueName = 'global') {\r\n  if (Queues.hasOwnProperty(queueName)) {\r\n    const noty = Queues[queueName].queue.shift()\r\n\r\n    if (noty) noty.show()\r\n  }\r\n}\r\n\r\n/**\r\n * @return {void}\r\n */\r\nexport function queueRenderAll () {\r\n  Object.keys(Queues).forEach(queueName => {\r\n    queueRender(queueName)\r\n  })\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function ghostFix (ref) {\r\n  const ghostID = Utils.generateID('ghost')\r\n  let ghost = document.createElement('div')\r\n  ghost.setAttribute('id', ghostID)\r\n  Utils.css(ghost, {\r\n    height: Utils.outerHeight(ref.barDom) + 'px'\r\n  })\r\n\r\n  ref.barDom.insertAdjacentHTML('afterend', ghost.outerHTML)\r\n\r\n  Utils.remove(ref.barDom)\r\n  ghost = document.getElementById(ghostID)\r\n  Utils.addClass(ghost, 'noty_fix_effects_height')\r\n  Utils.addListener(ghost, Utils.animationEndEvents, () => {\r\n    Utils.remove(ghost)\r\n  })\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function build (ref) {\r\n  findOrCreateContainer(ref)\r\n\r\n  const markup = `<div class=\"noty_body\">${ref.options.text}</div>${buildButtons(ref)}<div class=\"noty_progressbar\"></div>`\r\n\r\n  ref.barDom = document.createElement('div')\r\n  ref.barDom.setAttribute('id', ref.id)\r\n  Utils.addClass(\r\n    ref.barDom,\r\n    `noty_bar noty_type__${ref.options.type} noty_theme__${ref.options.theme}`\r\n  )\r\n\r\n  ref.barDom.innerHTML = markup\r\n\r\n  fire(ref, 'onTemplate')\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {boolean}\r\n */\r\nexport function hasButtons (ref) {\r\n  return !!(ref.options.buttons && Object.keys(ref.options.buttons).length)\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {string}\r\n */\r\nfunction buildButtons (ref) {\r\n  if (hasButtons(ref)) {\r\n    let buttons = document.createElement('div')\r\n    Utils.addClass(buttons, 'noty_buttons')\r\n\r\n    Object.keys(ref.options.buttons).forEach(key => {\r\n      buttons.appendChild(ref.options.buttons[key].dom)\r\n    })\r\n\r\n    ref.options.buttons.forEach(btn => {\r\n      buttons.appendChild(btn.dom)\r\n    })\r\n    return buttons.outerHTML\r\n  }\r\n  return ''\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function handleModal (ref) {\r\n  if (ref.options.modal) {\r\n    if (DocModalCount === 0) {\r\n      createModal(ref)\r\n    }\r\n\r\n    DocModalCount++\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function handleModalClose (ref) {\r\n  if (ref.options.modal && DocModalCount > 0) {\r\n    DocModalCount--\r\n\r\n    if (DocModalCount <= 0) {\r\n      const modal = document.querySelector('.noty_modal')\r\n\r\n      if (modal) {\r\n        Utils.removeClass(modal, 'noty_modal_open')\r\n        Utils.addClass(modal, 'noty_modal_close')\r\n        Utils.addListener(modal, Utils.animationEndEvents, () => {\r\n          Utils.remove(modal)\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * @return {void}\r\n */\r\nfunction createModal () {\r\n  const body = document.querySelector('body')\r\n  const modal = document.createElement('div')\r\n  Utils.addClass(modal, 'noty_modal')\r\n  body.insertBefore(modal, body.firstChild)\r\n  Utils.addClass(modal, 'noty_modal_open')\r\n\r\n  Utils.addListener(modal, Utils.animationEndEvents, () => {\r\n    Utils.removeClass(modal, 'noty_modal_open')\r\n  })\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nfunction findOrCreateContainer (ref) {\r\n  if (ref.options.container) {\r\n    ref.layoutDom = document.querySelector(ref.options.container)\r\n    return\r\n  }\r\n\r\n  const layoutID = `noty_layout__${ref.options.layout}`\r\n  ref.layoutDom = document.querySelector(`div#${layoutID}`)\r\n\r\n  if (!ref.layoutDom) {\r\n    ref.layoutDom = document.createElement('div')\r\n    ref.layoutDom.setAttribute('id', layoutID)\r\n    ref.layoutDom.setAttribute('role', 'alert')\r\n    ref.layoutDom.setAttribute('aria-live', 'polite')\r\n    Utils.addClass(ref.layoutDom, 'noty_layout')\r\n    document.querySelector('body').appendChild(ref.layoutDom)\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function queueClose (ref) {\r\n  if (ref.options.timeout) {\r\n    if (ref.options.progressBar && ref.progressDom) {\r\n      Utils.css(ref.progressDom, {\r\n        transition: `width ${ref.options.timeout}ms linear`,\r\n        width: '0%'\r\n      })\r\n    }\r\n\r\n    clearTimeout(ref.closeTimer)\r\n\r\n    ref.closeTimer = setTimeout(\r\n      () => {\r\n        ref.close()\r\n      },\r\n      ref.options.timeout\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function dequeueClose (ref) {\r\n  if (ref.options.timeout && ref.closeTimer) {\r\n    clearTimeout(ref.closeTimer)\r\n    ref.closeTimer = -1\r\n\r\n    if (ref.options.progressBar && ref.progressDom) {\r\n      Utils.css(ref.progressDom, {\r\n        transition: 'width 0ms linear',\r\n        width: '100%'\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @param {string} eventName\r\n * @return {void}\r\n */\r\nexport function fire (ref, eventName) {\r\n  if (ref.listeners.hasOwnProperty(eventName)) {\r\n    ref.listeners[eventName].forEach(cb => {\r\n      if (typeof cb === 'function') {\r\n        cb.apply(ref)\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function openFlow (ref) {\r\n  fire(ref, 'afterShow')\r\n  queueClose(ref)\r\n\r\n  Utils.addListener(ref.barDom, 'mouseenter', () => {\r\n    dequeueClose(ref)\r\n  })\r\n\r\n  Utils.addListener(ref.barDom, 'mouseleave', () => {\r\n    queueClose(ref)\r\n  })\r\n}\r\n\r\n/**\r\n * @param {Noty} ref\r\n * @return {void}\r\n */\r\nexport function closeFlow (ref) {\r\n  delete Store[ref.id]\r\n  ref.closing = false\r\n  fire(ref, 'afterClose')\r\n\r\n  Utils.remove(ref.barDom)\r\n\r\n  if (\r\n    ref.layoutDom.querySelectorAll('.noty_bar').length === 0 &&\r\n    !ref.options.container\r\n  ) {\r\n    Utils.remove(ref.layoutDom)\r\n  }\r\n\r\n  if (\r\n    Utils.inArray('docVisible', ref.options.titleCount.conditions) ||\r\n    Utils.inArray('docHidden', ref.options.titleCount.conditions)\r\n  ) {\r\n    docTitle.decrement()\r\n  }\r\n\r\n  queueRender(ref.options.queue)\r\n}\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/api.js", "import * as Utils from 'utils'\r\n\r\nexport class NotyButton {\r\n  constructor (html, classes, cb, attributes = {}) {\r\n    this.dom = document.createElement('button')\r\n    this.dom.innerHTML = html\r\n    this.id = (attributes.id = attributes.id || Utils.generateID('button'))\r\n    this.cb = cb\r\n    Object.keys(attributes).forEach(propertyName => {\r\n      this.dom.setAttribute(propertyName, attributes[propertyName])\r\n    })\r\n    Utils.addClass(this.dom, classes || 'noty_btn')\r\n\r\n    return this\r\n  }\r\n}\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/button.js", "export class Push {\r\n  constructor (workerPath = '/service-worker.js') {\r\n    this.subData = {}\r\n    this.workerPath = workerPath\r\n    this.listeners = {\r\n      onPermissionGranted: [],\r\n      onPermissionDenied: [],\r\n      onSubscriptionSuccess: [],\r\n      onSubscriptionCancel: [],\r\n      onWorkerError: [],\r\n      onWorkerSuccess: [],\r\n      onWorkerNotSupported: []\r\n    }\r\n    return this\r\n  }\r\n\r\n  /**\r\n   * @param {string} eventName\r\n   * @param {function} cb\r\n   * @return {Push}\r\n   */\r\n  on (eventName, cb = () => {}) {\r\n    if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\r\n      this.listeners[eventName].push(cb)\r\n    }\r\n\r\n    return this\r\n  }\r\n\r\n  fire (eventName, params = []) {\r\n    if (this.listeners.hasOwnProperty(eventName)) {\r\n      this.listeners[eventName].forEach(cb => {\r\n        if (typeof cb === 'function') {\r\n          cb.apply(this, params)\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  create () {\r\n    console.log('NOT IMPLEMENTED YET')\r\n  }\r\n\r\n  /**\r\n   * @return {boolean}\r\n   */\r\n  isSupported () {\r\n    let result = false\r\n\r\n    try {\r\n      result = window.Notification ||\r\n        window.webkitNotifications ||\r\n        navigator.mozNotification ||\r\n        (window.external && window.external.msIsSiteMode() !== undefined)\r\n    } catch (e) {}\r\n\r\n    return result\r\n  }\r\n\r\n  /**\r\n   * @return {string}\r\n   */\r\n  getPermissionStatus () {\r\n    let perm = 'default'\r\n\r\n    if (window.Notification && window.Notification.permissionLevel) {\r\n      perm = window.Notification.permissionLevel\r\n    } else if (\r\n      window.webkitNotifications && window.webkitNotifications.checkPermission\r\n    ) {\r\n      switch (window.webkitNotifications.checkPermission()) {\r\n        case 1:\r\n          perm = 'default'\r\n          break\r\n        case 0:\r\n          perm = 'granted'\r\n          break\r\n        default:\r\n          perm = 'denied'\r\n      }\r\n    } else if (window.Notification && window.Notification.permission) {\r\n      perm = window.Notification.permission\r\n    } else if (navigator.mozNotification) {\r\n      perm = 'granted'\r\n    } else if (\r\n      window.external && window.external.msIsSiteMode() !== undefined\r\n    ) {\r\n      perm = window.external.msIsSiteMode() ? 'granted' : 'default'\r\n    }\r\n\r\n    return perm.toString().toLowerCase()\r\n  }\r\n\r\n  /**\r\n   * @return {string}\r\n   */\r\n  getEndpoint (subscription) {\r\n    let endpoint = subscription.endpoint\r\n    const subscriptionId = subscription.subscriptionId\r\n\r\n    // fix for Chrome < 45\r\n    if (subscriptionId && endpoint.indexOf(subscriptionId) === -1) {\r\n      endpoint += '/' + subscriptionId\r\n    }\r\n\r\n    return endpoint\r\n  }\r\n\r\n  /**\r\n   * @return {boolean}\r\n   */\r\n  isSWRegistered () {\r\n    try {\r\n      return navigator.serviceWorker.controller.state === 'activated'\r\n    } catch (e) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @return {void}\r\n   */\r\n  unregisterWorker () {\r\n    const self = this\r\n    if ('serviceWorker' in navigator) {\r\n      navigator.serviceWorker.getRegistrations().then(function (registrations) {\r\n        for (let registration of registrations) {\r\n          registration.unregister()\r\n          self.fire('onSubscriptionCancel')\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @return {void}\r\n   */\r\n  requestSubscription (userVisibleOnly = true) {\r\n    const self = this\r\n    const current = this.getPermissionStatus()\r\n    const cb = result => {\r\n      if (result === 'granted') {\r\n        this.fire('onPermissionGranted')\r\n\r\n        if ('serviceWorker' in navigator) {\r\n          navigator.serviceWorker.register(this.workerPath).then(function () {\r\n            navigator.serviceWorker.ready.then(\r\n              function (serviceWorkerRegistration) {\r\n                self.fire('onWorkerSuccess')\r\n                serviceWorkerRegistration.pushManager\r\n                  .subscribe({\r\n                    userVisibleOnly: userVisibleOnly\r\n                  })\r\n                  .then(function (subscription) {\r\n                    const key = subscription.getKey('p256dh')\r\n                    const token = subscription.getKey('auth')\r\n\r\n                    self.subData = {\r\n                      endpoint: self.getEndpoint(subscription),\r\n                      p256dh: key\r\n                        ? window.btoa(\r\n                            String.fromCharCode.apply(null, new Uint8Array(key))\r\n                          )\r\n                        : null,\r\n                      auth: token\r\n                        ? window.btoa(\r\n                            String.fromCharCode.apply(\r\n                              null,\r\n                              new Uint8Array(token)\r\n                            )\r\n                          )\r\n                        : null\r\n                    }\r\n\r\n                    self.fire('onSubscriptionSuccess', [self.subData])\r\n                  })\r\n                  .catch(function (err) {\r\n                    self.fire('onWorkerError', [err])\r\n                  })\r\n              }\r\n            )\r\n          })\r\n        } else {\r\n          self.fire('onWorkerNotSupported')\r\n        }\r\n      } else if (result === 'denied') {\r\n        this.fire('onPermissionDenied')\r\n        this.unregisterWorker()\r\n      }\r\n    }\r\n\r\n    if (current === 'default') {\r\n      if (window.Notification && window.Notification.requestPermission) {\r\n        window.Notification.requestPermission(cb)\r\n      } else if (\r\n        window.webkitNotifications && window.webkitNotifications.checkPermission\r\n      ) {\r\n        window.webkitNotifications.requestPermission(cb)\r\n      }\r\n    } else {\r\n      cb(current)\r\n    }\r\n  }\r\n}\r\n\n\n\n// WEBPACK FOOTER //\n// ./src/push.js", "/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   4.1.1\n */\n\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  var type = typeof x;\n  return x !== null && (type === 'object' || type === 'function');\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\nvar _isArray = undefined;\nif (Array.isArray) {\n  _isArray = Array.isArray;\n} else {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = undefined;\nvar customSchedulerFn = undefined;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && ({}).toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  if (typeof vertxNext !== 'undefined') {\n    return function () {\n      vertxNext(flush);\n    };\n  }\n\n  return useSetTimeout();\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var r = require;\n    var vertx = r('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = undefined;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var _arguments = arguments;\n\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n  if (_state) {\n    (function () {\n      var callback = _arguments[_state - 1];\n      asap(function () {\n        return invokeCallback(_state, child, callback, parent._result);\n      });\n    })();\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve$1(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(16);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nvar GET_THEN_ERROR = new ErrorObject();\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction getThen(promise) {\n  try {\n    return promise.then;\n  } catch (error) {\n    GET_THEN_ERROR.error = error;\n    return GET_THEN_ERROR;\n  }\n}\n\nfunction tryThen(then$$1, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then$$1.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then$$1) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then$$1, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return resolve(promise, value);\n    }, function (reason) {\n      return reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$1) {\n  if (maybeThenable.constructor === promise.constructor && then$$1 === then && maybeThenable.constructor.resolve === resolve$1) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$1 === GET_THEN_ERROR) {\n      reject(promise, GET_THEN_ERROR.error);\n      GET_THEN_ERROR.error = null;\n    } else if (then$$1 === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$1)) {\n      handleForeignThenable(promise, maybeThenable, then$$1);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction resolve(promise, value) {\n  if (promise === value) {\n    reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    handleMaybeThenable(promise, value, getThen(value));\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = undefined,\n      callback = undefined,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction ErrorObject() {\n  this.error = null;\n}\n\nvar TRY_CATCH_ERROR = new ErrorObject();\n\nfunction tryCatch(callback, detail) {\n  try {\n    return callback(detail);\n  } catch (e) {\n    TRY_CATCH_ERROR.error = e;\n    return TRY_CATCH_ERROR;\n  }\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = undefined,\n      error = undefined,\n      succeeded = undefined,\n      failed = undefined;\n\n  if (hasCallback) {\n    value = tryCatch(callback, detail);\n\n    if (value === TRY_CATCH_ERROR) {\n      failed = true;\n      error = value.error;\n      value.error = null;\n    } else {\n      succeeded = true;\n    }\n\n    if (promise === value) {\n      reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n    succeeded = true;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n      resolve(promise, value);\n    } else if (failed) {\n      reject(promise, error);\n    } else if (settled === FULFILLED) {\n      fulfill(promise, value);\n    } else if (settled === REJECTED) {\n      reject(promise, value);\n    }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      resolve(promise, value);\n    }, function rejectPromise(reason) {\n      reject(promise, reason);\n    });\n  } catch (e) {\n    reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction Enumerator$1(Constructor, input) {\n  this._instanceConstructor = Constructor;\n  this.promise = new Constructor(noop);\n\n  if (!this.promise[PROMISE_ID]) {\n    makePromise(this.promise);\n  }\n\n  if (isArray(input)) {\n    this.length = input.length;\n    this._remaining = input.length;\n\n    this._result = new Array(this.length);\n\n    if (this.length === 0) {\n      fulfill(this.promise, this._result);\n    } else {\n      this.length = this.length || 0;\n      this._enumerate(input);\n      if (this._remaining === 0) {\n        fulfill(this.promise, this._result);\n      }\n    }\n  } else {\n    reject(this.promise, validationError());\n  }\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n}\n\nEnumerator$1.prototype._enumerate = function (input) {\n  for (var i = 0; this._state === PENDING && i < input.length; i++) {\n    this._eachEntry(input[i], i);\n  }\n};\n\nEnumerator$1.prototype._eachEntry = function (entry, i) {\n  var c = this._instanceConstructor;\n  var resolve$$1 = c.resolve;\n\n  if (resolve$$1 === resolve$1) {\n    var _then = getThen(entry);\n\n    if (_then === then && entry._state !== PENDING) {\n      this._settledAt(entry._state, i, entry._result);\n    } else if (typeof _then !== 'function') {\n      this._remaining--;\n      this._result[i] = entry;\n    } else if (c === Promise$2) {\n      var promise = new c(noop);\n      handleMaybeThenable(promise, entry, _then);\n      this._willSettleAt(promise, i);\n    } else {\n      this._willSettleAt(new c(function (resolve$$1) {\n        return resolve$$1(entry);\n      }), i);\n    }\n  } else {\n    this._willSettleAt(resolve$$1(entry), i);\n  }\n};\n\nEnumerator$1.prototype._settledAt = function (state, i, value) {\n  var promise = this.promise;\n\n  if (promise._state === PENDING) {\n    this._remaining--;\n\n    if (state === REJECTED) {\n      reject(promise, value);\n    } else {\n      this._result[i] = value;\n    }\n  }\n\n  if (this._remaining === 0) {\n    fulfill(promise, this._result);\n  }\n};\n\nEnumerator$1.prototype._willSettleAt = function (promise, i) {\n  var enumerator = this;\n\n  subscribe(promise, undefined, function (value) {\n    return enumerator._settledAt(FULFILLED, i, value);\n  }, function (reason) {\n    return enumerator._settledAt(REJECTED, i, reason);\n  });\n};\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all$1(entries) {\n  return new Enumerator$1(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race$1(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject$1(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {function} resolver\n  Useful for tooling.\n  @constructor\n*/\nfunction Promise$2(resolver) {\n  this[PROMISE_ID] = nextId();\n  this._result = this._state = undefined;\n  this._subscribers = [];\n\n  if (noop !== resolver) {\n    typeof resolver !== 'function' && needsResolver();\n    this instanceof Promise$2 ? initializePromise(this, resolver) : needsNew();\n  }\n}\n\nPromise$2.all = all$1;\nPromise$2.race = race$1;\nPromise$2.resolve = resolve$1;\nPromise$2.reject = reject$1;\nPromise$2._setScheduler = setScheduler;\nPromise$2._setAsap = setAsap;\nPromise$2._asap = asap;\n\nPromise$2.prototype = {\n  constructor: Promise$2,\n\n  /**\n    The primary way of interacting with a promise is through its `then` method,\n    which registers callbacks to receive either a promise's eventual value or the\n    reason why the promise cannot be fulfilled.\n  \n    ```js\n    findUser().then(function(user){\n      // user is available\n    }, function(reason){\n      // user is unavailable, and you are given the reason why\n    });\n    ```\n  \n    Chaining\n    --------\n  \n    The return value of `then` is itself a promise.  This second, 'downstream'\n    promise is resolved with the return value of the first promise's fulfillment\n    or rejection handler, or rejected if the handler throws an exception.\n  \n    ```js\n    findUser().then(function (user) {\n      return user.name;\n    }, function (reason) {\n      return 'default name';\n    }).then(function (userName) {\n      // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n      // will be `'default name'`\n    });\n  \n    findUser().then(function (user) {\n      throw new Error('Found user, but still unhappy');\n    }, function (reason) {\n      throw new Error('`findUser` rejected and we're unhappy');\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n      // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n    });\n    ```\n    If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n  \n    ```js\n    findUser().then(function (user) {\n      throw new PedagogicalException('Upstream error');\n    }).then(function (value) {\n      // never reached\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // The `PedgagocialException` is propagated all the way down to here\n    });\n    ```\n  \n    Assimilation\n    ------------\n  \n    Sometimes the value you want to propagate to a downstream promise can only be\n    retrieved asynchronously. This can be achieved by returning a promise in the\n    fulfillment or rejection handler. The downstream promise will then be pending\n    until the returned promise is settled. This is called *assimilation*.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // The user's comments are now available\n    });\n    ```\n  \n    If the assimliated promise rejects, then the downstream promise will also reject.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // If `findCommentsByAuthor` fulfills, we'll have the value here\n    }, function (reason) {\n      // If `findCommentsByAuthor` rejects, we'll have the reason here\n    });\n    ```\n  \n    Simple Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let result;\n  \n    try {\n      result = findResult();\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n    findResult(function(result, err){\n      if (err) {\n        // failure\n      } else {\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findResult().then(function(result){\n      // success\n    }, function(reason){\n      // failure\n    });\n    ```\n  \n    Advanced Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let author, books;\n  \n    try {\n      author = findAuthor();\n      books  = findBooksByAuthor(author);\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n  \n    function foundBooks(books) {\n  \n    }\n  \n    function failure(reason) {\n  \n    }\n  \n    findAuthor(function(author, err){\n      if (err) {\n        failure(err);\n        // failure\n      } else {\n        try {\n          findBoooksByAuthor(author, function(books, err) {\n            if (err) {\n              failure(err);\n            } else {\n              try {\n                foundBooks(books);\n              } catch(reason) {\n                failure(reason);\n              }\n            }\n          });\n        } catch(error) {\n          failure(err);\n        }\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findAuthor().\n      then(findBooksByAuthor).\n      then(function(books){\n        // found books\n    }).catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method then\n    @param {Function} onFulfilled\n    @param {Function} onRejected\n    Useful for tooling.\n    @return {Promise}\n  */\n  then: then,\n\n  /**\n    `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n    as the catch block of a try/catch statement.\n  \n    ```js\n    function findAuthor(){\n      throw new Error('couldn't find that author');\n    }\n  \n    // synchronous\n    try {\n      findAuthor();\n    } catch(reason) {\n      // something went wrong\n    }\n  \n    // async with promises\n    findAuthor().catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method catch\n    @param {Function} onRejection\n    Useful for tooling.\n    @return {Promise}\n  */\n  'catch': function _catch(onRejection) {\n    return this.then(null, onRejection);\n  }\n};\n\n/*global self*/\nfunction polyfill$1() {\n    var local = undefined;\n\n    if (typeof global !== 'undefined') {\n        local = global;\n    } else if (typeof self !== 'undefined') {\n        local = self;\n    } else {\n        try {\n            local = Function('return this')();\n        } catch (e) {\n            throw new Error('polyfill failed because global object is unavailable in this environment');\n        }\n    }\n\n    var P = local.Promise;\n\n    if (P) {\n        var promiseToString = null;\n        try {\n            promiseToString = Object.prototype.toString.call(P.resolve());\n        } catch (e) {\n            // silently ignored\n        }\n\n        if (promiseToString === '[object Promise]' && !P.cast) {\n            return;\n        }\n    }\n\n    local.Promise = Promise$2;\n}\n\n// Strange compat..\nPromise$2.polyfill = polyfill$1;\nPromise$2.Promise = Promise$2;\n\nreturn Promise$2;\n\n})));\n\n//# sourceMappingURL=es6-promise.map\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/es6-promise/dist/es6-promise.js\n// module id = 4\n// module chunks = 0", "// removed by extract-text-webpack-plugin\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/noty.scss\n// module id = 5\n// module chunks = 0", "/* global VERSION */\n\nimport 'noty.scss'\nimport Promise from 'es6-promise'\nimport * as Utils from 'utils'\nimport * as API from 'api'\nimport { NotyButton } from 'button'\nimport { Push } from 'push'\n\nexport default class Noty {\n  /**\n   * @param {object} options\n   * @return {Noty}\n   */\n  constructor (options = {}) {\n    this.options = Utils.deepExtend({}, API.Defaults, options)\n\n    if (API.Store[this.options.id]) {\n      return API.Store[this.options.id]\n    }\n\n    this.id = this.options.id || Utils.generateID('bar')\n    this.closeTimer = -1\n    this.barDom = null\n    this.layoutDom = null\n    this.progressDom = null\n    this.showing = false\n    this.shown = false\n    this.closed = false\n    this.closing = false\n    this.killable = this.options.timeout || this.options.closeWith.length > 0\n    this.hasSound = this.options.sounds.sources.length > 0\n    this.soundPlayed = false\n    this.listeners = {\n      beforeShow: [],\n      onShow: [],\n      afterShow: [],\n      onClose: [],\n      afterClose: [],\n      onClick: [],\n      onHover: [],\n      onTemplate: []\n    }\n    this.promises = {\n      show: null,\n      close: null\n    }\n    this.on('beforeShow', this.options.callbacks.beforeShow)\n    this.on('onShow', this.options.callbacks.onShow)\n    this.on('afterShow', this.options.callbacks.afterShow)\n    this.on('onClose', this.options.callbacks.onClose)\n    this.on('afterClose', this.options.callbacks.afterClose)\n    this.on('onClick', this.options.callbacks.onClick)\n    this.on('onHover', this.options.callbacks.onHover)\n    this.on('onTemplate', this.options.callbacks.onTemplate)\n\n    return this\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} cb\n   * @return {Noty}\n   */\n  on (eventName, cb = () => {}) {\n    if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\n      this.listeners[eventName].push(cb)\n    }\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  show () {\n    if (this.showing || this.shown) {\n      return this // preventing multiple show\n    }\n\n    if (this.options.killer === true) {\n      Noty.closeAll()\n    } else if (typeof this.options.killer === 'string') {\n      Noty.closeAll(this.options.killer)\n    }\n\n    let queueCounts = API.getQueueCounts(this.options.queue)\n\n    if (\n      queueCounts.current >= queueCounts.maxVisible ||\n      (API.PageHidden && this.options.visibilityControl)\n    ) {\n      API.addToQueue(this)\n\n      if (\n        API.PageHidden &&\n        this.hasSound &&\n        Utils.inArray('docHidden', this.options.sounds.conditions)\n      ) {\n        Utils.createAudioElements(this)\n      }\n\n      if (\n        API.PageHidden &&\n        Utils.inArray('docHidden', this.options.titleCount.conditions)\n      ) {\n        API.docTitle.increment()\n      }\n\n      return this\n    }\n\n    API.Store[this.id] = this\n\n    API.fire(this, 'beforeShow')\n\n    this.showing = true\n\n    if (this.closing) {\n      this.showing = false\n      return this\n    }\n\n    API.build(this)\n    API.handleModal(this)\n\n    if (this.options.force) {\n      this.layoutDom.insertBefore(this.barDom, this.layoutDom.firstChild)\n    } else {\n      this.layoutDom.appendChild(this.barDom)\n    }\n\n    if (\n      this.hasSound &&\n      !this.soundPlayed &&\n      Utils.inArray('docVisible', this.options.sounds.conditions)\n    ) {\n      Utils.createAudioElements(this)\n    }\n\n    if (Utils.inArray('docVisible', this.options.titleCount.conditions)) {\n      API.docTitle.increment()\n    }\n\n    this.shown = true\n    this.closed = false\n\n    // bind button events if any\n    if (API.hasButtons(this)) {\n      Object.keys(this.options.buttons).forEach(key => {\n        const btn = this.barDom.querySelector(\n          `#${this.options.buttons[key].id}`\n        )\n        Utils.addListener(btn, 'click', e => {\n          Utils.stopPropagation(e)\n          this.options.buttons[key].cb(this)\n        })\n      })\n    }\n\n    this.progressDom = this.barDom.querySelector('.noty_progressbar')\n\n    if (Utils.inArray('click', this.options.closeWith)) {\n      Utils.addClass(this.barDom, 'noty_close_with_click')\n      Utils.addListener(\n        this.barDom,\n        'click',\n        e => {\n          Utils.stopPropagation(e)\n          API.fire(this, 'onClick')\n          this.close()\n        },\n        false\n      )\n    }\n\n    Utils.addListener(\n      this.barDom,\n      'mouseenter',\n      () => {\n        API.fire(this, 'onHover')\n      },\n      false\n    )\n\n    if (this.options.timeout) Utils.addClass(this.barDom, 'noty_has_timeout')\n    if (this.options.progressBar) {\n      Utils.addClass(this.barDom, 'noty_has_progressbar')\n    }\n\n    if (Utils.inArray('button', this.options.closeWith)) {\n      Utils.addClass(this.barDom, 'noty_close_with_button')\n\n      const closeButton = document.createElement('div')\n      Utils.addClass(closeButton, 'noty_close_button')\n      closeButton.innerHTML = '×'\n      this.barDom.appendChild(closeButton)\n\n      Utils.addListener(\n        closeButton,\n        'click',\n        e => {\n          Utils.stopPropagation(e)\n          this.close()\n        },\n        false\n      )\n    }\n\n    API.fire(this, 'onShow')\n\n    if (this.options.animation.open === null) {\n      this.promises.show = new Promise(resolve => {\n        resolve()\n      })\n    } else if (typeof this.options.animation.open === 'function') {\n      this.promises.show = new Promise(this.options.animation.open.bind(this))\n    } else {\n      Utils.addClass(this.barDom, this.options.animation.open)\n      this.promises.show = new Promise(resolve => {\n        Utils.addListener(this.barDom, Utils.animationEndEvents, () => {\n          Utils.removeClass(this.barDom, this.options.animation.open)\n          resolve()\n        })\n      })\n    }\n\n    this.promises.show.then(() => {\n      const _t = this\n      setTimeout(\n        () => {\n          API.openFlow(_t)\n        },\n        100\n      )\n    })\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  stop () {\n    API.dequeueClose(this)\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  resume () {\n    API.queueClose(this)\n    return this\n  }\n\n  /**\n   * @param {int|boolean} ms\n   * @return {Noty}\n   */\n  setTimeout (ms) {\n    this.stop()\n    this.options.timeout = ms\n\n    if (this.barDom) {\n      if (this.options.timeout) {\n        Utils.addClass(this.barDom, 'noty_has_timeout')\n      } else {\n        Utils.removeClass(this.barDom, 'noty_has_timeout')\n      }\n\n      const _t = this\n      setTimeout(\n        function () {\n          // ugly fix for progressbar display bug\n          _t.resume()\n        },\n        100\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * @param {string} html\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setText (html, optionsOverride = false) {\n    if (this.barDom) {\n      this.barDom.querySelector('.noty_body').innerHTML = html\n    }\n\n    if (optionsOverride) this.options.text = html\n\n    return this\n  }\n\n  /**\n   * @param {string} type\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setType (type, optionsOverride = false) {\n    if (this.barDom) {\n      let classList = Utils.classList(this.barDom).split(' ')\n\n      classList.forEach(c => {\n        if (c.substring(0, 11) === 'noty_type__') {\n          Utils.removeClass(this.barDom, c)\n        }\n      })\n\n      Utils.addClass(this.barDom, `noty_type__${type}`)\n    }\n\n    if (optionsOverride) this.options.type = type\n\n    return this\n  }\n\n  /**\n   * @param {string} theme\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setTheme (theme, optionsOverride = false) {\n    if (this.barDom) {\n      let classList = Utils.classList(this.barDom).split(' ')\n\n      classList.forEach(c => {\n        if (c.substring(0, 12) === 'noty_theme__') {\n          Utils.removeClass(this.barDom, c)\n        }\n      })\n\n      Utils.addClass(this.barDom, `noty_theme__${theme}`)\n    }\n\n    if (optionsOverride) this.options.theme = theme\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  close () {\n    if (this.closed) return this\n\n    if (!this.shown) {\n      // it's in the queue\n      API.removeFromQueue(this)\n      return this\n    }\n\n    API.fire(this, 'onClose')\n\n    this.closing = true\n\n    if (this.options.animation.close === null || this.options.animation.close === false) {\n      this.promises.close = new Promise(resolve => {\n        resolve()\n      })\n    } else if (typeof this.options.animation.close === 'function') {\n      this.promises.close = new Promise(\n        this.options.animation.close.bind(this)\n      )\n    } else {\n      Utils.addClass(this.barDom, this.options.animation.close)\n      this.promises.close = new Promise(resolve => {\n        Utils.addListener(this.barDom, Utils.animationEndEvents, () => {\n          if (this.options.force) {\n            Utils.remove(this.barDom)\n          } else {\n            API.ghostFix(this)\n          }\n          resolve()\n        })\n      })\n    }\n\n    this.promises.close.then(() => {\n      API.closeFlow(this)\n      API.handleModalClose(this)\n    })\n\n    this.closed = true\n\n    return this\n  }\n\n  // API functions\n\n  /**\n   * @param {boolean|string} queueName\n   * @return {Noty}\n   */\n  static closeAll (queueName = false) {\n    Object.keys(API.Store).forEach(id => {\n      if (queueName) {\n        if (\n          API.Store[id].options.queue === queueName && API.Store[id].killable\n        ) {\n          API.Store[id].close()\n        }\n      } else if (API.Store[id].killable) {\n        API.Store[id].close()\n      }\n    })\n    return this\n  }\n\n  /**\n   * @param {string} queueName\n   * @return {Noty}\n   */\n  static clearQueue (queueName = 'global') {\n    if (API.Queues.hasOwnProperty(queueName)) {\n      API.Queues[queueName].queue = []\n    }\n    return this\n  }\n\n  /**\n   * @return {API.Queues}\n   */\n  static get Queues () {\n    return API.Queues\n  }\n\n  /**\n   * @return {API.PageHidden}\n   */\n  static get PageHidden () {\n    return API.PageHidden\n  }\n\n  /**\n   * @param {Object} obj\n   * @return {Noty}\n   */\n  static overrideDefaults (obj) {\n    API.Defaults = Utils.deepExtend({}, API.Defaults, obj)\n    return this\n  }\n\n  /**\n   * @param {int} amount\n   * @param {string} queueName\n   * @return {Noty}\n   */\n  static setMaxVisible (amount = API.DefaultMaxVisible, queueName = 'global') {\n    if (!API.Queues.hasOwnProperty(queueName)) {\n      API.Queues[queueName] = {maxVisible: amount, queue: []}\n    }\n\n    API.Queues[queueName].maxVisible = amount\n    return this\n  }\n\n  /**\n   * @param {string} innerHtml\n   * @param {String} classes\n   * @param {Function} cb\n   * @param {Object} attributes\n   * @return {NotyButton}\n   */\n  static button (innerHtml, classes = null, cb, attributes = {}) {\n    return new NotyButton(innerHtml, classes, cb, attributes)\n  }\n\n  /**\n   * @return {string}\n   */\n  static version () {\n    return VERSION\n  }\n\n  /**\n   * @param {String} workerPath\n   * @return {Push}\n   */\n  static Push (workerPath) {\n    return new Push(workerPath)\n  }\n}\n\n// Document visibility change controller\nif (typeof window !== 'undefined') {\n  Utils.visibilityChangeFlow()\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.js", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 7\n// module chunks = 0", "var g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// (webpack)/buildin/global.js\n// module id = 8\n// module chunks = 0", "/* (ignored) */\n\n\n//////////////////\n// WEBPACK FOOTER\n// vertx (ignored)\n// module id = 9\n// module chunks = 0"], "sourceRoot": ""}