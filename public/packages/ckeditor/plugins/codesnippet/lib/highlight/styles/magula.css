/*
Description: Magula style for highligh.js
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Website: http://rukeba.com/
Version: 1.0
Date: 2009-01-03
Music: Aphex Twin / Xtal
*/

.hljs {
  display: block; padding: 0.5em;
  background-color: #f4f4f4;
}

.hljs,
.hljs-subst,
.lisp .hljs-title,
.clojure .hljs-built_in {
  color: black;
}

.hljs-string,
.hljs-title,
.hljs-parent,
.hljs-tag .hljs-value,
.hljs-rules .hljs-value,
.hljs-rules .hljs-value .hljs-number,
.hljs-preprocessor,
.hljs-pragma,
.ruby .hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-aggregate,
.hljs-template_tag,
.django .hljs-variable,
.smalltalk .hljs-class,
.hljs-addition,
.hljs-flow,
.hljs-stream,
.bash .hljs-variable,
.apache .hljs-cbracket,
.coffeescript .hljs-attribute {
  color: #050;
}

.hljs-comment,
.hljs-annotation,
.hljs-template_comment,
.diff .hljs-header,
.hljs-chunk {
  color: #777;
}

.hljs-number,
.hljs-date,
.hljs-regexp,
.hljs-literal,
.smalltalk .hljs-symbol,
.smalltalk .hljs-char,
.hljs-change,
.tex .hljs-special {
  color: #800;
}

.hljs-label,
.hljs-javadoc,
.ruby .hljs-string,
.hljs-decorator,
.hljs-filter .hljs-argument,
.hljs-localvars,
.hljs-array,
.hljs-attr_selector,
.hljs-pseudo,
.hljs-pi,
.hljs-doctype,
.hljs-deletion,
.hljs-envvar,
.hljs-shebang,
.apache .hljs-sqbracket,
.nginx .hljs-built_in,
.tex .hljs-formula,
.hljs-prompt,
.clojure .hljs-attribute {
  color: #00e;
}

.hljs-keyword,
.hljs-id,
.hljs-phpdoc,
.hljs-title,
.hljs-built_in,
.hljs-aggregate,
.smalltalk .hljs-class,
.hljs-winutils,
.bash .hljs-variable,
.apache .hljs-tag,
.xml .hljs-tag,
.tex .hljs-command,
.hljs-request,
.hljs-status {
  font-weight: bold;
  color: navy;
}

.nginx .hljs-built_in {
  font-weight: normal;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}

/* --- */
.apache .hljs-tag {
  font-weight: bold;
  color: blue;
}
