let btnSave = document.querySelectorAll('#saveActions .submit-btn-save-action');

const validate = [
    {field: 'title', title: 'título', error: 'Escribe un título que resuma la solicitud'},
    {field: 'building_id_select', title: 'edificio', error: 'Elige un edificio'},
    {field: 'flat_id_select', title: 'apto', error: 'Elige un apartamento'},
    {field: 'select-case-category', title: 'categoria', error: 'Elige una categoría para este caso'},
];

// Loop through each button and add the event listener, excluding the button with id "bpSaveButtonsGroup"
btnSave.forEach(button => {

    button.addEventListener('click', (event) => {
        let isEmpty = false;
        for (let i = 0; i < validate.length; i++) {
            $('.error-message-' + validate[i].field).remove();
            if ($('#' + validate[i].field).val() == '' || $('#' + validate[i].field).val() == null) {
                const errorElement = createErrorElement(validate[i].field, validate[i].error);
                const $closestColDiv = $('#' + validate[i].field).closest('[class*="col-md-"]');
                $closestColDiv.append(errorElement);
                isEmpty = true;
            }
        }
        if (isEmpty) {
            event.preventDefault();
            event.stopPropagation();
            new Noty({
                type: "error",
                text: 'Completa los campos requeridos para guardar el caso',
            }).show();
        }
    });

});


function createErrorElement(field, error) {
    const errorElement = document.createElement('div');
    errorElement.classList.add(`error-message-${field}`);
    errorElement.style.display = 'flex';
    errorElement.style.alignItems = 'center';
    errorElement.innerHTML = `
        <ion-icon name='information-circle-outline' style='color:red!important'></ion-icon>
        <p style='margin:0;color:red !important'> ${error}.</p>
    `;
    return errorElement;
}


let titleField = document.getElementById('title');

titleField.addEventListener('keyup', (event) => {
    let titleError = document.querySelector('.error-message-title');
    if (titleField.value) {
        titleError && titleError.remove();
    }
});


function deleteSelectError(value, fieldClass) {
    let titleError = document.querySelector('.error-message-' + fieldClass);
    if (value != '') {
        titleError && titleError.remove();
    }
}
