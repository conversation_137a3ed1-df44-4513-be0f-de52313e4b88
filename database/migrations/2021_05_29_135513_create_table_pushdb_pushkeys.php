<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTablePushdbPushkeys extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pushdb_pushkeys', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('p_info');
            $table->text('p_extension_number');
            $table->text('p_device');
            $table->string('p_type')->nullable();
            $table->string('p_status')->nullable();
            $table->string('p_updated')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pushdb_pushkeys');
    }
}
