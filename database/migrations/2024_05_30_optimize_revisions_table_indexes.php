<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::table('revisions', function (Blueprint $table) {

            $table->index(['revisionable_type', 'revisionable_id', 'created_at'], 'revisions_optimized_query');

            $table->index(['user_id', 'created_at'], 'revisions_user_date');

            $table->index('created_at', 'revisions_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('revisions', function (Blueprint $table) {
            $table->dropIndex('revisions_optimized_query');
            $table->dropIndex('revisions_user_date');
            $table->dropIndex('revisions_created_at');
        });
    }
};
