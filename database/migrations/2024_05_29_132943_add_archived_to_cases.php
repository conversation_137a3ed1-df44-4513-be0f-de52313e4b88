<?php

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasColumn('cases', 'archived')) {
            Schema::table('cases', function (Blueprint $table) {
                $table->boolean('archived')->default(false);
            });
        }

        \DB::update("update cases set end_date = updated_at where state = 'finalizado' and end_date is null");
        \DB::update("update cases set archived = true where state = 'finalizado' and end_date < ?", [now()->subMonth()]);
    }

    public function down(): void
    {
        if (Schema::hasColumn('cases', 'archived')) {
            Schema::table('cases', function (Blueprint $table) {
                $table->dropColumn('archived');
            });
        }
    }
};
