<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterBuildingsTableAddOperatorId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->bigInteger('operator_id')->unsigned()->nullable();
            $table->foreign('operator_id')->references('id')->on('users');

            $table->bigInteger('administration_id')->unsigned()->nullable();
            $table->foreign('administration_id')->references('id')->on('companies');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->dropForeign('buildings_operator_id_foreign');
            $table->dropColumn('operator_id');
            $table->dropForeign('buildings_administration_id_foreign');
            $table->dropColumn('administration_id');
        });
    }
}
