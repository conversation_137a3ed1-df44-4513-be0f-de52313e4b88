<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeStyleDataInUserBuildingServicesAndAdmin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $buildings = \App\Models\Building::all();
        foreach ($buildings as $building){
            $building->between_streets = ucwords(mb_strtolower($building->between_streets));
            $building->name = ucwords(mb_strtolower($building->name));
            $building->building_type = ucwords(mb_strtolower($building->building_type)); //REVISAR
            if($building->service_type == 'PORTERÍA REMOTA'){
                $building->service_type = 'Porteria Remota';
            }
            else{
                $building->service_type = ucwords(mb_strtolower($building->service_type)); //REVISAR
            }
            $building->save();
        }

        $users = \App\Models\User\User::all();
        foreach ($users as $user){
            $user->name = ucwords(mb_strtolower($user->name));
            $user->surname = ucwords(mb_strtolower($user->surname));
            $user->complete_name = ucwords(mb_strtolower($user->complete_name));
            $user->save();
        }

        $admins = \App\Models\Company\Administration::all();
        foreach ($admins as $admin){
            $admin->name = ucwords(mb_strtolower($admin->name));
            $admin->address = ucwords(mb_strtolower($admin->address));
            $admin->save();
        }

        $services = \App\Models\Service::all();
        foreach ($services as $service){
            $service->service = ucwords(mb_strtolower($service->service));
            $service->provider = ucwords(mb_strtolower($service->provider));
            $service->save();
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
