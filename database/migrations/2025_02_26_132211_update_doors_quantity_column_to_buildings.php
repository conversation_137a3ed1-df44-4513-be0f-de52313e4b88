<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        DB::update('update buildings set doors_quantity = 1 where doors_quantity is null');

        Schema::table('buildings', function (Blueprint $table) {
            $table->integer('doors_quantity')->default(1)->change();
        });
    }
};
