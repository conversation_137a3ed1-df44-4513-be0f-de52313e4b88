<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if(!Schema::hasColumns('buildings', ['records_comments', 'tags_comments'])) {
            Schema::table('buildings', function (Blueprint $table) {
                $table->json('records_comments')->nullable();
                $table->json('tags_comments')->nullable();
                $table->json('investigations_comments')->nullable();
                $table->json('others_comments')->nullable();
            });
        }
    }

    public function down(): void
    {
        if(Schema::hasColumn('buildings', 'records_comments')) {
            Schema::table('buildings', function (Blueprint $table) {
                $table->dropColumn('records_comments', 'tags_comments', 'investigations_comments', 'others_comments');
            });
        }
    }
};
