<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Models\BuildingIntercom::whereNotNull('deleted_at')->forceDelete();
        $buildingIntercomList = \App\Models\BuildingIntercom::all();
        $intercom2N = \App\Models\IntercomModel::query()->where('model', '2N')->first();
        foreach ($buildingIntercomList as $intercom) {
            try {
                if (is_null($intercom->model) || trim($intercom->model) == "") {
                    $intercom->intercom_type = $intercom2N?->id;
                    $intercom->save();
                    continue;
                }

                $intercomModel = \App\Models\IntercomModel::where('model', trim(strtoupper($intercom->model)))->first();

                $intercom->intercom_type = $intercomModel?->id ?? $intercom2N?->id;
                $intercom->save();
            } catch (\Exception $e) {
                dd($intercomModel, $intercom, $e);
            }
        }
        Schema::table('building_intercoms', function (Blueprint $table) {
            $table->unsignedBigInteger('intercom_type')->change();
            $table->foreign('intercom_type')->references('id')->on('intercom_models');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};
