<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if(!Schema::hasColumn('building_contact', 'name_kazoo')) {
            Schema::table('building_contact', function (Blueprint $table) {
                $table->string('name_kazoo')->nullable();
                $table->string('user_sip_kazoo')->nullable();
                $table->string('password_kazoo')->nullable();
            });
        }
    }

    public function down(): void
    {
        if(Schema::hasColumn('building_contact', 'name_kazoo')) {
            Schema::table('building_contact', function (Blueprint $table) {
                $table->dropColumn(['name_kazoo', 'user_sip_kazoo', 'password_kazoo']);
            });
        }
    }
};
