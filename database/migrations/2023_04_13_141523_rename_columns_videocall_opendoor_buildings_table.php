<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameColumnsVideocallOpendoorBuildingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->renameColumn('videocall_disabled', 'open_door_disabled');
            $table->renameColumn('video_call_hability', 'video_disabled');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('buildings', function (Blueprint $table) {
            $table->renameColumn('open_door_disabled', 'videocall_disabled');
            $table->renameColumn('video_disabled', 'video_call_hability');
        });
    }
};
