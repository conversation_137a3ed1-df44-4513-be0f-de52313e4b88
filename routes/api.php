<?php

use App\Http\Controllers\Api\BuildingController;
use App\Http\Controllers\KazooController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', 'Api\UserController@AuthRouteAPI');


Route::group([
    'middleware' => 'ip_check',
], function () { // custom api routes under ip check middleware
    Route::get('/cliente/numero={numero}', 'Api\UserController@getInfoByPhone');
    Route::post('/contacto/', 'Api\UserController@storeContact');
    Route::post('/contacto-temporal/', 'Api\UserController@storeTemporaryContact');
    Route::get('/contact/info/{number}', 'Api\ContactController@info');
    //WittyBots API endpints
    Route::get('/temporary-contact/authorized/wittybots/{number}', 'Api\WittyBotsController@isAuthorized'); //Numero
    Route::post('/temporary-contact/wittybots', 'Api\WittyBotsController@storeTemporaryContact'); //Numero, nombre, descripcion aceptar que no ea obligatorio el nombre
    Route::put('/cases/close/{user_phone}', 'Api\WittyBotsController@closeCase'); // crear uid para comunicacion | tantas letras tantos numeros


});

Route::middleware([])->group(function ($router) {
    Route::post('register', 'AuthController@register')->name('register');
    Route::post('login', 'AuthController@login')->name('login');
    Route::post('logout', 'AuthController@logout');
    Route::post('refresh', 'AuthController@refresh');
    Route::post('me', 'AuthController@me');
    Route::get('verify-contact/{id}', 'AuthController@checkContactLoginHability');

    //    Route::get('type_contact', 'Api\TypeContactController@index');

    Route::get('/building/info/{id}', 'Api\BuildingController@buildingInfo');
    Route::get('/user/{id}/cases', 'Api\CaseController@userCases');
    Route::get('/user/{email}', 'Api\UserController@checkEmailExists');

    Route::get('/intercom/{id}/open', 'Api\IntercomController@open');
    Route::get('/intercom/{id}/open/{userId}', 'Api\IntercomController@open');
    Route::get('/intercom/{extension}/open-by-3cx/{userId}', 'Api\IntercomController@openBy3cx');


    Route::get('/register-app/email={email}', 'AuthController@registerApp');
    Route::get('/unregister/{id}', 'AuthController@unregister');


    Route::get('/privacy-policy', 'Api\UserController@showPrivacPolicy');

    Route::get('/verificate-user-test', 'Api\UserController@verificateUserTest');
    Route::get('/verificate-user-test-is-ok', 'Api\UserController@verificateUserTestIsOk');


    Route::get('/flats/duplicate/{ci}/{user_id}', 'Api\UserController@ciExistsInUsersFlat');
    Route::get('/flats/duplicate/{ci}/flat/{flat_id}', 'Api\UserController@ciExistsInFlat');
    Route::get('/flats/duplicate/{ci}/flat/{flat_id}/contact/{contact_id}', 'Api\UserController@ciExistsInFlat');
    Route::get('/building/all', 'Api\BuildingController@getAllBuildings');
});

Route::group([
    'middleware' => ['auth:api', 'has_access'],
    'namespace' => 'App',
], function () {

    //  Residents //
    Route::post('/resident', 'ResidentsAppController@store');
    Route::get('/residents', 'ResidentsAppController@list');
    Route::put('/resident/{id}', 'ResidentsAppController@update');
    Route::delete('/resident/{id}', 'ResidentsAppController@delete');

    Route::post('/resident/{id}/last-auto-login', 'ResidentsAppController@updateLastAutoLogin');

    //  End Residents //


    //  Authorizations //
    Route::get('/authorizations', 'AuthorizationsAppController@authorizations');
    Route::put('/authorization/{id}', 'AuthorizationsAppController@update');
    Route::delete('/authorization/{id}', 'AuthorizationsAppController@delete');
    Route::post('/authorization', 'AuthorizationsAppController@store');

    //  End Authorizations //

    //  Temporal Permits //
    Route::post('/temporal-permit', 'TemporalPermitAppController@store');
    Route::get('/temporal-permits/', 'TemporalPermitAppController@list');
    Route::put('/temporal-permit/{id}', 'TemporalPermitAppController@update');
    Route::delete('/temporal-permit/{id}', 'TemporalPermitAppController@delete');
    //  End Temporal Permits //

    //  Cases //
    Route::post('/case', 'CasesAppController@store');
    Route::get('/cases', 'CasesAppController@list');
    Route::put('/cases/{id}', 'CasesAppController@update');
    Route::delete('/cases/{id}', 'CasesAppController@delete');
    //    End Cases //
    Route::get('/category/app', 'CasesAppController@getAppCategories');

    Route::get('/name/{id}', 'ContactAppController@contactName');
    Route::get('/contact/{id}/authorizations', 'ContactAppController@authorizations');
    Route::get('/contact/{id}/residents', 'ContactAppController@residents');

    //  Log //
    Route::post('/log', 'LogsController@store');
    Route::get('/logs', 'LogsController@list');
    Route::put('/log/{id}', 'LogsController@update');
    Route::delete('/log/{id}', 'LogsController@delete');
    //  End Log //
    Route::put('/store-videocall-supported/{id}', 'ResidentsAppController@storeVideoCallSupported');

});

Route::group([
    'namespace' => 'Api',
], function () {
    //  Log //
    Route::post('/log', 'LogsController@store');
    Route::get('/logs', 'LogsController@list');
    Route::put('/log/{id}', 'LogsController@update');
    Route::delete('/log/{id}', 'LogsController@delete');

    Route::get('/building-flats/{id}', 'BuildingController@searchAllFlatsByBuilding');

    //  End Log //

});


Route::get('/notificate/{callerNumber}/show/{type}', 'Api\NotificationController@show');

Route::get('/building/{building_id}/pull-logs', 'Admin\BuildingLogsCrudController@pullLogs');
Route::get('/test-print/key={key}', 'Api\UserController@testPrint');
Route::get('/send-asterisk-down-mail/', 'Api\UserController@sendAsteriskDownMail');

/*Códigos de Accesso Wittybots*/

Route::get('/user/generate-access-code/{phoneNumber}/', 'Api\AccessCodeController@createNewAccessCode');
Route::get('/user/building-has-keyboard/{phoneNumber}/', 'Api\UserController@buildingHasKeyboardForWittyBots');




Route::delete('/user/delete-access-code/{userId}/{code}/{building_id}', 'Api\AccessCodeController@deleteAccessCode');

/* Intercoms user manipulation  */
Route::get('/intercom/users/2n/{ip}', 'Api\IntercomController@getUsers2n');
Route::get('/user/info/ext={extension}', 'Api\UserController@getInfoByExtension');


Route::post('/transferred-calls/', 'Api\TransferredCallsController@callTransfer');
Route::post('/store-transferred-calls/', 'Api\TransferredCallsController@storeCallLogs');
Route::get('/transferred-calls/{id_3xc}', 'Api\TransferredCallsController@getLastInteractionTransferredCallsByOperator');
Route::get('/building-status-and-planned-date/{building_number}', 'Api\BuildingController@getBuildingStatusAndPlannedDate');

///*Códigos de acceso desde el show contacto*/
Route::post('/user/access-code', 'Api\AccessCodeController@generateAccessCode');
Route::get('/user/access-codes/{user_id}', 'Api\AccessCodeController@getAccessCodes');
Route::delete('/user/{user_id}/access-code/{code}', 'Api\AccessCodeController@removeAccessCode');

Route::get('/building', 'Api\BuildingController@getAllBuilding');
Route::post('/store-transferred-calls/', 'Api\TransferredCallsController@storeCallLogs');

// Kazoo
// Accounts

Route::get('/kazoo/accounts/{accountId}', [KazooController::class, 'getAccount']);
Route::post('/kazoo/accounts', [KazooController::class, 'createAccount']);
Route::put('/kazoo/accounts/{accountId}', [KazooController::class, 'updateAccount']);
    //Devices
Route::post('/kazoo/intercoms', [KazooController::class, 'createIntercomData']);
Route::get('/building/{id}/intercom_name/{intercom_name}', [BuildingController::class, 'existIntercomName']);
