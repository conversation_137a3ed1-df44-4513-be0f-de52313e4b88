<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Resources\BuildingCollecction as BuildingResource;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Caseq;
use App\Models\Category;
use App\Models\Notification;
use App\Models\User\Contact;
use Carbon\Carbon;
use \Laravel\Socialite\Facades\Socialite;
use \App\Models\Car;
use \App\Http\Controllers\Admin\BuildingCrudController;

Route::get('/', ['middleware' => 'is_admin', function () {
    return redirect('/admin');
}]);

Route::get('/admin/dashboard', ['middleware' => 'is_admin', function () {
    return redirect('/admin/index');
}]);

Route::group([
    'prefix' => 'admin/',
    'middleware' => ['web', 'admin', 'is_admin'],
    'namespace' => 'Admin',
], function () {
    Route::get('/index', 'BuildingCrudController@buildingsIndex');
    Route::get('/buildings-index', 'BuildingCrudController@buildingsIndex');
    Route::post('/active-hours/save', 'BuildingActiveHoursCrudController@store');
    Route::post('/active-hours/{id}/update', 'BuildingActiveHoursCrudController@update');
    Route::get('/temporary_contact/reformatAll/', 'TemporaryContactCrudController@reformatTemporaryContacts');
    Route::get('/contact-show/name={name}&phone={phone}&address={address}', 'ContactCrudController@showContact');
    Route::get('/current-user-id', 'UserCrudController@currentId');
    Route::get('/cars', [Car::class, 'getCars'])->name('cars');
});


Route::group([
    'prefix' => 'admin/building',
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin')],
    'namespace' => 'Admin',
], function () { // custom admin routes
    Route::get('logs/{id}', 'BuildingLogsCrudController@setupListOperation');

    Route::get('/delete/{id}', 'BuildingCrudController@delete');

    Route::get('/info/{id}', 'BuildingCrudController@buildingInfo');
    Route::get('/info_of/{name}', 'BuildingCrudController@buildingInfoByName');
    Route::get('/info_of/building/{id}', 'BuildingCrudController@buildingInfoById');
    Route::get('/is-active/{id}', 'BuildingCrudController@isActive');
    Route::get('/initialize-has-active-hours', 'BuildingCrudController@initializeBuildingsActiveHours');


    Route::get('/contacts/info/{id}/minimized-contacts-for-dashboard', 'BuildingCrudController@contactsMinimizedInDashboard');

    Route::get('/contacts/info/{id}/minimized-contacts', 'BuildingCrudController@buildingSortedContactsMinimized');
    Route::get('/contacts/verificate/info/{id}', 'BuildingCrudController@buildingSortedContactsVerificate');
    Route::get('/contacts/verificate/cadete/info/{id}', 'BuildingCrudController@buildingSortedContactsVerificateCadete');
    Route::get('/contacts/comision/info/{id}', 'BuildingCrudController@buildingSortedContactsComision');
    Route::get('/contacts/authorizations/info/{id}', 'BuildingCrudController@buildingSortedContactsAuthorizations');
    Route::get('/contacts/referrer/info/{id}', 'BuildingCrudController@buildingSortedContactsReferrer');
    Route::get('/contacts/temporary/info/{id}', 'BuildingCrudController@buildingSortedContactsAuthorizationsTemporary');
    Route::get('/contacts-by-type/info/{id}/{type}', 'BuildingCrudController@buildingContactsByType');

    Route::get('/temporary-contacts/info/{id}', 'BuildingCrudController@buildingTemporaryContactsInfo');
    Route::get('/services/info/{id}', 'BuildingCrudController@buildingServicesInfo');
    Route::get('/services/by-type/{id}/{type}', 'BuildingCrudController@buildingServicesInfoByType');
    Route::get('/services/not-by-types/{id}/{types}', 'BuildingCrudController@buildingServicesInfoNotByTypes');

    Route::get('/services/info/{id}/minimized-services', 'BuildingCrudController@buildingServicesInfoMinimized');

    Route::get('/is-active', 'BuildingCrudController@isActive');

    Route::get('/{id}/flats', 'BuildingCrudController@showFlats');

    Route::get('/building/{id}/restore', 'BuildingCrudController@restore');

    Route::post('/contact/edit/las/interaction/{id}', 'ContactCrudController@editLastInteractionInShow');
    Route::get('/contact/{relation_id}/restore', 'ContactCrudController@restore');
    Route::get('/contact/{id}/flat/{flat_id}/other-type-of-contact', 'ContactCrudController@getOtherTypeOfContact');
    Route::get('/flat/{id}/contacts', 'FlatCrudController@showContacts');

    Route::get('all', [BuildingCrudController::class, 'getBuildings'])->name('building.all');
    Route::get('all-info', [BuildingCrudController::class, 'getBuildingsInfo'])->name('building.all.info');

});


Route::group([
    'prefix' => 'admin/flats',
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin'), 'is_admin'],
    'namespace' => 'Admin',
], function () { // custom admin routes
    Route::get('/rearrange', 'FlatCrudController@rearrangeFlats');
    Route::get('/clean', 'FlatCrudController@cleanFlats');
    Route::get('/info/{id}', 'FlatCrudController@flatInfo');
    Route::get('/duplicate/{id}/{building_id}', 'FlatCrudController@duplicateFlat');
});


Route::group([
    'prefix' => 'admin/users',
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin'), 'is_admin'],
    'namespace' => 'Admin',
], function () { // custom admin routes
    Route::get('/reformatAll/', 'UserCrudController@reformatUsers');
    Route::get('/reformatPhones/', 'UserCrudController@reformatPhones');
    Route::get('/reformatNames/', 'UserCrudController@reformatNames');
    Route::get('/reformatDocuments/', 'UserCrudController@reformatDocuments');

});


Route::group([
    'prefix' => 'admin/contact',
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin'), 'is_admin'],
    'namespace' => 'Admin',
], function () { // custom admin routes
    Route::get('/{id}/restore', 'ContactCrudController@restore');
    Route::get('/all', 'ContactCrudController@list');
    Route::get('/residents', 'ContactCrudController@listResidents');
    Route::get('/{id}', 'ContactCrudController@getContact');
    Route::get('/ci/{ci}', 'ContactCrudController@getContactCi');
});

//Route::get('/api/type_contact', 'Api\TypeContactController@index');

//Email
//Route::post('/send-to-comercial', 'EmailController@contact');
//Ruta que esta señalando nuestro formulario
//EndEmail


/////////////////////////
//   EXCEL IMPORTERS   //
/////////////////////////

Route::group([
    'prefix' => 'admin/',
    'middleware' => ['web', 'admin', 'is_admin'],
    'namespace' => 'Importers',
], function () {
    Route::get('/administrators/upload', 'ImportExcelAdministratorsController@index');
    Route::get('/buildings/upload', 'ImportExcelBuildingsController@index');
    Route::get('/contacts/upload', 'ImportExcelContactsController@index');
    Route::get('/vehicles/upload', 'ImportExcelVehiclesController@index');
    Route::get('/contacts-only-by-name/upload', 'ImportExcelContactsOnlyByNameController@index');
    Route::get('/services/upload', 'ImportExcelServicesController@index');
    Route::get('/building_active_hours/upload', 'ImportExcelBuildingActiveHoursController@index');
    Route::get('/temporary_contact/upload', 'ImportExcelTemporaryContactController@index');

    Route::post('/import_excel/administrators', 'ImportExcelAdministratorsController@import');
    Route::post('/import_excel/buildings', 'ImportExcelBuildingsController@import');
    Route::post('/import_excel/contacts', 'ImportExcelContactsController@import');
    Route::post('/import_excel/vehicles', 'ImportExcelVehiclesController@import');
    Route::post('/import_excel/contacts-only-by-name', 'ImportExcelContactsOnlyByNameController@import');
    Route::post('/import_excel/services', 'ImportExcelServicesController@import');
    Route::post('/import_excel/building_active_hours', 'ImportExcelBuildingActiveHoursController@import');
    Route::post('/import_excel/temporary_contact', 'ImportExcelTemporaryContactController@import');


    Route::get('/contacts/updateBuildings', 'ImportExcelContactsController@updateContactsBuildingIds');
    Route::get('/contacts/deleteAll/', 'ImportExcelContactsController@deleteAllContacts');
    Route::get('/services/deleteAll/', 'ImportExcelServicesController@deleteAllServices');
    Route::get('/buildings/deleteAll/', 'ImportExcelBuildingsController@deleteAllBuildings');

    Route::get('/tag/upload/', 'ImportTagController@index');
    Route::post('/tag/import/', 'ImportTagController@import');
});
/////////////////////////
// END EXCEL IMPORTERS //
/////////////////////////

//Contact registration APP
Route::group([
    'prefix' => 'app/contact',
    'middleware' => [], //Todo ver middelware de la APP 'ip_check'
    'namespace' => 'App',
], function () { // custom admin routes
    Route::get('/', 'ContactAppController@list');
    Route::post('/', 'ContactAppController@store');
    Route::post('/update/{id}', 'ContactAppController@update');
//    Route::get('/create', 'ContactAppController@create');
    Route::post('/store', 'ContactAppController@store');
    Route::post('/unregister/{id}', 'ContactAppController@destroy');


    Route::get('/update/token={token}', 'ContactAppController@edit');
    Route::get('/register/email={email}', 'ContactAppController@register');

    Route::get('/show-registration-mail', 'ContactAppController@showRegistrationMailView');

    Route::post('/send-registration-mail/', 'ContactAppController@sendRegistrationMail');

    Route::get('/update-password/', 'ContactAppController@updatePassword');

    Route::get('/password-reset/', 'ContactAppController@passwordReset');

//    Route::get('/authorization/{id}', 'ContactAppController@authorizations');
});
//End contact registration APP

//General rutes
Route::group([
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin')],
], function () { // custom admin routes
    Route::get('/admin/contact/{id}/info', 'Admin\ContactCrudController@showInfo');
    Route::get('/admin/contact/{id}/authorized/flat/{flat_id}', 'Admin\ContactCrudController@getAuthorizedTime');
    Route::get('/admin/contact/{id}/schedule/flat/{flat_id}', 'Admin\ContactCrudController@getSchedule');
    Route::post('/admin/contact/{id}/schedule/flat/{flat_id}', 'Admin\ContactCrudController@saveSchedule');
    Route::get('/admin/contact/phone/{phone}/info', 'Admin\ContactCrudController@showInfoByPhone');
    Route::get('/admin/contact/{id}/deleteInfo', 'Admin\ContactCrudController@deleteInInfo');

    Route::get('/admin/building/{id}/info', 'Admin\BuildingCrudController@showInfo');
    Route::get('/admin/building/{id}/info-with-building-number', 'Admin\BuildingCrudController@showInfoWithNumber');

    Route::get('/notificate/{callerNumber}/open-operator/', 'Api\NotificationController@openOperatorTab');


    //Downloads
    Route::get('/download_job/{id}', 'Download\DownloadController@downloadJob');
    Route::get('/download_job_checked/{id}', 'Download\DownloadController@downloadJobChecked');
    Route::get('/download_contract/{id}', 'Download\DownloadController@downloadContract');
    Route::get('/download_security/{id}', 'Download\DownloadController@downloadSecurity');

    //Formulario de Edificios prospectos
    Route::get('/form-lead-buildings', 'Forms\LeadBuildingsFormController@index');
    Route::post('/form-lead-buildings', 'Forms\LeadBuildingsFormController@store');
    Route::get('/service/info/{type}', 'Api\ServiceNameController@serviceName');
    Route::get('/admin/building/info/{name}/{edificio}', 'Api\AdminController@adminId');

    //Ver todas las notificaciones
    Route::get('admin/all_notify/{user_id}', 'Admin\UserCrudController@viewAllNotify');
    //End formulario

    //Verificar contacto
    Route::get('admin/contact/{id}/verificate/{acepter_id}', 'Admin\ContactCrudController@verificateContact');
    //End verificar

    //Create iteraction
    Route::get('/createiteractionlost/{id}', 'Admin\RecentlyInteractionCrudController@interactionLost');
    Route::get('/createiteractioncall/{id}/{tel}/{comment?}', 'Admin\RecentlyInteractionCrudController@interactionCall');
    Route::get('/createiteractioncallPorter/{mail}/{tel}', 'Admin\RecentlyInteractionCrudController@interactionCallPorter');

    //end iteraction

    //Vista enviar sms WittyBots
    Route::get('/admin/send-wittybot-sms', 'Api\WittyBotsController@showWittyBotsSendView');
    //END Vista enviar sms WittyBots

    //Confirm Contact
    Route::get('/admin/contact/verificate/{id}', 'Admin\ContactCrudController@aceptAppUser');
    Route::get('/admin/check-contact/{id}/ci/{ci}', function ($id, $ci) {

        if (\App\Models\User\User::where('ci', $ci)->get()->count() == 1) {
            return redirect('/admin/contact/verificate/' . $id);
        }
        return redirect('/admin/contact?ci=' . $id);
    });
    //End Confirm Contact

    //Vista enviar sms WittyBots
    Route::get('/admin/send-wittybot-sms', 'Api\WittyBotsController@showWittyBotsSendView');
    //END Vista enviar sms WittyBots


});
//End general rutes

//Ajax API
Route::group([
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin')],
], function () { // custom admin routes
    //Ajax para categorias
//    Route::get('cases/cat_name/info/{id}', 'Api\CategoryNameController@categoryNameById');
    Route::get('admin/category-2/{id}', 'Api\CategoryNameController@category2Name');
    Route::get('admin/category-3/{id}', 'Api\CategoryNameController@category3Name');
//    Route::get('admin/case/categories2/info/{id}', 'Api\CategoryNameController@category2Name');
//    Route::get('admin/case/categories3/info/{id}', 'Api\CategoryNameController@category3Name');
    Route::get('admin/category/{category_id}/responsables', 'Api\CategoryNameController@responsable');
//    Route::get('admin/user/responsable/{category_id}', 'Api\CategoryNameController@responsable');
    //END Ajax para categorias

    //   api/users/{user_id}/cases
    //  api/building/{id}/cases
    //  api/flat/{id}/users
    // api/users/{id}/last-intea
    //Ajax cases
//    Route::get('/admin/cases/user/info/{user_id}', 'Api\CaseController@getCasesWithUser');

    Route::get('/admin/contact/building/relation/{id}', 'Admin\ContactCrudController@getContactRelationshipBuilding');
    Route::get('/admin/contact/{contactId}/{flatId}/intercom-incoming-call', 'Admin\ContactCrudController@getIntercomIncomingCall');
    Route::delete('/admin/contact/building/relation/{contact_id}/{flat_id}', 'Admin\ContactCrudController@deleteContactRelationshipBuilding');
    Route::delete('/admin/contact/building/relation/contact/{contact_id}/{flat_id}', 'Admin\ContactCrudController@deleteContactRelationshipBuilding');
    Route::delete('/admin/contact/list/building/relation/{relation_id}', 'Admin\ContactCrudController@deleteContactRelationshipBuildingFromList');
    Route::get('/admin/vehicles/all', 'Admin\CarCrudController@getAllVehicles');
    Route::get('/admin/buildings/relation/info/{building_id}/{flat_id}/{contact_id}', 'Admin\BuildingCrudController@getInfoOfTheRelationBuilding');
    Route::get('/admin/all/contacts/', 'Admin\ContactCrudController@getAllContacts');
    Route::get('/admin/all/buildings/', 'Admin\BuildingCrudController@getAllBuildings');
    Route::post('/admin/contact/save-email-ci-security_word/{contactId}', 'Admin\ContactCrudController@saveEmailCiSecurityWord');


    Route::get('/admin/contact-field', 'Admin\ContactCrudController@indexFieldAjax');
    Route::get('/admin/contact-field/{id}', 'Admin\ContactCrudController@showFieldAjax');

    Route::get('/admin/case/childs/{id}', 'Api\CaseController@getChildsOfCase');
    Route::get('/admin/case/childs/{id}/{state}', 'Api\CaseController@getChildsOfCaseState');
    Route::get('/admin/flat/{user_id}/cases/{flat_id}', 'Api\CaseController@getCasesWithFlat');
    Route::get('/admin/flat/{user_id}/cases/{flat_id}/flat/state/{state}', 'Api\CaseController@getCasesWithFlatNotPrincipal');
    Route::get('/admin/flat/{user_id}/cases/open/{flat_id}', 'Api\CaseController@getCasesWithFlatOpen');
    Route::get('/admin/flat/{user_id}/cases/curse/{flat_id}', 'Api\CaseController@getCasesWithFlatCruso');
    Route::get('/admin/flat/{user_id}/cases/close/{flat_id}', 'Api\CaseController@getCasesWithFlatClose');
//    Route::get('/admin/flat/{user_id}/cases/close/{flat_id}/{case_state}', 'Api\CaseController@getUserCasesWithFlat');
    Route::get('/admin/flat/{user_id}/cases/warning/{flat_id}', 'Api\CaseController@getCasesWithFlatWarning');
    Route::get('/admin/flat/{user_id}/cases/sinfo/{flat_id}', 'Api\CaseController@getCasesWithFlatSInfo');
    Route::get('/admin/info/category/{cat_id}', 'Api\CategoryNameController@getCategoryInfo');
    Route::get('/admin/user/{user_id}/interactions', 'Api\CaseController@getInteractionsWithUser');
    Route::get('/admin/user/interactions/{user_id}', 'Api\CaseController@getUserInteractions');

    Route::get('/admin/building/{number}/tracings', 'Api\CaseController@tracing');
    Route::get('/admin/building/{number}/last-open-tracings', 'Api\CaseController@lastOpenTracings');
    Route::get('/admin/building/{number}/cases', 'Api\CaseController@info');
    Route::get('/admin/building/{number}/flats', 'Api\CaseController@info');
    Route::get('/admin/building/number/{number}/flats', 'Admin\FlatCrudController@getFlatsByBuildingNumber');
    Route::get('/admin/building/{number}/last-open-cases', 'Api\CaseController@lastOpenCases');
    Route::get('/admin/building/{number}/{state}/filter-by-state-case', 'Api\CaseController@filterByStateCases');
    Route::get('/admin/building/{number}/{state}/{text}/filter-by-text-case', 'Api\CaseController@filterByTextCases');


    Route::get('/admin/building/{number}/all/cases', 'Api\CaseController@allInfo');
    Route::get('/admin/quick_case/category/{number}', 'Api\CaseController@quickCaseFormat');

    Route::get('/admin/building/{building_id}/users', 'Api\UserController@getUsersByBuildings');
    Route::get('/admin/building/{building_id}/flat/{flat_id}/users', 'Api\UserController@getUsersByFlatsOrBuildings');
    Route::get('/admin/building/flats/{flat_id}/users', 'Api\UserController@getUsersByFlats');

    Route::get('/admin/building/contact/user/{contact_id}/{flat_id}', 'Api\UserController@getPhoneAndEmail');
    Route::get('admin/last-interaction/{user_id}/go-edit', 'Api\InteractionController@getLastInteraction');

    Route::get('/admin/clean-notifications/{notify_id}', 'Api\NotificationsCrm@cleanNotify');
    Route::get('/admin/mark-all-notifications-read', 'Api\NotificationsCrm@markAllNotificationsRead');
    Route::get('/admin/delete-notifications/{notify_id}', 'Api\NotificationsCrm@deleteSpecificNotifications');
    Route::post('/admin/case/child', 'Api\CaseController@createChildCase');
    Route::put('/admin/case/exist/child/{child_id}', 'Api\CaseController@makeChildExistCase');
    Route::put('/admin/case/comment/{id}', 'App\CasesAppController@addComment');
    Route::get('/admin/case/comment/{id}', 'App\CasesAppController@getComments');
    Route::put('/admin/case/delete/comment/{id}', 'App\CasesAppController@deleteComment');
    Route::get('/admin/case/{case_id}/info', 'Admin\CaseCrudController@showInfoView');
    Route::put('/admin/case/{case_id}/update_state/{state}', 'Admin\CaseCrudController@updateCaseState');
    Route::put('/admin/case/announcement/{id}/{value}', 'Admin\CaseCrudController@announcementCase');


    Route::put('/admin/case/{case_id}/{updated_comments}', 'Admin\CaseCrudController@updateComment');
    Route::put('/admin/case/related/delete/{case_id}/', 'Admin\CaseCrudController@deleteCaseRelated');


    Route::get('/admin/area/{id}/responsables', 'AreaController@getAllResponsable');

    Route::get('/admin/intercom-cards/contact/{user_id}/flat/{flat_id}', 'Admin\IntercomCardsCrudController@getCardsFromContactAndFlat');
    Route::get('/admin/access-by-pin/contact/{user_id}/flat/{flat_id}', 'Admin\BuildingCrudController@hasAccessByPin');
    Route::get('/admin/building/access-by-pin/{id}', 'Admin\BuildingCrudController@buildingHasAccessByPin');
    Route::delete('/admin/access-by-pin/contact/{user_id}/flat/{flat_id}', 'Admin\BuildingCrudController@deleteAccessCodeForUserFromFlat');
    Route::get('/admin/schedule/{user_id}/flat/{flat_id}', 'Admin\BuildingCrudController@getSchedule');


    //END Ajax para cases

    // Wittybots //
    Route::get('/api/contacts/building/{building_id}', 'Api\WittyBotsController@getContactOfBuildings');
    Route::get('/api/tower/building/{building_id}', 'Api\WittyBotsController@getTowerOfBuildings');
    Route::get('/api/contacts/tower/{tower_id}', 'Api\WittyBotsController@getContactOfTower');
    Route::get('/api/contacts-comision/building/{building_id}', 'Api\WittyBotsController@getContactComisionOfBuildings');
    Route::get('/api/template/body/{key}', 'Api\WittyBotsController@getBody'); //key // Devuelve body //Interno solo FlowLabs
    Route::post('/api/HSM/send-message', 'Api\WittyBotsController@sendToBuilding');
    Route::post('/api/send-mesage-to-specific-contacts/{id}', 'Api\WittyBotsController@specificContacts');
    Route::post('/api/send-mesage-to-comision-contacts-in-building/{id}', 'Api\WittyBotsController@comisionContact');
    Route::post('/api/send-mesage-to-contacts-in-building/{id}', 'Api\WittyBotsController@allContactsInBuilding');
    Route::post('/api/send-mesage-to-contacts-in-tower/{id}', 'Api\WittyBotsController@allContactsInTower');
// END Wittybots //

//Crear interaccion entrante
    Route::get('/api/contact/{caller_id}/do-call/{userNumber}', 'Api\InteractionController@receivedCall');
//END Crear interaccion entrante
});

Route::group([
    'prefix' => '/api/tags',
], function () {
    Route::get('/{id}', [\App\Http\Controllers\Admin\TagCrudController::class, 'getById']);
    Route::get('/flat/{flat_id}', [\App\Http\Controllers\Admin\TagCrudController::class, 'getByFlatId']);
    Route::put('/{id}', [\App\Http\Controllers\Admin\TagCrudController::class, 'update']);
    Route::post('/', [\App\Http\Controllers\Admin\TagCrudController::class, 'store']);
    Route::delete('/{id}', [\App\Http\Controllers\Admin\TagCrudController::class, 'deleteTag']);
    Route::delete('/{id}', [\App\Http\Controllers\Admin\TagCrudController::class, 'deleteTag']);
});

Route::get('/cases/{flat_id}', [\App\Http\Controllers\Admin\CaseCrudController::class, 'getCases']);

Route::post('/admin/contact/reset/pass/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('backpack.auth.password.email');


//End ajax API

Route::get('/google', function () {
    return Socialite::driver('google')->redirect();
});

Route::get('/google-callback', function () {
    $user = Socialite::driver('google')->user();
    $controller = new \App\Http\Controllers\AuthController();
    return $controller->loginGoogle($user);

});

Route::get('admin/notifications/data', 'NotificationsController@getNotifications');
