<?php

// --------------------------
// Custom Backpack Routes
// --------------------------
// This route file is loaded automatically by Backpack\Base.
// Routes you generate using Backpack\Generators will be placed here.

use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => config('backpack.base.route_prefix', 'admin'),
    'middleware' => ['web', config('backpack.base.middleware_key', 'admin')],
    'namespace'  => 'App\Http\Controllers\Admin',
], function () { // custom admin routes
    Route::crud('flat', 'FlatCrudController');
    Route::crud('transferred-calls', 'TransferredCallsCrudController');
    Route::crud('transferred-calls-logs', 'TransferredCallsLogsCrudController');
    Route::crud('tag', 'TagCrudController');
    Route::crud('contact', 'ContactCrudController');
    Route::post('contact/{entryId}/revise/{revisionId}/restore', 'ContactCrudController@restore');
    Route::crud('access_revision', 'Access_revisionCrudController');
    Route::crud('building', 'BuildingCrudController');
    // Ruta para restore de revisiones
    Route::post('building/{entryId}/revise/{revisionId}/restore', 'BuildingCrudController@restore');
    Route::crud('operator', 'OperatorCrudController');
    Route::crud('contact', 'ContactCrudController');
    Route::crud('provider', 'ProviderCrudController');
    Route::crud('administrator', 'AdministratorCrudController');
    Route::crud('provider', 'ProviderCrudController');
    Route::crud('service', 'ServiceCrudController');
    Route::crud('company', 'CompanyCrudController');
    Route::crud('temporary_contact', 'TemporaryContactCrudController');
    Route::crud('building_active_hours', 'BuildingActiveHoursCrudController');
    Route::crud('car', 'CarCrudController');
    Route::crud('intercom-cards', 'IntercomCardsCrudController');
    Route::get('intercom-cards/{id}', 'IntercomCardsCrudController@getSpecificIntercom');
    Route::crud('roles', 'RoleCrudController');
    Route::get('change_to_building/{id}', 'BuildingCrudController@changeOfLead');
    Route::get('close_case/{id}', 'CaseCrudController@closeCase');
    Route::get('close_case_dashboard/{id}', 'CaseCrudController@closeCaseDashboard');
    Route::get('close_case_contact/{id}/{user_id}', 'CaseCrudController@closeCaseContact');
    Route::get('open_case/{id}', 'CaseCrudController@openCase');
    Route::get('recentlyinteraction/{user_id}/{id}', 'RecentlyInteractionCrudController@deleteFrom');
    Route::post('/building/fetch/services', 'ServiceCrudController@inline');

    Route::post('/administration/inline/create/modal', 'AdministratorCrudController@getInlineCreateModal')->name('administration-inline-create');
    Route::post('/administration/inline/create', 'AdministratorCrudController@storeInlineCreate')->name('administration-inline-create-save');
    Route::post('/building/fetch/administration', 'AdministratorCrudController@inline');

    Route::post('/building/fetch/commissions', 'ContactCrudController@inline');

    Route::crud('building-log', 'BuildingLogsCrudController');
    Route::crud('case', 'CaseCrudController');
    // Ruta para restore de revisiones de casos
    Route::post('case/{entryId}/revise/{revisionId}/restore', 'CaseCrudController@restore');
    Route::crud('category', 'CategoryCrudController');
    Route::crud('recently-interaction', 'RecentlyInteractionCrudController');
    Route::crud('wittytemplate', 'WittyTemplateCrudController');

    Route::get('test/ajax-case-options', 'CaseCrudController@caseOptions');
    Route::crud('building-intercom', 'BuildingIntercomCrudController');
    Route::crud('tower', 'TowerCrudController');
    Route::crud('user-info', 'UserInfoCrudController');
    Route::crud('access-code', 'AccessCodeCrudController');

    Route::get('akuvox-logs/{building_number}', 'AkuvoxLogsCrudController@list');
    Route::get('akuvox-door-status/{ip}', 'AkuvoxLogsCrudController@getDoorStatus');

    Route::crud('witty-log', 'WittyLogCrudController');
    Route::crud('pin-log', 'PinLogCrudController');
    Route::crud('intercom-models', 'IntercomModelsCrudController');
    Route::crud('authorized-contact', 'AuthorizedContactCrudController');
    Route::crud('users', 'UserCrudController');

}); // this should be the absolute lasct line of this file
