<?php

namespace Tests\Browser;

use App\Models\User\User;
use Laravel\Dusk\Browser;
use Tests\Browser\Pages\LoginPage;
use Tests\DuskTestCase;

class CreateUserTest extends DuskTestCase
{

    public function testCreateUserAdmin()
    {

        $this->browse(function (Browser $browser) {
            $browser->on(new LoginPage())
                ->loginUser()
                ->visit('/admin/users/create')
                ->check('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(7) > div > div:nth-child(2) > div > label > input[type=checkbox]')
                ->check('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(7) > div > div:nth-child(9) > div > label > input[type=checkbox]')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div.form-group.col-sm-12.required > input', 'USER')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(2) > input', 'TEST')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(3) > input', '00223')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(4) > input', '<EMAIL>')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(5) > input', '123456789')
                ->type('#tables-list-cruds > div > div > form > div.card > div > div:nth-child(6) > input', '123456789')
                ->scrollIntoView('#saveActions')
                ->assertSee('Guardar y regresar')
                ->press('button[type="submit"]');
        });
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
        User::query()->where('email', '<EMAIL>')->forceDelete();

        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }
}
