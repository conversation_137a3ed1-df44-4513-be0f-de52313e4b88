<?php

namespace Tests\Browser\Pages;

use App\Models\User\User;
use <PERSON><PERSON>\Dusk\Browser;

class LoginPage extends Page
{
    /**
     * Get the URL for the page.
     *
     * @return string
     */
    public function url()
    {
        return '/login';
    }

    /**
     * Abstract the Login functionality
     *
     * @param \Laravel\Dusk\Browser $browser
     * @param string $name
     * @return void
     */
    public function loginUser(Browser $browser)
    {
        $browser->maximize()
            ->visit('/')
            ->type('email', env('TESTING_EMAIL'))
            ->type('password', env('TESTING_PASSWORD'))
            ->press('button[type="submit"]')
            ->assertPathIs('/admin/index');
    }
}
