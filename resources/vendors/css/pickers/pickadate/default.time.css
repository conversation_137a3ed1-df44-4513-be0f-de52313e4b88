/* ==========================================================================
   $BASE-TIME-PICKER
   ========================================================================== */
/**
 * The list of times.
 */
.picker__list {
  list-style: none;
  padding: 0.75em 0 4.2em;
  margin: 0;
}
/**
 * The times on the clock.
 */
.picker__list-item {
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
  margin-bottom: -1px;
  position: relative;
  background: #fff;
  padding: .75em 1.25em;
}
@media (min-height: 46.75em) {
  .picker__list-item {
    padding: .5em 1em;
  }
}
/* Hovered time */
.picker__list-item:hover {
  cursor: pointer;
  color: #000;
  background: #b1dcfb;
  border-color: #0089ec;
  z-index: 10;
}
/* Highlighted and hovered/focused time */
.picker__list-item--highlighted {
  border-color: #0089ec;
  z-index: 10;
}
.picker__list-item--highlighted:hover,
.picker--focused .picker__list-item--highlighted {
  cursor: pointer;
  color: #000;
  background: #b1dcfb;
}
/* Selected and hovered/focused time */
.picker__list-item--selected,
.picker__list-item--selected:hover,
.picker--focused .picker__list-item--selected {
  background: #0089ec;
  color: #fff;
  z-index: 10;
}
/* Disabled time */
.picker__list-item--disabled,
.picker__list-item--disabled:hover,
.picker--focused .picker__list-item--disabled {
  background: #f5f5f5;
  border-color: #f5f5f5;
  color: #ddd;
  cursor: default;
  border-color: #ddd;
  z-index: auto;
}
/**
 * The clear button
 */
.picker--time .picker__button--clear {
  display: block;
  width: 80%;
  margin: 1em auto 0;
  padding: 1em 1.25em;
  background: none;
  border: 0;
  font-weight: 500;
  font-size: .67em;
  text-align: center;
  text-transform: uppercase;
  color: #666;
}
.picker--time .picker__button--clear:hover,
.picker--time .picker__button--clear:focus {
  color: #000;
  background: #b1dcfb;
  background: #e20;
  border-color: #e20;
  cursor: pointer;
  color: #fff;
  outline: none;
}
.picker--time .picker__button--clear:before {
  top: -0.25em;
  color: #666;
  font-size: 1.25em;
  font-weight: bold;
}
.picker--time .picker__button--clear:hover:before,
.picker--time .picker__button--clear:focus:before {
  color: #fff;
  border-color: #fff;
}

/* ==========================================================================
   $DEFAULT-TIME-PICKER
   ========================================================================== */
/**
 * The frame the bounds the time picker.
 */
.picker--time .picker__frame {
  min-width: 256px;
  max-width: 320px;
}
/**
 * The picker box.
 */
.picker--time .picker__box {
  font-size: 1em;
  background: #f2f2f2;
  padding: 0;
}
@media (min-height: 40.125em) {
  .picker--time .picker__box {
    margin-bottom: 5em;
  }
}
