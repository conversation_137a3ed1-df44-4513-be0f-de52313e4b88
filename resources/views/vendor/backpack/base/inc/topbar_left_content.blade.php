<?php
$current_user = backpack_user();
?>


@if ($current_user)
    <div class="side-bar-">
        <ul class="navigation navigation-main height-100-per d-flex flex-column" style="height: 94vh;"
            id="main-menu-navigation" data-menu="menu-navigation">
            {{-- Boton de inicio --}}
            <li class=" nav-item {{ request()->is('admin/index') ? ' active' : '' }}">
                <a class="d-flex align-items-center" href="/admin">
                    <ion-icon class="ion-icon-sidebar" style="" name="home-outline"></ion-icon>
                    <span class="text-truncate sidebar-text-bold" data-i18n="Todo">
                        Inicio
                    </span>
                </a>
            </li>
            {{-- Fin de Boton de inicio --}}

            {{-- Boton de Edificios --}}
            <li
                class="nav-item has-sub day-arrow-style {{ request()->is([
                    'admin/building*',
                    'form-lead-buildings',
                    'admin/building-log*',
                    'admin/building-intercom*',
                    'admin/administrator*',
                ])
                    ? ' active'
                    : '' }}">
                <a class="d-flex align-items-center" href="#">
                    <ion-icon class="ion-icon-sidebar" name="business-outline"></ion-icon>
                    <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                        Edificios
                    </span>
                </a>
                <ul class="menu-content mcsidebar ">
                    <li
                        class="{{ request()->is('admin/building') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/building">
                            <span class="menu-item text-truncate" data-i18n="Add">
                                Listar Edificios
                            </span>
                        </a>
                    </li>
                    @if (
                        $current_user->hasRole([
                            'Admin',
                            'Team Leader',
                            'Técnica',
                            'Auxiliar Ingeniería',
                            'Jefe Ingeniería',
                            'Supervisión',
                            'Operador Monitoreo',
                            'Analista en Seguridad',
                            'Jefe Operaciones',
                            'Representante Atención al Cliente',
                            'Jefe Atención al Cliente',
                            'Jefe Administrativo',
                            'Auxiliar Administrativo',
                        ]))

                        <li
                            class="{{ request()->is('admin/building-log*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/building-log">
                                <span class="menu-item text-truncate" data-i18n="Add">
                                    Logs
                                </span>
                            </a>
                        </li>
                        <li
                            class="{{ request()->is('admin/administrator*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/administrator">
                                <span class="menu-item text-truncate" data-i18n="Add">
                                    Administraciones
                                </span>
                            </a>
                        </li>
                        <li class="{{ 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/service">
                                <span class="menu-item text-truncate" data-i18n="Add">
                                    Servicios
                                </span>
                            </a>
                        </li>
                        @if ($current_user->hasRole(['Admin', 'Técnica', 'Auxiliar Ingeniería', 'Auxiliar Ingeniería N1', 'Jefe Ingeniería','Representante Atención al Cliente']))
                            <li
                                class="{{ request()->is('admin/building-intercom*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item"
                                   href="/admin/building-intercom">
                                    <span class="menu-item text-truncate" data-i18n="Add">
                                        Intercomunicadores
                                    </span>
                                </a>
                            </li>
                        @endif
                    @endif
                    <li
                        class="{{ request()->is('admin/building/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/building/create">
                            <span class="menu-item text-truncate" data-i18n="Add">
                                Nuevo Edificio
                            </span>
                        </a>
                    </li>
                    @if ($current_user->hasRole(['Representante Atención al Cliente', 'Jefe Atención al Cliente', 'Admin']))
                        <li
                            class="{{ request()->is('admin/flat*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/flat">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Apartamentos
                                </span>
                            </a>
                        </li>
                    @endif
                    <li
                        class="{{ request()->is('admin/tag*') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/tag">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Tags
                            </span>
                        </a>
                    </li>
                    <li
                        class="{{ request()->is('admin/transferred-calls*') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/transferred-calls">
                            <span class="menu-item text-truncate" data-i18n="List">
                               Interacciones AV
                            </span>
                        </a>
                    </li>
                </ul>
            </li>
            {{-- Fin de Boton de Edificios --}}

            {{-- Boton de Contactos --}}
            <li
                class="nav-item has-sub day-arrow-style {{ request()->is(['admin/contact*', 'admin/car*', 'admin/authorized-contact*']) ? ' active' : '' }}">
                <a class="d-flex align-items-center" href="#">
                    <ion-icon class="ion-icon-sidebar" name="person-outline"></ion-icon>
                    <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                        Contactos
                    </span>
                </a>
                <ul class="menu-content mcsidebar">
                    @if (!$current_user->hasRole('Cadete'))
                        <li
                            class="{{ request()->is('admin/contact') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/contact">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Listar Contactos
                                </span>
                            </a>
                        </li>
                        @if(!backpack_user()->hasPermissionTo('No crear Contacto'))
                            <li
                                class="{{ request()->is('admin/contact/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/contact/create">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Nuevo Contacto
                                </span>
                                </a>
                            </li>
                        @endif
                        <li
                            class="{{ request()->is('admin/contacts/upload') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/contacts/upload">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Importar contactos
                            </span>
                            </a>
                        </li>
                    @endif
                    @if (
                        $current_user->hasRole([
                            'Jefe Operaciones',
                            'Jefe Atención al Cliente',
                            'Representante Atención al Cliente',
                            'Admin',
                            'Team Leader',
                            'Operador Monitoreo',
                            'Analista en Seguridad',
                            'Supervisión',
                        ]))
                        <li
                            class="{{ request()->is('admin/car*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/car">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Vehículos
                                </span>
                            </a>
                        </li>
                    @endif
                    @if(!$current_user->hasRole('Cadete'))
                        <li
                            class="{{ request()->is('admin/auhorized-contact/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item"
                               href="/admin/authorized-contact/create">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Nuevo Contacto Temporal
                            </span>
                            </a>
                        </li>
                    @endif
                </ul>
            </li>
            {{-- Fin de Boton de Contactos --}}

            {{-- Boton de servicios --}}
            <li class="nav-item has-sub day-arrow-style {{ request()->is('admin/service*') ? ' active' : '' }}">
                <a class="d-flex align-items-center" href="#">
                    <ion-icon class="ion-icon-sidebar" name="help-buoy-outline"></ion-icon>
                    <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                        Servicios
                    </span>
                </a>
                <ul class="menu-content mcsidebar">
                    <li
                        class="{{ request()->is('admin/service') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/service">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Listar Servicios
                            </span>
                        </a>
                    </li>
                    <li
                        class="{{ request()->is('admin/service/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/service/create">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Nuevo Servicio
                            </span>
                        </a>
                    </li>
                </ul>
            </li>
            {{-- Fin de Boton de servicios --}}

            {{-- Boton de Permisos temporales --}}
            @if (
                $current_user->hasRole([
                    'Jefe Operaciones',
                    'Jefe Atención al Cliente',
                    'Representante Atención al Cliente',
                    'Admin',
                    'Team Leader',
                    'Operador Monitoreo',
                    'Analista en Seguridad',
                    'Supervisión',
                ]))
                <li
                    class="nav-item has-sub day-arrow-style {{ request()->is('admin/temporary_contact*') ? ' active' : '' }}">
                    <a class="d-flex align-items-center" href="#">
                        <ion-icon class="ion-icon-sidebar" name="calendar-clear-outline"></ion-icon>
                        <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                            Permisos
                        </span>
                    </a>
                    <ul class="menu-content mcsidebar">
                        <li
                            class="{{ request()->is('admin/temporary_contact') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/temporary_contact">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Listar Permisos
                                </span>
                            </a>
                        </li>
                        <li
                            class="{{ request()->is('admin/temporary_contact/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item"
                               href="/admin/authorized-contact/create">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Nuevo Contacto Temporal
                                </span>
                            </a>
                        </li>

                    </ul>
                </li>
            @endif
            {{-- Fin de Boton de Permisos temporales --}}

            {{-- Boton de casos --}}
            <li class="nav-item has-sub day-arrow-style {{ request()->is('admin/case*') ? ' active' : '' }}">
                <a class="d-flex align-items-center" href="#">

                    <ion-icon name="alert-circle-outline" class="ion-icon-sidebar"></ion-icon>
                    <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                        Casos
                    </span>
                </a>
                <ul class="menu-content mcsidebar">
                    <li
                        class="{{ request()->is('admin/case') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/case">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Listar Casos
                            </span>
                        </a>
                    </li>
                    <li
                        class="{{ request()->is('admin/case/create') ? 'active active-single-item' : 'text-underline-hover' }}">
                        <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/case/create"
                           target="_blank">
                            <span class="menu-item text-truncate" data-i18n="List">
                                Nuevo Caso
                            </span>
                        </a>
                    </li>
                </ul>
            </li>
            {{-- Fin de Boton de casos --}}

            {{-- Boton de Administrar --}}
            @if ($current_user->hasRole(['Admin','Jefe Ingeniería']))
                <li
                    class="nav-item has-sub day-arrow-style {{ request()->is([
                        'admin/flat*',
                        'admin/category*',
                        'admin/users/' . $current_user->id . '/edit',
                        'admin/users*',
                        'admin/company*',
                    ])
                        ? ' active'
                        : '' }}">
                    <a class="d-flex align-items-center" href="#">
                        <ion-icon class="ion-icon-sidebar" name="settings-outline"></ion-icon>
                        <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                            Administrar
                        </span>
                    </a>
                    <ul class="menu-content mcsidebar">
                        @if ($current_user->hasRole(['Admin']))
                            <li
                                class="{{ request()->is('admin/intercom-models*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/intercom-models">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Modelos intercom
                                </span>
                                </a>
                            </li>
                            <li
                                class="{{ request()->is('admin/company*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/company">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Usuarios CRM
                                </span>
                                </a>
                            </li>
{{--                            <li--}}
{{--                                class="{{ request()->is('admin/users*') ? 'active active-single-item' : 'text-underline-hover' }}">--}}
{{--                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/users">--}}
{{--                                <span class="menu-item text-truncate" data-i18n="List">--}}
{{--                                    Usuarios--}}
{{--                                </span>--}}
{{--                                </a>--}}
{{--                            </li>--}}
                            <li
                                class="{{ request()->is('admin/category*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/category">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Categorias
                                </span>
                                </a>
                            </li>
                            <li
                                class="{{ request()->is('admin/users/' . $current_user->id . '/edit') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item"
                                   href="/admin/users/{{ $current_user->id }}/edit">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Cuenta
                                </span>
                                </a>
                            </li>
                            <li
                                class="{{ request()->is('admin/access-code*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/access-code">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    PINs de acceso
                                </span>
                                </a>
                            </li>
                            <li
                                class="{{ request()->is('admin/pin-log*') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/pin-log">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Pin Logs
                                </span>
                                </a>
                            </li>
                        @endif
                        <li
                            class="{{ request()->is('admin/tag*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/tag/upload">
                            <span class="menu-item text-truncate" data-i18n="List">
                              Importar Tags
                            </span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif
            {{-- Fin de Boton de Administrar --}}
            {{-- Boton de Autorizaciones --}}
            @if ($current_user->hasRole(['Admin']))
                <li
                    class="nav-item has-sub day-arrow-style {{ request()->is(['admin/roles', 'admin/permission']) ? ' active' : '' }}">
                    <a class="d-flex align-items-center">
                        <ion-icon name="checkmark-circle-outline" class="ion-icon-sidebar"></ion-icon>
                        <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                            Autorizaciones
                        </span>
                    </a>
                    <ul class="menu-content mcsidebar">
                        <li
                            class="{{ request()->is('admin/roles*') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/roles">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Roles
                                </span>
                            </a>
                        </li>
                        <li
                            class="{{ request()->is('admin/permission') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/permission">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Permisos
                                </span>
                            </a>
                        </li>
                    </ul>

                </li>
            @endif



            {{-- Boton de APP y Witty --}}
            @if ($current_user->hasRole(['Admin']))
                <li
                    class="nav-item has-sub day-arrow-style {{ request()->is('app/contact/show-registration-mail') ? ' active' : '' }}">
                    <a class="d-flex align-items-center" href="#">
                        <ion-icon class="ion-icon-sidebar" name="phone-portrait-outline"></ion-icon>
                        <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                            App
                        </span>
                    </a>
                    <ul class="menu-content mcsidebar">
                        <li
                            class="{{ request()->is('app/contact/show-registration-mail') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item"
                               href="/app/contact/show-registration-mail">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Mails De Registro
                                </span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif

            @if (
                $current_user->hasRole([
                    'Operador Monitoreo',
                    'Analista en Seguridad',
                    'Representante Atención al Cliente',
                    'Jefe Atención al Cliente',
                    'Jefe Operaciones',
                    'Admin',
                ]))
                <li
                    class="nav-item has-sub day-arrow-style {{ request()->is(['admin/wittytemplate', 'admin/send-wittybot-sms']) ? ' active' : '' }}">
                    <a class="d-flex align-items-center" href="#">
                        <ion-icon class="ion-icon-sidebar" name="logo-whatsapp"></ion-icon>
                        <span class="text-truncate navbar-text-color sidebar-text-bold" data-i18n="Invoice">
                            WittyBots
                        </span>
                    </a>
                    <ul class="menu-content mcsidebar">
                        <li
                            class="{{ request()->is('admin/send-wittybot-sms') ? 'active active-single-item' : 'text-underline-hover' }}">
                            <a class="d-flex align-items-center new-sidebar-sub-item"
                               href="/admin/send-wittybot-sms">
                                <span class="menu-item text-truncate" data-i18n="List">
                                    Witty Bots
                                </span>
                            </a>
                        </li>
                        @if ($current_user->hasRole('Admin'))
                            <li
                                class="{{ request()->is('admin/wittytemplate') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item"
                                   href="/admin/wittytemplate">
                                    <span class="menu-item text-truncate" data-i18n="List">
                                        Witty Template
                                    </span>
                                </a>
                            </li>
                        @endif
                        @if (
                            $current_user->hasRole([
                                'Admin',
                                'Team Leader',
                                'Operador Monitoreo',
                                'Representante Atención al Cliente',
                                'Jefe Atención al Cliente',
                            ]))
                            <li
                                class="{{ request()->is('admin/wittytemplate') ? 'active active-single-item' : 'text-underline-hover' }}">
                                <a class="d-flex align-items-center new-sidebar-sub-item" href="/admin/witty-log">
                                    <span class="menu-item text-truncate" data-i18n="List">
                                        Witty Logs
                                    </span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>

            @endif
            {{-- Fin de Boton de APP y Witty --}}
            <li class="nav-item mt-auto" style="margin-bottom: 20px !important;">
                <a class="d-flex align-items-center mb-1"
                   href='https://sites.google.com/foxsys.com/ayuda/inicio'
                   style="background: none !important; box-shadow: none !important;" target="_blank">
                    <ion-icon class="ion-icon-sidebar" name="help-circle-outline"></ion-icon>
                    <span class="text-truncate actualizations-button"
                          data-i18n="List">Portal de ayuda</span>
                </a>
                <a class="d-flex align-items-center" href='#SGBF-open-62de8f911d3c30001332fed8'
                   style="background: none !important; box-shadow: none !important;">
                    <ion-icon class="ion-icon-sidebar color-blue" style="" name="megaphone-outline"></ion-icon>
                    <span class="text-truncate color-blue actualizations-button"
                          data-i18n="List">Actualizaciones</span>
                </a>
            </li>
        </ul>
    </div>
    <link rel="stylesheet" type="text/css" href="/css/sidebar-styles.css">
    <style>
        .navigation-main * {
            font-size: 1.1rem !important;
        }
    </style>
@endif
