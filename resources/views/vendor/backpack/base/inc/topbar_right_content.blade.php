<!-- This file is used to store topbar (right) items -->


{{-- <li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-bell"></i><span class="badge badge-pill badge-danger">5</span></a></li>
<li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-list"></i></a></li>
<li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-map"></i></a></li> --}}

<!-- This file is used to store topbar (right) items -->


{{-- <li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-bell"></i><span class="badge badge-pill badge-danger">5</span></a></li>--}}
{{--<li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-list"></i></a></li>--}}
{{--<li class="nav-item d-md-down-none"><a class="nav-link" href="#"><i class="la la-map"></i></a></li> --}}
<?php
    $user = \App\Models\User\User::findOrFail(backpack_user()->id);
    $notifications =  json_decode($user->unreadNotifications()->limit(20)->get());
    $count = $user->unreadNotifications->count();
    ?>

<div style="padding: 14px;">
    <li class="dropdown text-center" style="    background-color: #ffffff;
    border-radius: 50px!important;
    width: 75px;
    max-height: 32px;">
        <a id="dLabel" class="" role="button" data-toggle="dropdown" data-target="#" href="#" >
                <i id="bell-notify" class="nav-icon la la-bell" size="" style="color: #852e2e; font-weight: bold; font-size: 25px!important;"></i>
            <sup id="noty-number" style="color: #852E2E; font-size: 15px; font-weight: bold;">{{$count}}</sup>
        </a>

        <ul class="dropdown-menu notifications" role="menu" aria-labelledby="dLabel" style="left: -290px;    min-width: 190px;">

            <div class="notification-heading"><h4 class="menu-title">Notificaciones</h4><h4 class="menu-title pull-right"><a href="/admin/all_notify/{{backpack_user()->id}}">&nbsp;Ver todas<i class="glyphicon glyphicon-circle-arrow-right"></i></a></h4>
            </div>
            <div id="notifications-wrapper" class="notifications-wrapper" style="width: 100%;">
                            @foreach($notifications as $notify)
                                <div id="{{$notify->id}}">
                                <?php
                                    $data = $notify->data;
                                    $date = \Carbon\Carbon::parse($notify->created_at)->diffForHumans();
                                ?>
                                    <div class="text-center" onclick="cleanNotify('{{$notify->id}}')" title="Marcar como leida" style="padding: 2px; cursor: crosshair; margin-left: 95%;">x</div>

                                    <a class="content" onclick="goToNoty('{{$notify->id}}','{{$data->link}}')"> {{-- link para ver la accion de esta notify --}}
                                    <div class="notification-item">
{{--                                        <img src="{{$data->photo}}"> --}}{{-- user foto --}}
                                        <h4 class="item-title" style="color: #0B90C4;">Por  {{\App\Models\User\User::find($data->created_by) ? \App\Models\User\User::find($data->created_by)->complete_name : 'Foxsys'}}</h4>
                                        <h4 class="item-title" style="font-weight: normal!important;"> {{$data->text}}</h4>
                                        <p class="item-info">Area: {{\App\Models\Area::find($data->area_id)->name ?? 'Atención'}}. <small class="item-info" STYLE="color: grey"> {{$date}}</small>
                                        </p>
                                    </div>
                                </a>
                                </div>
                            @endforeach
            </div>
        </ul>
    </li>
</div>

<script>
    checkBell();

    function cleanNotify(notify_id){
        markNotifyRead(notify_id);
        document.getElementById(notify_id).remove();
        number = document.getElementById('noty-number').innerHTML;
        document.getElementById('noty-number').innerHTML = number - 1;
        checkBell();
    }

    function goToNoty(notify_id,link){
        markNotifyRead(notify_id);
        window.location.pathname= link;
    }

    function markNotifyRead(notify_id){
        $.ajax({
            url: '/admin/clean-notifications/' + notify_id,
            type: 'GET',
            dataType: "json",
            async: false,
        });
    }

    function checkBell(){
        number = document.getElementById('noty-number').innerHTML;
        if(number == 0){
            document.getElementById('bell-notify').style.color = '#467fd0';
            document.getElementById('noty-number').innerHTML = '';
        }
    }


    function editLastInteractions(user_id){
        var parametros = '';
        $.ajax({
            url: '/admin/getnotifications/' + user_id,
            type: 'GET',
            dataType: "json",
            async: true,
            success: function (data) {
                parametros=data
            },
        });
        return parametros;
    }
</script>
