@extends(backpack_view('blank'))

@php
  $defaultBreadcrumbs = [
    trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
    $crud->entity_name_plural => url($crud->route),
    trans('revise-operation::revise.revisions') => false,
  ];

  // if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
  $breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;

  $heading = $crud->getHeading() ?? $crud->entity_name_plural;
  $subheading = $crud->getSubheading() ?? method_exists($entry, 'identifiableName') ? trans('revise-operation::revise.revisions_for').' "'.$entry->identifiableName().'"' : trans('revise-operation::revise.revisions');
@endphp

@section('header')
  <div class="container-fluid">
    <h2>
        <span class="text-capitalize">{!! $heading !!}</span>
        <small>{!! $subheading !!}.</small>

        @if ($crud->hasAccess('list'))
          <small><a href="{{ url($crud->route) }}" class="hidden-print font-sm"><i class="la la-angle-double-left"></i> {{ trans('backpack::crud.back_to_all') }} <span>{{ $crud->entity_name_plural }}</span></a></small>
        @endif
    </h2>
  </div>
@endsection

@section('content')
<div class="row m-t-20">
  <div class="{{ $crud->get('revise.timelineContentClass') ?? 'col-md-12' }}">
    <!-- Pagination Controls -->
    <div class="card mb-3">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-md-6">
            <div class="form-group mb-0">
              <label for="per_page" class="form-label">{{ trans('revise-operation::revise.revisions_per_page', [], 'Revisiones por página') }}:</label>
              <select id="per_page" class="form-control" style="width: auto; display: inline-block;">
                <option value="5" {{ request()->get('per_page', 10) == 5 ? 'selected' : '' }}>5</option>
                <option value="10" {{ request()->get('per_page', 10) == 10 ? 'selected' : '' }}>10</option>
                <option value="20" {{ request()->get('per_page', 10) == 20 ? 'selected' : '' }}>20</option>
                <option value="50" {{ request()->get('per_page', 10) == 50 ? 'selected' : '' }}>50</option>
              </select>
            </div>
          </div>
          <div class="col-md-6 text-right">
            <small class="text-muted">
              {{ trans('revise-operation::revise.showing_revisions', [
                'from' => $paginator->firstItem() ?? 0,
                'to' => $paginator->lastItem() ?? 0,
                'total' => $paginator->total()
              ], 'Mostrando :from a :to de :total revisiones') }}
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Timeline Container -->
    <div id="timeline-container">
      @if(!count($revisions))
        <div class="card">
          <div class="card-header with-border">
            <h3 class="card-title">{{ trans('revise-operation::revise.no_revisions') }}</h3>
          </div>
        </div>
      @else
        @include('revise-operation::revision_timeline_paginated')
      @endif
    </div>

    <!-- Pagination Links -->
    @if($paginator->hasPages())
      <div class="d-flex justify-content-center mt-4">
        {{ $paginator->appends(request()->query())->links() }}
      </div>
    @endif
  </div>
</div>
@endsection

@section('after_styles')
  <link rel="stylesheet" href="{{ asset('packages/backpack/crud/css/crud.css') }}">
  <link rel="stylesheet" href="{{ asset('packages/backpack/crud/css/revisions.css') }}">
  <style>
    .pagination-loading {
      opacity: 0.6;
      pointer-events: none;
    }
    
    .timeline-item-wrap.fadein {
      -webkit-animation: restore-fade-in 3s;
              animation: restore-fade-in 3s;
    }
    
    @-webkit-keyframes restore-fade-in {
      from {opacity: 0}
      to {opacity: 1}
    }
    
    @keyframes restore-fade-in {
      from {opacity: 0}
      to {opacity: 1}
    }
  </style>
@endsection

@section('after_scripts')
  <script src="{{ asset('packages/backpack/crud/js/crud.js') }}"></script>
  <script src="{{ asset('packages/backpack/crud/js/revisions.js') }}"></script>
  
  <script type="text/javascript">
    $(document).ready(function() {
      // Handle per page change
      $('#per_page').on('change', function() {
        var perPage = $(this).val();
        var url = new URL(window.location.href);
        url.searchParams.set('per_page', perPage);
        url.searchParams.set('page', 1); // Reset to first page
        window.location.href = url.toString();
      });

      // Handle pagination links
      $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        
        if (url) {
          loadPage(url);
        }
      });

      function loadPage(url) {
        $('#timeline-container').addClass('pagination-loading');
        
        $.ajax({
          url: url,
          type: 'GET',
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          },
          success: function(data) {
            $('#timeline-container').html(data);
            $('#timeline-container').removeClass('pagination-loading');
            
            // Update URL without page reload
            window.history.pushState({}, '', url);
          },
          error: function() {
            $('#timeline-container').removeClass('pagination-loading');
            new Noty({
              type: "error",
              text: "{{ trans('revise-operation::revise.error_loading_revisions', [], 'Error al cargar las revisiones') }}"
            }).show();
          }
        });
      }
    });

    // CSRF token setup for AJAX requests
    $.ajaxPrefilter(function (options, originalOptions, xhr) {
      var token = $('meta[name="csrf_token"]').attr('content');
      if (token) {
        return xhr.setRequestHeader('X-XSRF-TOKEN', token);
      }
    });

    function onRestoreClick(e) {
      e.preventDefault();
      var entryId = $(e.target).attr('data-entry-id');
      var revisionId = $(e.target).attr('data-revision-id');
      
      $.ajax('{{ url(\Request::url()).'/' }}' + revisionId + '/restore', {
        method: 'POST',
        data: {
          revision_id: revisionId
        },
        success: function (revisionTimeline) {
          // Reload the current page to show updated revisions
          location.reload();
        },
        error: function (data) {
          // Show a red notification bubble
          new Noty({
            type: "error",
            text: data.responseJSON.message
          }).show();
        }
      });
    }
  </script>
@endsection
