<div id="timeline">
@foreach($revisions as $revisionDate => $dateRevisions)
    <div class="card timeline-item-wrap">
        <div class="card-header bg-body-secondary border-bottom">
            <h5 class="mb-0">
                <i class="la la-calendar"></i> {{ \Carbon\Carbon::parse($revisionDate)->format('Y-m-d') }}
            </h5>
        </div>
        <div class="card-body p-0">
            @foreach($dateRevisions as $history)
                @php
                    $user = null;
                    if ($history->user_id) {
                        try {
                            $user = \App\Models\User\User::find($history->user_id);
                        } catch (Exception $e) {
                            $user = null;
                        }
                    }
                    $userName = $user && isset($user->complete_name) ? $user->complete_name : 'Usuario invitado';
                @endphp

                <div class="border-bottom p-3 @if(!$loop->last) border-bottom @endif">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="mb-1">
                                <i class="la la-clock"></i> {{ date('h:ia', strtotime($history->created_at)) }} ({{ $userName }})
                            </div>
                            <div >
                                @if($history->key == 'created_at' && !$history->old_value)
                                    <strong>Creado</strong>
                                @else
                                    <strong>{{ $history->key }}</strong>
                                @endif
                            </div>
                        </div>
                        @if($history->key != 'created_at' || $history->old_value)
                            <div>
                                <button type="button" class="btn btn-outline-danger btn-sm restore-btn-custom"
                                        data-entry-id="{{ $entry->id }}" data-revision-id="{{ $history->id }}"
                                        onclick="doRestore(this); return false;">
                                    <i class="la la-undo"></i> {{ trans('revise-operation::revise.undo') }}
                                </button>
                            </div>
                        @endif
                    </div>

                    @if($history->key != 'created_at' || $history->old_value)
                        <div class="row mt-2">
                            <div class="col-md-6">{{ mb_ucfirst(trans('revise-operation::revise.from')) }}:</div>
                            <div class="col-md-6">{{ mb_ucfirst(trans('revise-operation::revise.to')) }}:</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-danger"
                                     style="overflow: hidden;">{{ $history->old_value ?? 'N/A' }}</div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-success"
                                     style="overflow: hidden;">{{ $history->new_value ?? 'N/A' }}</div>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
@endforeach
</div>

@if($paginator->hasPages())
  <div class="d-flex justify-content-center mt-4">
    {{ $paginator->appends(request()->query())->links('revise-operation::pagination') }}
  </div>
@endif

<script type="text/javascript">
  function doRestore(button) {
    var entryId = $(button).attr('data-entry-id');
    var revisionId = $(button).attr('data-revision-id');

    if (!entryId || !revisionId) {
      return;
    }

    var currentPath = window.location.pathname;
    var basePath = currentPath.split('/revise')[0];
    var url = basePath + '/revise/' + revisionId + '/restore';
    var token = $('meta[name="csrf-token"]').attr('content');

    var originalHtml = $(button).html();
    $(button).prop('disabled', true);
    $(button).html('<div class="spinner-border spinner-border-sm" role="status"></div> Procesando...');

    $.ajax({
      url: url,
      method: 'POST',
      data: {
        revision_id: revisionId,
        _token: token
      },
      success: function(response) {
        $(button).prop('disabled', false);
        $(button).html(originalHtml);

        if (typeof Noty !== 'undefined') {
          new Noty({
            type: "success",
            text: "<strong>¡Cambio deshecho!</strong><br>El cambio ha sido revertido exitosamente."
          }).show();
        } else {
          showBootstrapNotification('success', '¡Cambio deshecho!', 'El cambio ha sido revertido exitosamente.');
        }

        setTimeout(function() {
          location.reload();
        }, 2000);
      },
      error: function(xhr, status, error) {
        $(button).prop('disabled', false);
        $(button).html(originalHtml);

        var errorMessage = 'Error al deshacer el cambio';
        if (xhr.responseJSON && xhr.responseJSON.message) {
          errorMessage = xhr.responseJSON.message;
        }

        if (typeof Noty !== 'undefined') {
          new Noty({
            type: "error",
            text: "<strong>Error:</strong><br>" + errorMessage
          }).show();
        } else {
          showBootstrapNotification('error', 'Error', errorMessage);
        }
      }
    });
  }



  function showBootstrapNotification(type, title, message) {
    var alertClass = 'alert-success';
    var iconClass = 'la-check-circle';

    if (type === 'error') {
      alertClass = 'alert-danger';
      iconClass = 'la-times-circle';
    } else if (type === 'warning') {
      alertClass = 'alert-warning';
      iconClass = 'la-exclamation-triangle';
    }

    var notificationHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
      ">
        <i class="la ${iconClass} mr-2"></i>
        <strong>${title}</strong><br>
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
    `;

    $('body').append(notificationHtml);

    setTimeout(function() {
      $('.alert').fadeOut(500, function() {
        $(this).remove();
      });
    }, 4000);
  }


</script>
