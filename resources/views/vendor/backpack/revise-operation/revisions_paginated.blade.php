@extends(backpack_view('blank'))

@php
  $defaultBreadcrumbs = [
    trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
    $crud->entity_name_plural => url($crud->route),
    trans('revise-operation::revise.revisions') => false,
  ];

  $breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;
  $heading = $crud->getHeading() ?? $crud->entity_name_plural;
  $subheading = $crud->getSubheading() ?? method_exists($entry, 'identifiableName') ? trans('revise-operation::revise.revisions_for').' "'.$entry->identifiableName().'"' : trans('revise-operation::revise.revisions');
@endphp

@section('header')
  <div class="container-fluid">
    <h2>
        <span class="text-capitalize">{!! $heading !!}</span>
        <small>{!! $subheading !!}.</small>

        @if ($crud->hasAccess('list'))
          <small><a href="{{ url($crud->route) }}" class="hidden-print font-sm"><i class="la la-angle-double-left"></i> {{ trans('backpack::crud.back_to_all') }} <span>{{ $crud->entity_name_plural }}</span></a></small>
        @endif
    </h2>
  </div>
@endsection

@section('content')
<div class="row m-t-20">
  <div class="{{ $crud->get('revise.timelineContentClass') ?? 'col-md-12' }}">
    @if($paginator->total() > 20)
    <div class="card mb-3">
      <div class="card-body">
        <div class="text-center">
          <strong class="">
            {{ $paginator->total() }} revisiones en total
          </strong>
        </div>
      </div>
    </div>
    @endif

    <!-- Timeline Container -->
    <div id="timeline-container">
      @if(!count($revisions))
        <div class="card">
          <div class="card-header with-border">
            <h3 class="card-title">No hay revisiones disponibles</h3>
          </div>
        </div>
      @else
        @include('revise-operation::revision_timeline_paginated')
      @endif
    </div>
  </div>
</div>
@endsection

@section('after_styles')
  <link rel="stylesheet" href="{{ asset('packages/backpack/crud/css/crud.css') }}">
  <link rel="stylesheet" href="{{ asset('packages/backpack/crud/css/revisions.css') }}">
  <style>
    .pagination-loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .timeline-item-wrap.fadein {
      -webkit-animation: restore-fade-in 3s;
              animation: restore-fade-in 3s;
    }

    @-webkit-keyframes restore-fade-in {
      from {opacity: 0}
      to {opacity: 1}
    }

    @keyframes restore-fade-in {
      from {opacity: 0}
      to {opacity: 1}
    }
  </style>
@endsection

@section('after_scripts')
  <script src="{{ asset('packages/backpack/crud/js/crud.js') }}"></script>
  <script src="{{ asset('packages/backpack/crud/js/revisions.js') }}"></script>

  <script type="text/javascript">
    $(document).ready(function() {
      $('.footer').hide();

      $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');

        if (url) {
          loadPage(url);
        }
      });

      function loadPage(url) {
        $('#timeline-container').addClass('pagination-loading');

        $.ajax({
          url: url,
          type: 'GET',
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          },
          success: function(data) {
            $('#timeline-container').html(data);
            $('#timeline-container').removeClass('pagination-loading');

            window.history.pushState({}, '', url);
          },
          error: function() {
            $('#timeline-container').removeClass('pagination-loading');
            new Noty({
              type: "error",
              text: "Error al cargar las revisiones"
            }).show();
          }
        });
      }
    });

    $.ajaxPrefilter(function (options, originalOptions, xhr) {
      var token = $('meta[name="csrf_token"]').attr('content');
      if (token) {
        return xhr.setRequestHeader('X-XSRF-TOKEN', token);
      }
    });

    function onRestoreClick(e) {
      e.preventDefault();
      var entryId = $(e.target).attr('data-entry-id');
      var revisionId = $(e.target).attr('data-revision-id');

      $.ajax('{{ url(\Request::url()).'/' }}' + revisionId + '/restore', {
        method: 'POST',
        data: {
          revision_id: revisionId
        },
        success: function (revisionTimeline) {
          location.reload();
        },
        error: function (data) {
          new Noty({
            type: "error",
            text: data.responseJSON.message
          }).show();
        }
      });
    }
  </script>
@endsection
