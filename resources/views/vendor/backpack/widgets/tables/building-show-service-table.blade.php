<table
    class="table table-hover building-services-table index-table-height compact nowrap table-full-width dataTable no-footer"
    style="width: 100% !important; max-height: 100px;" id="service-table">


    <thead style="position: relative; z-index: 9">
    <tr>
        <th style="width:120px;; cursor: pointer;" class="text-capitalize" scope="col"
            wire:click="sortServiceBy('provider')">EMPRESA
            <i class="la la-sort"></i></th>
        <th style="width:60px" class="text-capitalize" scope="col" title="Apartamento">TELÉFONO</th>
        <th style="width:120px; cursor: pointer;" class="text-capitalize" scope="col"
            wire:click="sortServiceBy('service')">TIPO DE SERVICIO <i class="la la-sort"></i></th>
    </tr>
    </thead>
    <tbody id="loader-tbody-services" class="d-none">
    <tr>
        <td colspan="3" class="text-center loader-cell">
            <span class="loader"></span>
        </td>
    </tr>
    </tbody>
    <tbody id="data-tbody-services">
    @if($administrator)
        <tr wire:key="{{$administrator->id}}">
            <td class="administration-name" data-administration="{{$administrator->id}}"
                onclick="openWindow('administrator', '{{$administrator->id}}')"
                style="font-weight: bold; padding: 15px 0px 15px 10px;">
                {{\Str::substr($administrator->name, 0, 20)}}
            </td>
            <td class="text-wrap">
                <a href="tel:{{$administrator->office_phone}}">
                    {{trim($administrator->office_phone)}}
                </a>
            </td>
            <td onclick="openWindow('administrator', '{{$administrator->id}}')">Administración</td>
        </tr>
    @endif
    @forelse($services as $service)
        <tr id="service-row" data-service="{{$service->id}}" wire:key="{{$service->id}}">
            <td data-service="{{$service->id}}" title="{{$service->provider}}"
                onclick="openWindow('service', '{{$service->id}}')" class="name-color"
                style="font-weight: bold; padding: 15px 0px 15px 10px;">
                {{$service->provider ? \Str::substr($service->provider, 0, 20) : "Sin descripción"}}
            </td>

            <td title="@if(isset($service->urgency))  {{$service->urgency}} / @endif {{$service->office_phone}}"
                class="text-wrap">
                @if(isset($service->claims))
                    <div class="d-flex align-items-center align-content-center " style="gap: 8px">
                        <ion-icon wire:ignore class="icon-service-urgency" style="margin-bottom: 4px; min-width: 20px;"
                                  name="notifications-circle-outline">
                        </ion-icon>
                        <a href="tel:{{$service->claims}}">
                            {{$service->claims}}
                        </a>
                    </div>
                @endif
                <a href="tel:{{$service->office_phone}}">
                    {{trim($service->office_phone)}}
                </a>

            </td>

            <td title="{{$service->service}}"
                onclick="showService('{{$service->id}}')">
                {{\Str::substr($service->service, 0, 20)}}</td>
        </tr>
    @empty
        @if(!$administrator)
            <tr>
                <td colspan="5" style="height: 200px;">
                    <div class="d-flex justify-content-center align-items-center flex-column">
                        <span>
                            <ion-icon name="information-circle-outline" style="color:#727D8A;font-size: 22px !important" role="img" class="md hydrated" aria-label="information circle outline">
                            </ion-icon>
                        </span>
                        <label class="lbl-no-content-building-tables" style="margin-top:5px; font-size: 12px;">
                            @if($search)
                                No se encuentran resultados
                            @else
                                Aún no hay servicios
                            @endif
                        </label>
                    </div>
                </td>
            </tr>
        @endif
    @endforelse
    </tbody>
</table>

@push('after_styles')
    <style>
        .name-color {
            color: #467fd0;
        }

        .header-table-contact {
            padding: 24px 24px;
            background-color: white;
            display: flex;
            gap: 0.7rem;
            align-items: center;
        }

        .btn-new-case {
            display: flex;
            padding: 8px 16px;
            border-radius: 50px;
            background: var(--Primarios-Azul-oscuro-Foxsys);
            color: white;
            width: max-content;
            min-width: 141px;
        }

        .search-container {
            display: flex;
            align-items: center;
            border: 1px solid #ccc;
            border-radius: 50px;
            padding: 10px 12px;
            background-color: #fff;
            width: 230px;
            height: 40px;
            position: relative;
        }

        .search-input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 14px;
            color: #333;
        }

        .shortcuts-container {
            display: flex;
            align-items: center;
            gap: 8px;
            position: absolute;
            right: 12px;
        }

        .search-input::placeholder {
            color: #aaa;
        }

        .shortcuts {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 12px;
            color: #555;
        }

        .shortcuts span {
            background: var(--Secundarios-Gris-claro-5);
            border-radius: 4px;
            padding: 4px 6px;
            color: #727D8A;
            font-feature-settings: 'liga' off, 'clig' off;
            font-size: 9px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;

        }

        .search-button {
            display: flex;
        }

        .search-button ion-icon {
            zoom: 1;
            color: black;

        }

        table tr:nth-child(even) td {
            background-color: white !important;
        }

        table tr:nth-child(odd) td {
            background-color: #FBFBFC !important;

        }

        .table thead th {
            position: sticky;
            top: 0;
            background-color: #f4f4f4;
            border-bottom: 2px solid #ccc;
            z-index: 10;
            text-align: left;
            color: #333;
            font-weight: bold;
            padding: 1rem !important;
            gap: 15px;
        }

        .dark-layout {

            table tr:nth-child(odd) td {
                background-color: #27272B !important;
            }

            table tr:nth-child(even) td {
                background-color: #1C1C24 !important;
            }
        }

        table.dataTable td,
        table.dataTable th {
            padding-right: 0;
            padding-top: 16px;
            padding-bottom: 16px;
            vertical-align: middle;
        }
    </style>
@endpush

@push('after_scripts')
    <script>


        $(document).on('mousedown', '#service-row', (e) => {
            if (e.which == 2) {
                if (e.which == 2) {
                    openWindow('service', e.currentTarget.dataset.service)
                }
            }
        })

        $(document).on('mousedown', '.administration-name', (e) => {
            if (e.which == 2) {
                if (e.which == 2) {
                    openWindow('administrator', e.currentTarget.dataset.administration)
                }
            }
        })

    </script>
@endpush

