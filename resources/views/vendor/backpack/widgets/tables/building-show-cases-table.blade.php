<table
    class="table table-hover building-services-table index-table-height compact nowrap table-full-width dataTable no-footer"
    style="width: 100% !important; max-height: 100px;" id="cases-table">


    <thead style="position: relative; z-index: 9">
    <tr>
        <th style="width:50px; cursor: pointer;" class="text-capitalize" scope="col" wire:click="sortBy('id')">ID <i
                class="la la-sort"></i></th>
        <th style="width:40px; cursor: pointer;" class="text-capitalize" scope="col" wire:click="sortBy('flat_number')"
            title="Apartamento">APTO. <i class="la la-sort"></i></th>
        <th style="width:100px; cursor: pointer;" class="text-capitalize" scope="col"
            wire:click="sortBy('last_category_name')">CATEGORÍA <i class="la la-sort"></i></th>
        <th style="width:80px" class="text-capitalize" scope="col">TÍTULO</th>
        <th style="width:120px" class="text-capitalize" scope="col">ÚLTIMO COMENTARIO</th>
        <th style="width:40px" class="text-capitalize" scope="col">ACCIONES</th>
    </tr>
    </thead>
    <tbody id="loader-tbody-cases" class="d-none">
    <tr>
        <td colspan="6" class="text-center loader-cell">
            <span class="loader"></span>
        </td>
    </tr>
    </tbody>
    <tbody id="data-tbody-cases">
    @forelse($cases as $case)
        <tr wire:key="case-{{$case['id']}}">
            <td style="padding: 15px 0px 15px 10px;">
                <div class="row">
                    <div class="col-md-5 case-number-in-crud-table-building-case"><a id="case_id_to_copy"
                                                                                     target="_blank" data-key="{{$case['id']}}"
                                                                                     href="/admin/case/{{$case['id']}}/info"></a>
                    </div>
                </div>
            </td>
            <td onclick="showCase('{{$case['id']}}')">{{$case['flat_number']}}</td>
            <td onclick="showCase('{{$case['id']}}')"
                title="{{\App\Services\CategoryService::getCategoryHierarchy($case['last_category_id'])}}">{{\Str::substr($case['last_category_name'], 0, 10)}}</td>
            <td onclick="showCase('{{$case['id']}}')" title="{{$case['title']}}">{{\Str::substr($case['title'], 0, 10)}}</td>
            <td onclick="showCase('{{$case['id']}}')"
                title="{{$case['last_comment']}}">{{\Str::substr($case['last_comment'], 0,20)}}</td>
            <td >
                <div class="d-flex align-items-center" style="gap: 4px;">
                    <a href="/admin/case/{{$case['id']}}/info" target="_blank"><i class="las la-eye cases-btn-actions action-case actions-buttons-color open-case" title="Ver caso" data-case="{{$case['id']}}"></i></a>
                    <a href="/admin/case/{{$case['id']}}/edit" target="_blank"><i class="las la-edit cases-btn-actions action-case actions-buttons-color open-case" title="Editar caso" data-edit="true" data-case="{{$case['id']}}"></i></a>
                    <i class="las la-times-circle cases-btn-actions actions-buttons-color" title="Cerrar caso" onclick="showPopUpDelete({{$case['id']}})"></i>
                </div>
            </td>
        </tr>
    @empty
        <tr>
            <td colspan="6" style="height: 200px;">
                <div class="d-flex justify-content-center align-items-center flex-column">
                    <span>
                        <ion-icon name="information-circle-outline" style="color:#727D8A;font-size: 22px !important" role="img" class="md hydrated" aria-label="information circle outline">
                        </ion-icon>
                    </span>
                    <label class="lbl-no-content-building-tables" style="margin-top:5px; font-size: 12px;">
                        @if($search)
                            No se encuentran resultados
                        @else
                            Aún no hay casos
                        @endif
                    </label>
                </div>
            </td>
        </tr>
    @endforelse
    </tbody>

</table>

@push('after_styles')
    <style>
        .cases-btn-actions {
            color: #3289DA;
            font-size: 16px;
        }

        .dark-layout .cases-btn-actions {
            color: white;
        }

        .table thead th {
            background-color: #f4f4f4;
            color: #333;
            font-weight: bold;
            text-align: left;
            /*padding: !important;*/
            gap: 15px;

        }

        .header-table-contact {
            padding: 24px 24px;
            background-color: white;
            display: flex;
            gap: 0.7rem;
            align-items: center;
        }

        .btn-new-case {
            display: flex;
            padding: 8px 16px;
            border-radius: 50px;
            background: var(--Primarios-Azul-oscuro-Foxsys);
            color: white;
            width: max-content;
            min-width: 141px;
        }

        .search-container {
            display: flex;
            align-items: center;
            border: 1px solid #ccc;
            border-radius: 50px;
            padding: 10px 12px;
            background-color: #fff;
            width: 230px;
            height: 40px;
            position: relative;
        }

        .search-input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 14px;
            color: #333;
        }

        .shortcuts-container {
            display: flex;
            align-items: center;
            gap: 8px;
            position: absolute;
            right: 12px;
        }

        .search-input::placeholder {
            color: #aaa;
        }

        .shortcuts {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 12px;
            color: #555;
        }

        .shortcuts span {
            background: var(--Secundarios-Gris-claro-5);
            border-radius: 4px;
            padding: 4px 6px;
            color: #727D8A;
            font-feature-settings: 'liga' off, 'clig' off;
            font-size: 9px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;

        }

        .search-button {
            display: flex;
        }

        .search-button ion-icon {
            zoom: 1;
            color: black;

        }

        table tr:nth-child(odd) td {
            background-color: white !important;
        }

        table tr:nth-child(even) td {
            background-color: #FBFBFC !important;

        }

        .table thead th {
            position: sticky;
            top: 0;
            background-color: #f4f4f4;
            border-bottom: 2px solid #ccc;
            z-index: 10;
            text-align: left;
            color: #333;
            font-weight: bold;
            padding: 1rem!important;
            gap: 15px;
        }

        .dark-layout {

            table tr:nth-child(odd) td {
                background-color: #27272B !important;
            }

            table tr:nth-child(even) td {
                background-color: #1C1C24 !important;
            }
        }
    </style>
@endpush

<script>
    document.addEventListener('livewire:update', () => {
        document.querySelectorAll('#case_id_to_copy').forEach((item) => {
           item.textContent = item.dataset.key
        })

    })


    function copyCaseNumber(number) {
        // event.preventDefault();
        navigator.clipboard.writeText(number);
        new Noty({
            type: "success",
            text: 'Texto copiado',
        }).show();
    }

    function copyUrlCase(id) {
        let url = 'Caso ' + id + ' - ' + window.origin + '/admin/case' + '/' + id + '/' + 'info';
        navigator.clipboard.writeText(url);
        new Noty({
            type: "success",
            text: 'Texto copiado',
        }).show();

    }

    let all_cases = []

    $('#state_table_cases').on('change', function () {
        var state = $(this).val();
        filterTableByState(state);
    });

    function filterTableByState(state) {

        let casesq = all_cases;
        cleanCasesTable();
        addCasesTableHeader();
        if (state != 'all') {
            let arrayCases = casesq.filter(objeto => objeto.state === state);
            arrayCases.forEach(addCasesToTable);
        } else {
            let arrayCases = casesq.filter(objeto => objeto.state != 'finalizado');
            arrayCases.forEach(addCasesToTable);
        }
    }


    // $(document).ready(function () {
    //     $("#search_cases").keyup(function () {
    //         _this = this;
    //         // Show only matching TR, hide rest of them
    //         $.each($("#cases-table tbody tr"), function () {
    //             if ($(this).text().toLowerCase().indexOf($(_this).val().toLowerCase()) === -1)
    //                 $(this).hide();
    //             else
    //                 $(this).show();
    //         });
    //     });
    // });

    var table = document.getElementById("cases-table").getElementsByTagName('tbody')[0];
    var row = table.insertRow(0);
    // var text = row.insertCell(0);
    // text.innerHTML = '';
    // text.className = 'dataTables_empty';
    // text.style.borderBottom = "0px;"


    {{--    Building expandable info --}}

    function showBuildingInfo(building_id) {
        var building = getBuildingInfo(building_id);

        return '<div class="col-md-12">' +

            '<div class="col-md-4 overflow-table-wrap"><img src=' + getBuildingImagePath(building) +
            ' height="200"></div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Tipo de edificio: </b>' + parseResponse(building
                .building_type) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Tipo de servicio: </b>' + parseResponse(building
                .service_type) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Dirección: </b>' + parseResponse(building.address) +
            '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Entre calles: </b>' + parseResponse(building
                .between_streets) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Comentarios: </b>' + parseResponse(building.comments) +
            '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>¿Pueden abrir de arriba? </b>' + parseResponse(building
                .opens_from_flat) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Respuesta física: </b>' + parseResponse(building.gamma_code) +
            '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Ubicación de medidores </b>' + parseResponse(building
                .measures_locations) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Horario de servicio: </b>' + parseResponse(building
                .doorman_service_hours) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Horario de porteros: </b>' + parseResponse(building
                .building_doorman_hours) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Comentarios de seguridad: </b>' + parseResponse(building
                .security_comments) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>¿Cómo llaman desde abajo? </b>' + parseResponse(building
                .doorman_format) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Nivel de servicio: </b>' + parseResponse(building
                .service_level) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Controles de rampa y puerta: </b>' + parseResponse(building
                .gates_ramps_controllers) + '</div>' +
            '</div>';
    }

    async function getBuildingCases(id) {

        var cases = '';
        $.ajax({
            url: '/admin/building/' + id + '/last-open-cases',
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                cases = data;
            },
        });
        return cases.data;
    }

    async function getTracingsCases(id) {
        let tracings = '';
        let element;
        let cases_filter = [];
        $.ajax({
            url: '/admin/building/' + id + '/last-open-tracings',
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                tracings = data;
            },
        });

        for (element of tracings.original.data) {
            if (element.last_category_name != "Comunicados") {
                cases_filter.push(element)
            }
        }
        return cases_filter;
    }

    //Contacts table laod
    async function showBuildingCases(building_id) {
        cleanCasesTable();
        addCasesTableHeader();
        let sorted_cases = await getBuildingCases(building_id);
        all_cases = sorted_cases;

        sorted_cases = sorted_cases.filter(objeto => objeto.state != 'finalizado');

        sorted_cases.forEach(addCasesToTable);


    }

    function addCasesTableHeader() {
        var table = document.getElementById("cases-table").getElementsByTagName('thead')[0];
        var row = table.insertRow(0);

        row.innerHTML = '<thead style="position: sticky;">\n' +
            '                    <tr>\n' +
            '                        <th scope="col" style="width:10%;" class="sorting pr-0 " onclick="sortTableShowCase(0)">Id <i class="la la-sort"></i></th>\n' +
            '                        <th scope="col" style="width:15%;" class="sorting pr-0 " onclick="sortTableShowCase(1)" title="Apartamento">Apto <i class="la la-sort"></i></th>\n' +
            '                        <th scope="col" style="width:20%; " class="sorting pr-0 " onclick="sortTableShowCase(2)">Categoría<i class="la la-sort"></i></th>\n' +
            '                        <th scope="col" style="width:45%; "  class="sorting pr-0 " onclick="sortTableShowCase(3)">Titulo <i class="la la-sort"></i></th>\n' +
            '                        <th scope="col" style="width: 15%"  class="sorting p-0" onclick="sortTableShowCase(4)">Acciones</th>\n' +
            '                    </tr>\n' +
            '                    </thead>';
    }

    function parseResponse(text) {
        if (text === undefined || text === null || text === '') {
            return '-';
        }
        return text;
    }

    function addCasesToTable(caseq) {
        var table = document.getElementById("cases-table").getElementsByTagName('tbody')[0];
        var row = table.insertRow(0);
        var number = row.insertCell(0);
        var flat = row.insertCell(1);
        var category = row.insertCell(2);
        var title = row.insertCell(3);
        var actions = row.insertCell(4);

        // Open case on row click
        Array.from(row.getElementsByTagName('td')).forEach((item, index) => {
            if (index !== 4) {
                item.addEventListener('click', () => {
                    window.open("/admin/case/" + caseq.id + "/show", "_blank")
                })
            }
        })

        number.style.width = '2rem';

        number.className = 'color-id-table-case ';
        number.style.fontWeight = 'bold';
        flat.style.width = '3rem';
        category.style.width = '4rem';
        actions.style.padding = '0px';
        actions.innerHTML = '<td>\n' +
            '                <a title="Ver" href="/admin/case/' + caseq.id + '/show"' +
            '                 class="btn btn-sm btn-link p-0"><ion-icon style="pointer-events:none" title="Ver" name="eye-outline"></ion-icon></a> ' +
            '                <a title="Editar" href="/admin/case/' + caseq.id + '/edit"' +
            '                 class="btn btn-sm btn-link p-0"><ion-icon style="pointer-events:none" title="Editar" name="create-outline"></ion-icon></a> ' +
            '                 <a href="#" onclick="showPopUpDelete(' + caseq.id + ')"' +
            '                 class="btn btn-sm btn-link p-0" data-button-type="close" title="Cerrar"><ion-icon style="pointer-events:none" name="close-circle-outline"></ion-icon> </a> ';

        name.innerHTML =
            '<button\n' +
            '                                        type="button"\n' +
            '                                        class="btn btn-link"\n' +
            '                                        data-toggle="modal"\n' +
            '                                        data-id="' + parseResponse(caseq.id) + '"' +
            '                                        data-case-number="' +
            '<a target=_blank href=/admin/contact/' + caseq.id + '/edit>' +
            parseResponse(caseq.number) + '</a>"' +
            '                                        data-number="' + parseResponse(caseq.number) + '"' +
            '                                        data-flat="' + parseResponse(caseq.flat_number) + '"' +
            '                                        data-category="' + parseResponse(caseq.last_category_name) + '"' +
            '                                        data-initial_date="' + parseResponse(caseq.initial_date) + '"' +
            '                                        data-title="' + parseResponse(caseq.title) + '"' +
            '                                        data-comments="' + parseResponse(caseq.comments) + '"' +
            //ahora es description
            '                                        data-backdrop="false"' +
            '                                        data-target="#buildingsModal">\n' +
            '                                        ' + caseq.number + '\n' +
            '                                    </button>';

        number.innerHTML = caseq.id;
        if (caseq.flat_number != undefined) {
            flat.innerHTML = caseq.flat_number;
        } else {
            flat.innerHTML = '';

        }
        category.innerHTML = caseq.last_category_name.slice(0, 10);
        category.title = caseq.last_category_name;

        if (caseq.title != null && caseq.title != undefined && caseq.title != '') {
            title.innerHTML = caseq.title.slice(0, 30);
            title.title = caseq.title;
        }

    }

    function commentFormatTitle(jcomment) {
        coments = JSON.parse(jcomment);
        text = '';
        if (coments == null || coments[0] == undefined) {
            text = 'No hay comentarios'
        } else {
            for (i = 0; i < coments.length; i++) {
                text = text + 'Usuario: ' + coments[i].user + ', ' + coments[i].comment + ' / '
            }
        }
        return text;
    }

    function commentFormat(jcomment) {
        coments = JSON.parse(jcomment);
        text = '';
        if (coments == null || coments[0] == undefined) {
            text = 'No hay comentarios'
        } else {
            for (i = coments.length - 1; i < coments.length; i++) {
                text = text + 'Usuario: ' + coments[i].user + ', ' + coments[i].comment + '<br>'
            }
        }
        return text;
    }

    function cleanCasesTable() {
        $("#cases-table thead").empty();
        $("#cases-table tbody").empty();
    }

    function sortTableShowCase(n) {
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("cases-table");
        switching = true;
        // Set the sorting direction to ascending:
        dir = "asc";
        /* Make a loop that will continue until
        no switching has been done: */
        while (switching) {
            // Start by saying: no switching is done:
            switching = false;
            rows = table.rows;
            /* Loop through all table rows (except the
            first, which contains table headers): */
            for (i = 1; i < (rows.length - 1); i++) {
                // Start by saying there should be no switching:
                shouldSwitch = false;
                /* Get the two elements you want to compare,
                one from current row and one from the next: */
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                /* Check if the two rows should switch place,
                based on the direction, asc or desc: */
                if (dir == "asc") {
                    if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                        // If so, mark as a switch and break the loop:
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                        // If so, mark as a switch and break the loop:
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            if (shouldSwitch) {
                /* If a switch has been marked, make the switch
                and mark that a switch has been done: */
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                // Each time a switch is done, increase this count by 1:
                switchcount++;
            } else {
                /* If no switching has been done AND the direction is "asc",
                set the direction to "desc" and run the while loop again. */
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }

    function showPopUpDelete(id) {
        if (confirm("¿Estas seguro que desea cerrar el caso " + id + "?")) {
            $.ajax({
                url: '/admin/close_case_dashboard/' + id,
                method: "GET",
                success: function (data) {
                    if (!data) {
                        new Noty({
                            type: "success",
                            text: 'Caso ' + id + ' cerrado correctamente',
                        }).show();
                        Livewire.emit('updateCases')
                    } else {
                        new Noty({
                            type: "error",
                            text: data.message,
                        }).show();
                    }
                }
            })
        }
    }
</script>

<style>
    table.dataTable td,
    table.dataTable th {
        padding-right: 0;
        padding-top: 16px;
        padding-bottom: 16px;
        vertical-align: middle;
    }
</style>
