<table
    class="table table-striped building-contacts-table building-services-table index-table-height compact nowrap table-full-width dataTable no-footer"
    style="width: 100% !important;" id="contacts-table">


    <thead>
    <tr>
        <th scope="col" style="width:136px; padding-left: 13px;" class="sorting" onclick="sortTable(1)"><i title="Foto" class="las la-camera"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nombre</th>
        <th scope="col" style="width:90px" class="sorting" onclick="sortTable(2)">Dto.</th>
        <th scope="col" style="width:45px"  class="sorting" onclick="sortTable(3)" title="Apartamento">Apto.</th>
        <th scope="col" style="width:81px" class="sorting" onclick="sortTable(4)" title="Propietario o Inquilino">Relación</th>
        <th scope="col" style="width:15px"  class="sorting" onclick="sortTable(5)" title="No Llamar"><i class="las la-phone-slash la-9x"></i></th>
        <th scope="col" style="width:50px"  class="sorting" onclick="sortTable(5)" title="Referente">Ref.</th>
        <th scope="col" style="width:85px" class="sorting" onclick="sortTable(6) " title="Telefono">Tel.</th>
        <th scope="col" style="width:110px" class="sorting" onclick="sortTable(7)">Tipo</th>
    </tr>
    </thead>
    <tbody>

    </tbody>
</table>

{{--            <h2> Edificios </h2>--}}
{{--            {!! $widget['content'] !!}--}}


<script>
    {{--    Building expandable info--}}
    var global_var_building_id;

    function showBuildingInfo(building_id) {
        var building = getBuildingInfo(building_id);
        return '<div class="col-md-12">' +
            '<div class="col-md-4 overflow-table-wrap"><img src=' + getBuildingImagePath(building) + ' height="200"></div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Tipo de edificio: </b>' + parseResponse(building.building_type) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Tipo de servicio: </b>' + parseResponse(building.service_type) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Dirección: </b>' + parseResponse(building.address) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Entre calles: </b>' + parseResponse(building.between_streets) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Comentarios: </b>' + parseResponse(building.comments) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>¿Pueden abrir de arriba? </b>' + parseResponse(building.opens_from_flat) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Respuesta física: </b>' + parseResponse(building.gamma_code) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Ubicación de medidores </b>' + parseResponse(building.measures_locations) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Horario de servicio: </b>' + parseResponse(building.doorman_service_hours) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Horario de porteros: </b>' + parseResponse(building.building_doorman_hours) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Comentarios de seguridad: </b>' + parseResponse(building.security_comments) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>¿Cómo llaman desde abajo? </b>' + parseResponse(building.doorman_format) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Nivel de servicio: </b>' + parseResponse(building.service_level) + '</div>' +
            '<div class="col-md-4 overflow-table-wrap"><b>Controles de rampa y puerta: </b>' + parseResponse(building.gates_ramps_controllers) + '</div>' +
            '</div>';
    }

    function getBuildingImagePath(building) {
        if (building.image) {
            return window.location.origin + '/' + building.image;
        }
        return window.location.origin + '/uploads/user_default.png';
    }

    function getBuildingContacts(id) {
        var contacts = '';
        $.ajax({
            url: '{{url('/admin/building/contacts/info')}}/' + id + '/minimized-contacts-for-dashboard',
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                contacts = data;
            },
            error: function (data) {
                console.log(data);
            }
        });
        return contacts;
    }

    function getBuildingContactsByType(id, type) {
        var contacts = '';
        $.ajax({
            url: '{{url('admin/building/contacts-by-type/info')}}' + '/' + id + '/' + type,
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                contacts = data;
            },
        });
        return contacts;
    }

    //Contacts table laod
    function showBuildingContacts(building_id) {
        global_var_building_id = building_id;
        var sorted_contacts = getBuildingContacts(building_id);
        cleanContactsTable();
        addContactTableHeader();
        sorted_contacts.forEach((element, index) => addContactToTable(element, index));
    }

    function addContactTableHeader() {
        var table = document.getElementById("contacts-table").getElementsByTagName('thead')[0];
        var row = table.insertRow(0);
        row.innerHTML = '<thead>\n' +
            '                    <tr>\n' +
            '                        <th scope="col" style="width:125px; padding-left: 13px;" class="sorting" onclick="sortTable(1)"><i title="Foto" class="las la-camera"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Nombre</th>\n' +
            '                        <th scope="col" style="width:80px" class="sorting" onclick="sortTable(2)">Dto.</th>\n' +
            '                        <th scope="col" style="width:45px"  class="sorting" onclick="sortTable(3)" title="Apartamento">Apto.</th>\n' +
            '                        <th scope="col" style="width:80px" class="sorting" onclick="sortTable(4)" title="Propietario o Inquilino">Relación</th>\n' +
            '                        <th scope="col" style="width:15px"  class="sorting" onclick="sortTable(5)" title="No Llamar"><i class="las la-phone-slash la-9x"></i></th>\n' +
            '                        <th scope="col" style="width:50px"  class="sorting" onclick="sortTable(5)" title="Referente">Ref.</th>\n' +
            '                        <th scope="col" style="width:85px" class="sorting" onclick="sortTable(6)" title="Telefono">Tel.</th>\n' +
            '                        <th scope="col" style="width:110px" class="sorting" onclick="sortTable(7)">Tipo</th>\n' +
            '                    </tr>\n' +
            '                    </thead>';
    }

    function addContactToTable(contact, index) {
        if (contact.hasOwnProperty('secondary_info')) {
            for (i = 0; i < contact.secondary_info.length; i++) {
                if (contact.secondary_info[i].building_id == global_var_building_id) {
                    contact.contact_type = contact.secondary_info[i].contact_type;
                }
            }
        }
        var table = document.getElementById("contacts-table").getElementsByTagName('tbody')[0];
        var row = table.insertRow(0);
        var image = row.insertCell(0);
        var name = row.insertCell(1);
        var ci = row.insertCell(2);
        var flat = row.insertCell(3);
        var flat_number = 'S/A';
        if (contact.flat_number != null) {
            flat_number = contact.flat_number;
        }
        var owner_or_tenant = row.insertCell(4);
        var dont_call = row.insertCell(5);
        var referrer = row.insertCell(6);
        var primary_phone = row.insertCell(7);
        var contact_type = row.insertCell(8);
        var original_order_column = row.insertCell(9);
        original_order_column.innerHTML = index;
        original_order_column.style.display = 'none';
        image.style.width = '14px';
        name.style.width = '65px';
        ci.style.width = '55px';
        flat.style.width = '30px';
        owner_or_tenant.style.width = '47px';
        dont_call.style.width = '25px';
        referrer.style.width = '25px';
        primary_phone.style.width = '50px';
        contact_type.style.width = '75px';
        if(contact.contact_type === 'Prohibido Ingreso'){
            contact_type.style.backgroundColor = '#800303';
            contact_type.style.color = 'white';
            contact_type.style.borderRadius = '7px';
        }
        var foto = document.createElement("img");
        foto.src = getImagePath(contact.image);
        foto.style.width = '30px';
        foto.style.height = '30px';
        image.appendChild(foto);
        name.innerHTML =
            '<a target="_blank" href="contact/' + contact.id + '/info">' +
            '                                        ' + contact.complete_name + '\n' +
            '                                    </a>';
        flat.innerHTML = flat_number;
        owner_or_tenant.innerHTML = contact.owner_or_tenant;
        if (contact.dont_call) {
            //dont_call.style.backgroundColor   = '#800303';
            dont_call.innerHTML = '<i style="color: red;" class="nav-icon la la-times-circle"></i>';
        }
        if (contact.referrer) {
            //referrer.style.backgroundColor = '#137507';
            referrer.innerHTML = '<i style="color: green;" class="nav-icon la la-check-circle"></i>';
        }
        ci.innerHTML = contact.ci;


        primary_phone.innerHTML = '';
        if (contact.principal_phone !== '') {
            primary_phone.innerHTML = ' <a onclick="createInteractionCall(' + contact.id + ',' + contact.principal_phone + ')"><span id="fav-admin_type" class="bold">' + formatPhone(contact.principal_phone) + '</span></a>';
        }

        contact_type.innerHTML = contact.contact_type;
        if (contact.contact_type === 'Contacto Principal - Comisión') {
            contact_type.innerHTML = 'Cto. Ppal. - Comisión';
        }
    }

    function getImagePath($image) {
        if ($image != null) {
            $path = '/' + $image;
            return $path;
        }
        $path = '\\uploads\\user_default.png';
        return $path
    }

    function cleanContactsTable() {
        $("#contacts-table thead").empty();
        $("#contacts-table tbody").empty();
    }

    function formatPhone(phone) {
        if (phone) {
            phone = phone.replace(/ /g, ""); //remove blank spaces
            let cellphone_length = 9;
            let homephone_length = 8;
            if (phone.startsWith('09') && phone.length === cellphone_length) {
                phone = phone.slice(0, 3) + ' ' + phone.slice(3, 6) + ' ' + phone.slice(6, 9);
            } else if (phone.startsWith('2') && phone.length === homephone_length) {
                phone = phone.slice(0, 4) + ' ' + phone.slice(4, 8);
            }
            return phone;
        }
        return '';
    }

    function sortTable(n) {
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("contacts-table");
        switching = true;
        // Set the sorting direction to ascending:
        dir = "asc";
        /* Make a loop that will continue until
        no switching has been done: */
        while (switching) {
            // Start by saying: no switching is done:
            switching = false;
            rows = table.rows;
            /* Loop through all table rows (except the
            first, which contains table headers): */
            for (i = 1; i < (rows.length - 1); i++) {
                // Start by saying there should be no switching:
                shouldSwitch = false;
                /* Get the two elements you want to compare,
                one from current row and one from the next: */
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                /* Check if the two rows should switch place,
                based on the direction, asc or desc: */
                if (dir == "asc") {
                    if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                        // If so, mark as a switch and break the loop:
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                        // If so, mark as a switch and break the loop:
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            if (shouldSwitch) {
                /* If a switch has been marked, make the switch
                and mark that a switch has been done: */
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                // Each time a switch is done, increase this count by 1:
                switchcount++;
            } else {
                /* If no switching has been done AND the direction is "asc",
                set the direction to "desc" and run the while loop again. */
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }

    function createInteractionCall(id, phone_mobile) {
        phone_mobile = phone_mobile.toString();
        var parametros = '';
        $.ajax({
            url: '/createiteractioncall/' + id + '/' + phone_mobile,
            type: 'GET',
            dataType: "text",
            async: false,
            success: function (data) {
                if (phone_mobile.substring(0, 1) == 9) {
                    window.location = "Tel:0" + phone_mobile;
                } else {
                    window.location = "Tel:" + phone_mobile;
                }
            },
        });
        // return category;
    }
</script>


