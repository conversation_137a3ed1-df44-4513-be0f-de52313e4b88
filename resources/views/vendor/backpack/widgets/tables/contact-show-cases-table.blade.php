<div class="cases-table-header">
    <div>

        <a id="filter-button-1" onclick="showContactCases()"
           class="btn btn-link waves-effect waves-float waves-light filter-button-selected"><i
                style="margin-right: 5px;" class="la la-search"></i>Todos</a>
        <a id="filter-button-2" onclick="getCasesOpen()"
           class="btn btn-link  waves-effect waves-float waves-light color-red-cases"> Pendiente</a>
        <a id="filter-button-3" onclick="getCasesCurse()"
           class="btn btn-link  waves-effect waves-float waves-light color-blue-cases">En curso</a>
        <a id="filter-button-4" onclick="getCasesInfo()"
           class="btn btn-link  waves-effect waves-float waves-light color-orange-cases">Solicitar Info</a>
        <a id="filter-button-5" onclick="getCasesWarning()"
           class="btn btn-link waves-effect waves-float waves-light color-green-cases">Aviso al cliente</a>
        <a id="filter-button-6" onclick="getCasesClose()"
           class="btn btn-link  waves-effect waves-float waves-light color-grey-cases">Finalizados</a>
    </div>
    <div class="col-md-3" style="padding-left: 3%; ">
        <input class="form-control" id="search_cases" type="text"
               style="border-radius: 6px; border: solid 1px #bdbdbd; height: 32px; width: 100%; position: relative"
               placeholder="Buscar Casos">
        <i class="la la-search" style="position: absolute;right: 25px;top: 9px;"></i>
        <p style="font-size: x-small; color: grey; margin: 0px; padding-left: 5px; margin-bottom: -10px;">Ctrl + 3</p>
    </div>
</div>


<div id="contact-cases-table" class="row tupakl"
     style="height: 26vh; overflow-y: scroll; overflow-x: hidden; font-size: 14px;">
    <table
        class="table table-striped building-contacts-table building-services-table index-table-height compact nowrap table-full-width dataTable no-footer"
        style="width: 100% !important; font-size: 14px; max-height: 100px;" id="cases-table">


        <thead style="position: relative; z-index: 9;">
        <tr>
            <th style="width:50px" scope="col">Id</th>
            <th style="width:60px" scope="col" title="Apartamento">Apto.</th>
            <th style="width:60px" scope="col">Categoria</th>
            <th style="width:60px" scope="col">Asunto</th>
            <th style="width:60px" scope="col">Inicio</th>
            <th style="width:170px" scope="col">Comentario</th>
            <th style="width:170px" scope="col">Estado</th>
            <th style="width:60px" scope="col">Acciones</th>
        </tr>
        </thead>
        <tbody>

        </tbody>
    </table>


    <script>
        user_id = contacts && contacts[0] ? contacts[0].contact_id : null;
        let next_page_url_contact_cases = null
        const SCROLL_CONTACT_CASES_NEXT_PAGE_URL = 'scroll_contact_cases_next_page'
        const flatId = new URLSearchParams(window.location.search).get('flat_id')
        let state = 'todos'
        let current_page_url_contact_cases = '{{url("/admin/flat/$contact->id/cases")}}' + '/' + flatId + '/flat/state'

        const cases_table = document.getElementById("contact-cases-table")
        let searchCases = document.getElementById('search_cases')

        async function getCases() {
            next_page_url_contact_cases = window.localStorage.getItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL) ?? null
            const scrollHeight = cases_table.scrollHeight
            const scrollTop = cases_table.scrollTop
            const clientHeight = cases_table.clientHeight

            if (scrollTop + clientHeight >= scrollHeight - 10) {
                if (null != next_page_url_contact_cases && "null" != next_page_url_contact_cases) {
                    let currentContactCasesUrl = searchCases.value.trim().length >= 2
                        ? current_page_url_contact_cases + '/' + state + next_page_url_contact_cases + "&search=" + searchCases.value.trim()
                        : current_page_url_contact_cases + '/' + state + next_page_url_contact_cases
                    await getContactCasesScroll(currentContactCasesUrl)
                }
            }
        }

        async function getContactCasesScroll(url) {
            $.ajax({
                url: url,
                dataType: "json",
                success: function (data) {
                    const {data: paginated_contact_cases, next_page_url} = data.original.data
                    next_page_url_contact_cases = next_page_url
                    window.localStorage.setItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL, next_page_url_contact_cases)
                    paginated_contact_cases?.forEach(addCasesToTable)
                }
            })
        }

        cases_table.addEventListener('scroll', getCases)

        {{--    Building expandable info--}}
        function showBuildingInfo(building_id) {
            var building = getBuildingInfo(building_id);

            return '<div class="col-md-12">' +

                '<div class="col-md-4 overflow-table-wrap"><img src=' + getBuildingImagePath(building) + ' height="200"></div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Tipo de edificio: </b>' + parseResponse(building.building_type) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Tipo de servicio: </b>' + parseResponse(building.service_type) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Dirección: </b>' + parseResponse(building.address) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Entre calles: </b>' + parseResponse(building.between_streets) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Comentarios: </b>' + parseResponse(building.comments) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>¿Pueden abrir de arriba? </b>' + parseResponse(building.opens_from_flat) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Respuesta física: </b>' + parseResponse(building.gamma_code) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Ubicación de medidores </b>' + parseResponse(building.measures_locations) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Horario de servicio: </b>' + parseResponse(building.doorman_service_hours) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Horario de porteros: </b>' + parseResponse(building.building_doorman_hours) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Comentarios de seguridad: </b>' + parseResponse(building.security_comments) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>¿Cómo llaman desde abajo? </b>' + parseResponse(building.doorman_format) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Nivel de servicio: </b>' + parseResponse(building.service_level) + '</div>' +
                '<div class="col-md-4 overflow-table-wrap"><b>Controles de rampa y puerta: </b>' + parseResponse(building.gates_ramps_controllers) + '</div>' +
                '</div>';
        }

        function getContactCases(id, state = 'todos') {
            const flatId = new URLSearchParams(window.location.search).get('flat_id')
            let searchValue = ""
            if (searchCases.value.trim().length >= 2)
                searchValue = `?search=${searchCases.value.trim()}`
            else
                searchValue = ""
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '{{url("/admin/flat/$contact->id/cases")}}' + '/' + flatId + '/flat/state' + '/' + state + '/' + searchValue,
                    type: 'GET',
                    dataType: 'json',
                    success: function (data) {
                        const {data: paginated_contact_cases, next_page_url} = data.original.data
                        window.localStorage.setItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL, next_page_url)
                        resolve(paginated_contact_cases);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        reject(errorThrown);
                    }
                });
            });
        }

        function getContactCasesOpen(id) {
            getContactCases(id, 'pendiente')
                .then(cases => {
                    cleanCasesTable();
                    addCasesTableHeader();
                    cases.forEach(addCasesToTable);
                })
                .catch(error => {
                    console.error('Failed to load cases:', error);
                });
        }

        function getContactCasesCurse(id) {
            window.localStorage.setItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL, null)
            var cases = '';
            $.ajax({
                url: '/admin/flat/' + id + '/cases/curse/' + current_flat_id,
                type: 'GET',
                dataType: "json",
                async: false,
                success: function (data) {
                    cases = data;
                },
            });
            return cases.original.data;
        }

        function getContactCasesClose(id) {
            showContactCases('finalizado')
        }

        function getContactCasesWarning(id) {
            window.localStorage.setItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL, null)
            var cases = '';
            $.ajax({
                url: '/admin/flat/' + id + '/cases/warning/' + current_flat_id,
                type: 'GET',
                dataType: "json",
                async: false,
                success: function (data) {
                    cases = data;
                },
            });
            return cases.original.data;
        }

        function getContactCasesInfo(id) {
            window.localStorage.setItem(SCROLL_CONTACT_CASES_NEXT_PAGE_URL, null)
            var cases = '';
            $.ajax({
                url: '/admin/flat/' + id + '/cases/sinfo/' + current_flat_id,
                type: 'GET',
                dataType: "json",
                async: false,
                success: function (data) {
                    cases = data;
                },
            });
            return cases.original.data;
        }

        function getCasesOpen() {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-2').classList.add("filter-button-selected");
            state = 'pendiente'
            showContactCases('pendiente')
        }

        function getCasesCurse() {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-3').classList.add("filter-button-selected");
            state = 'en_curso'
            showContactCases('en_curso')
        }

        function getCasesClose() {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-6').classList.add("filter-button-selected");
            state = 'finalizado'
            showContactCases('finalizado')
        }

        function getCasesWarning() {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-5').classList.add("filter-button-selected");
            state = 'aviso_al_cliente'
            showContactCases('aviso_al_cliente')
        }

        function getCasesInfo() {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-4').classList.add("filter-button-selected");
            state = 'solicitar_info'
            showContactCases('solicitar_info')
        }

        //Contacts table laod
        function showContactCases(caseState = '') {
            if(caseState.trim() === '')
                state = 'todos'
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-1').classList.add("filter-button-selected");
            cleanCasesTable();
            addCasesTableHeader();
            getContactCases(user_id, state)
                .then(cases => {
                    cases.forEach(addCasesToTable);
                })
                .catch(error => {
                    console.error('Failed to load cases:', error);
                });

        }

        async function showContactCasesInOtherFlat(flat_id) {
            document.getElementsByClassName('filter-button-selected')[0].classList.remove("filter-button-selected");
            document.getElementById('filter-button-1').classList.add("filter-button-selected");
            var sorted_cases = await getContactCasesInNotPrincipalBuilding(user_id, flat_id);
            cleanCasesTable();
            addCasesTableHeader();
            sorted_cases.forEach(addCasesToTable);
        }


        function addCasesTableHeader() {
            var table = document.getElementById("cases-table").getElementsByTagName('thead')[0];
            var row = table.insertRow(0);

            row.innerHTML = '<thead style="position: sticky;">\n' +
                '                    <tr>\n' +
                '                        <th scope="col" style="width:55px;" class="sorting " onclick="sortTableShowCase(0)">Id <i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:95px; padding-left: 0px;" class="sorting " onclick="sortTableShowCase(1) title="Apartamento"">Apto.<i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:95px; padding-left: 0px;" class="sorting " onclick="sortTableShowCase(2)">Categoría<i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:95px; padding-left: 10px;" class="sorting " onclick="sortTableShowCase(3)">Inicio<i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:235px; padding-left: 0px;"  class="sorting " onclick="sortTableShowCase(4)">Descripción<i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:235px;"  class="sorting " onclick="sortTableShowCase(5)">Estado<i class="la la-sort"></i></th>\n' +
                '                        <th scope="col" style="width:200px; padding-left: 0px; padding-right: 21px;"  class="sorting table-show-bottom-titleNOUSA" onclick="sortTableShowCase(6)">Acciones</th>\n' +
                '                    </tr>\n' +
                '                    </thead>';
        }

        function parseResponse(text) {
            if (text === undefined || text === null || text === '') {
                return '-';
            }
            return text;
        }

        function addCasesToTable(caseq) {
            var table = document.getElementById("cases-table").getElementsByTagName('tbody')[0];
            var row = table.insertRow(-1);
            var number = row.insertCell(0);
            var flat = row.insertCell(1);
            var category = row.insertCell(2);
            var start = row.insertCell(3);
            var description = row.insertCell(4);
            var state = row.insertCell(5);
            var actions = row.insertCell(6);


            // asunto.style.width = '25px';
            start.style.width = '200px';
            start.style.padding = '0px';
            state.style.width = '15px';
            number.style.width = '25px';
            flat.style.width = '25px';
            flat.style.padding = '0px';
            category.style.width = '155px';
            category.style.padding = '0px';
            description.style.width = '999px';
            description.style.padding = '5px';
            actions.style.width = '215px';
            actions.style.padding = '0px';
            actions.innerHTML = '<td>\n' +
                '                <a title="ver" href="/admin/case/' + caseq.id + '/show"' +
                '                 class="btn btn-sm btn-link" style="position: inherit;"><ion-icon style="pointer-events:none; font-size: 18px!important;" title="Ver" name="eye-outline"></ion-icon></a> ' +
                '                <a title="Editar" href="/admin/case/' + caseq.id + '/edit"' +
                '                 class="btn btn-sm btn-link" style="position: inherit;"><ion-icon style="pointer-events:none; font-size: 18px!important;" title="Editar" name="create-outline"></ion-icon></a> ' +
                '                 <a title="Cerrar" href="#" onclick="showPopUpDelete(' + caseq.id + ')"' +
                '                 class="btn btn-sm btn-link" data-button-type="close" style="position: inherit;"><ion-icon style="pointer-events:none; font-size: 18px!important;" name="close-circle-outline"></ion-icon>  </a> ';

            name.innerHTML =
                '<button\n' +
                '                                        type="button"\n' +
                '                                        class="btn btn-link"\n' +
                '                                        data-toggle="modal"\n' +
                '                                        data-id="' + parseResponse(caseq.id) + '"' +
                '                                        data-case-number="' +
                '<a target=_blank href=/admin/contact/' + caseq.id + '/edit>' +
                parseResponse(caseq.number) + '</a>"' +
                '                                        data-number="' + parseResponse(caseq.number) + '"' +
                '                                        data-flat="' + parseResponse(caseq.flat_number) + '"' +
                '                                        data-category="' + parseResponse(caseq.last_category.name) + '"' +
                '                                        data-initial_date="' + parseResponse(caseq.initial_date) + '"' +
                '                                        data-description="' + parseResponse(caseq.description) + '"' +
                '                                        data-comments="' + parseResponse(caseq.comments) + '"' +
                '                                        data-backdrop="false"' +
                '                                        data-target="#buildingsModal">\n' +
                '                                        ' + caseq.number + '\n' +
                '                                    </button>';

            number.innerHTML = '   <a href="/admin/case/' + caseq.id + '/show" class="c-blue bold">' + caseq.id + '</a>';
            // asunto.innerHTML = caseq.description;
            start.innerHTML = getCorrectDate(caseq.created_at);
            state.innerHTML = caseq.state;

            flat.innerHTML = caseq.flat_number;
            category.innerHTML = caseq.last_category.name.slice(0, 10);
            category.title = caseq.last_category.name;


            if (caseq.description != null && caseq.description != undefined && caseq.description != '') {
                description.innerHTML = caseq.description.slice(0, 30);
                description.title = caseq.description;
            }

            if (caseq.state == 'pendiente') {
                state.innerHTML = '<p class="case_table_state_style" style="background-color: var(--ColorRed)">' + caseq.state[0].toUpperCase() + caseq.state.substring(1).replace(/_/g, ' ') + '</p>';
            } else if (caseq.state == 'finalizado') {
                state.innerHTML = '<p class="case_table_state_style" style="background-color: var(--ColorGrey)">' + caseq.state[0].toUpperCase() + caseq.state.substring(1).replace(/_/g, ' ') + '</p>';
            } else if (caseq.state == 'solicitar_info') {
                state.innerHTML = '<p class=" case_table_state_style" style="background-color: var(--ColorOrange)">' + caseq.state[0].toUpperCase() + caseq.state.substring(1).replace(/_/g, ' ') + '</p>';
            } else if (caseq.state == 'aviso_al_cliente') {
                state.innerHTML = '<p class=" case_table_state_style" style="background-color: var(--ColorGreen)">' + caseq.state[0].toUpperCase() + caseq.state.substring(1).replace(/_/g, ' ') + '</p>';
            } else {
                state.innerHTML = '<p class=" case_table_state_style" style="background-color: var(--ColorBlue)">' + caseq.state[0].toUpperCase() + caseq.state.substring(1).replace(/_/g, ' ') + '</p>';
            }


        }

        function capitalize(s) {
            return s && s[0].toUpperCase() + s.slice(1).toLowerCase();
        }


        function commentFormatTitle(jcomment) {
            coments = JSON.parse(jcomment);
            text = '';
            if (coments == null || coments[0] == undefined) {
                text = 'No hay comentarios'
            } else {
                for (i = 0; i < coments.length; i++) {
                    text = text + 'Usuario: ' + coments[i].user + ', ' + coments[i].comment + ' / '
                }
            }
            return text;
        }

        function commentFormat(jcomment) {
            coments = JSON.parse(jcomment);
            text = '';
            if (coments == null || coments[0] == undefined) {
                text = 'No hay comentarios'
            } else {
                for (i = coments.length - 1; i < coments.length; i++) {
                    text = text + 'Usuario: ' + coments[i].user + ', ' + coments[i].comment + '<br>'
                }
            }
            return text;
        }

        function cleanCasesTable() {
            $("#cases-table thead").empty();
            $("#cases-table tbody").empty();
        }

        function sortTableShowCase(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.getElementById("cases-table");
            switching = true;
            // Set the sorting direction to ascending:
            dir = "asc";
            /* Make a loop that will continue until
            no switching has been done: */
            while (switching) {
                // Start by saying: no switching is done:
                switching = false;
                rows = table.rows;
                /* Loop through all table rows (except the
                first, which contains table headers): */
                for (i = 1; i < (rows.length - 1); i++) {
                    // Start by saying there should be no switching:
                    shouldSwitch = false;
                    /* Get the two elements you want to compare,
                    one from current row and one from the next: */
                    x = rows[i].getElementsByTagName("TD")[n];
                    y = rows[i + 1].getElementsByTagName("TD")[n];
                    /* Check if the two rows should switch place,
                    based on the direction, asc or desc: */
                    if (dir == "asc") {
                        if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
                if (shouldSwitch) {
                    /* If a switch has been marked, make the switch
                    and mark that a switch has been done: */
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    // Each time a switch is done, increase this count by 1:
                    switchcount++;
                } else {
                    /* If no switching has been done AND the direction is "asc",
                    set the direction to "desc" and run the while loop again. */
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
        }

        function showPopUpDelete(id) {
            if (confirm("¿Estas seguro que desea cerrar el caso?")) {
                $.ajax({
                    url: '/admin/close_case_dashboard/' + id,
                    method: "GET",
                    success: function (data) {
                        if (!data) {
                            new Noty({
                                type: "success",
                                text: 'Caso ' + id + ' cerrado correctamente',
                            }).show();
                            showContactCasesInOtherFlat(current_flat_id)
                        } else {
                            new Noty({
                                type: "error",
                                text: data.message
                            }).show()
                        }
                    },
                })
            }
        }

        function getCorrectDate($data) {
            reto = $data.slice(0, 10);
            return reto;
        }

    </script>

    <style>
        .select2-selection__rendered {
            width: 108%;
        }

        .case_table_state_style {
            color: white;
            border-radius: 4px;
            font-weight: 600;
            margin: 0px;
            text-align: center;
        }

    </style>
