<?php
$buildings = \App\Models\Building::where('lead',null)->get();
?>
<div class='col-sm-12 col-md-12 padding-10'>


    <div class="col-sm-6 col-md-6 no-padding">
        @include('.vendor.backpack.widgets.tables.buildings-table')
    </div>

    <div class="col-sm-6 col-md-6">
        <div class="card-body p-0 tabcontent">
            <div class="card mb-2">
                <div class="card-body container">


                    <div class="col-md-12 mb-3">


                        <div class="col-md-6" style="padding: 0">
                            <label class="text-left">Contactos</label>
                        </div>

                        <div class="col-md-3">
                            <input type="text" id="contactsByFlatSearchInput" name="contactsByFlatSearchInput"
                                   onkeyup="searchContactsAndTemporalsByFlatNumber()"
                                   placeholder="Buscar Nro. Apto"
                                   class="col-sm-12 custom-datatable-filter dataTables_filter float-right mb-3 form-control"/>
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="searchInput" name="extrasSearchInput"
                                   onkeyup="searchAllTables()"
                                   placeholder="Buscar General"
                                   class="col-sm-12 custom-datatable-filter dataTables_filter float-right mb-3 form-control"/>
                        </div>
                        <div class="index-table-height" id="contacts">
                            <div id="contacts-table_wrapper" class="dataTables_wrapper no-footer" style="max-height: 220px; overflow-y: auto">
                                @include('.vendor.backpack.widgets.tables.building-contacts-table')
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="text-left">Permisos Temporales Activos</label>
                        <div class="index-table-height" id="temporary-contacts">
                            <div id="temporarycontacts-table_wrapper" class="dataTables_wrapper no-footer" style="max-height: 220px; overflow-y: auto">
                                @include('.vendor.backpack.widgets.tables.building-temporary-contacts-table')
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="text-left">Servicios</label>
                        <div class="index-table-height" id="services">
                            <div id="services-table_wrapper" class="dataTables_wrapper no-footer" style="max-height: 220px; overflow-y: auto">
                                @include('.vendor.backpack.widgets.tables.building-services-table')
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    @include('.vendor.backpack.widgets.tables.contact-modal')
    @include('.vendor.backpack.widgets.tables.temporary-contact-modal')

</div>

<script>
    //    JAVASCRIPT SEARCH
    function searchAllTables() {
        search('contacts-table');
        search('services-table');
        search('temporary-contacts-table');
    }

    let selected_building = ''

    function search(table_name) {
        // Declare variables
        var filter, filterWords, table, td, i, j, k, txtValue, found;
        input = document.getElementById('searchInput');

        if (table_name === 'buildings-table') {
            input = document.getElementById('buildingsSearchInput');
        }

        filter = removeAccents(input.value.toUpperCase());
        filterWords = splitStringWordsIntoArray(filter);

        table = document.getElementById(table_name);
        tr = table.getElementsByTagName('tr');
        var filtered = [];

        for (k = 0; k < filterWords.length; k++) {
            if (filterWords[k] != "") {

                for (i = 1; i < tr.length; i++) {
                    found = false;
                    td = tr[i].cells;
                    for (j = 0; j < td.length; j++) {
                        txtValue = td[j].innerText;
                        if (filterWords[k] != "" && removeAccents(txtValue.toUpperCase()).indexOf(filterWords[k]) > -1) {
                            found = true;
                        }
                    }
                    if (found) {
                        tr[i].style.display = "";
                        filtered.push(tr[i]);
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            } else if (filterWords[k] == "" && filterWords.length === 1) {

                for (i = 1; i < tr.length; i++) {
                    found = true;
                    td = tr[i].cells;
                    tr[i].style.display = "";
                    filtered.push(tr[i]);

                }
            }
        }

        setTimeout(function () {
            if (table_name === 'buildings-table') {
                var first_building_id = filtered[0].cells[0].innerHTML;
                $('#table-building-' + first_building_id).click();
            }
        }, 750);
    }

    function searchByName(table_name) {
        // Declare variables
        var filter, filterWords, table, td, i, j, k, txtValue, found, ocurrencies;
        input = document.getElementById('onlyBuildingsSearchInput');

        filter = removeAccents(input.value.toUpperCase());
        table = document.getElementById(table_name);
        tr = table.getElementsByTagName('tr');
        ocurrencies = [];


        for (i = 1; i < tr.length; i++) {
            found = false;
            td = tr[i].cells;
            for (j = 0; j < td.length; j++) {
                if (j === 0 || j === 2 || j === 4)
                    txtValue = td[j].innerText;
                if (j === 4)
                    txtValue = txtValue.replace(/[0-9]/g, '');

                if (removeAccents(txtValue.toUpperCase()).indexOf(filter) > -1) {
                    found = true;
                }
            }

            if (found) {
                tr[i].style.display = "";
            } else {
                tr[i].style.display = "none";
            }

        }
    }

    function validateAllOcurrencies(ocurrencies) {
        let allFound = true;

        for (i = 0; i < ocurrencies.length; i++) {
            if (ocurrencies[i] === false) {
                allFound = false;
            }
        }
        return allFound;
    }


    function splitStringWordsIntoArray(str) {
        return str.split(" ");
    }

    function searchContactsAndTemporalsByFlatNumber() {
        searchByFlatNumberWithSpecialOrder('contacts-table', 3);
        searchByFlatNumber('temporary-contacts-table', 1);
        searchByFlatNumber('cases-table', 1);
        searchByFlatNumber('tracing-table', 1);
    }

    function sortTableSortCondition(n,sort,int) {
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("contacts-table");
        switching = true;
        // Set the sorting direction to ascending:
        dir = sort;
        /* Make a loop that will continue until
        no switching has been done: */
        while (switching) {
            // Start by saying: no switching is done:
            switching = false;
            rows = table.rows;
            /* Loop through all table rows (except the
            first, which contains table headers): */
            for (i = 1; i < (rows.length - 1); i++) {
                // Start by saying there should be no switching:
                shouldSwitch = false;
                /* Get the two elements you want to compare,
                one from current row and one from the next: */
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                /* Check if the two rows should switch place,
                based on the direction, asc or desc: */
                if(int){
                    if (dir == "asc") {
                        if (parseInt(x.innerHTML.toLowerCase()) > parseInt(y.innerHTML.toLowerCase())) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (parseInt(x.innerHTML.toLowerCase()) < parseInt(y.innerHTML.toLowerCase())) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    }
                }else{
                    if (dir == "asc") {
                        if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                            // If so, mark as a switch and break the loop:
                            shouldSwitch = true;
                            break;
                        }
                    }
                }

            }
            if (shouldSwitch) {
                /* If a switch has been marked, make the switch
                and mark that a switch has been done: */
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                // Each time a switch is done, increase this count by 1:
                switchcount++;
            }
        }
    }

    function searchByFlatLogic(tr, flat_position, filter){
        for (i = 1; i < tr.length; i++) {
            found = false;
            td = tr[i].cells;
            txtValue = td[flat_position].innerText; // only flat number col
            txtValue = removeAccents(txtValue.toUpperCase());
            if (isNaN(parseInt(txtValue))) {
                //Client asked to filter with "contains" if the filter is not a number
                if (removeAccents(txtValue.toUpperCase()).indexOf(filter) > -1) {
                    found = true;
                }
            } else {
                if (filter === '' || txtValue === filter) {
                    found = true;
                }
            }

            if (found) {
                tr[i].style.display = "";
            } else {
                tr[i].style.display = "none";
            }
        }
    }

    function searchByFlatNumber(table_name, flat_position) {
        // Declare variables
        var filter, table, td, i, j, txtValue, found;
        input = document.getElementById('contactsByFlatSearchInput');

        filter = removeAccents(input.value.toUpperCase());
        table = document.getElementById(table_name);
        tr = table.getElementsByTagName('tr');

        searchByFlatLogic(tr,flat_position,filter);


    }

    function searchByFlatNumberWithSpecialOrder(table_name, flat_position) {
        // Declare variables
        var filter, table, td, i, j, txtValue, found;
        input = document.getElementById('contactsByFlatSearchInput');

        filter = removeAccents(input.value.toUpperCase());
        table = document.getElementById(table_name);
        tr = table.getElementsByTagName('tr');


        if(filter == ''){
            for (i = 1; i < tr.length; i++) {
                tr[i].style.display = "";
            }
            sortTableSortCondition(9,"desc",true);
        }else{
            searchByFlatLogic(tr,flat_position,filter);

            if (flat_position == 3) {
                sortTableSortCondition(6, "asc",false);
            }
        }

    }

    function getBuildingInfo(id) {
        var building = '';
        $.ajax({
            url: '{{url('/admin/building/info/')}}' + '/' + id,
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                building = data;
            },
        });

        return building;
    }

    function setBuildingActiveStatus(building) {
        var hours = building['schedule'];
        var first_hour = building['active_hours'][0]['active_hours'];
        var second_hour = building['active_hours'][0]['second_active_hours'];
        var isActive = false;

        if (buildingIsActive(hours)) {
            isActive = true;
        }
        // if (hourInSlider(JSON.parse(first_hour)) || hourInSlider(JSON.parse(second_hour))) {
        //     isActive = true;
        // }
        if (isActive === false) {
            document.getElementById('details-control-building-' + building['id']).style.backgroundImage = "url(/img/details_non_active.png)";
        }

    }

    function buildingIsActive(hours){ //Horarios como string en fomra de array con jsons
        hours = JSON.parse(JSON.stringify(hours));
        var fecha = new Date();
        var day = fecha.getDay();
        if(day==0){
            day = 7;
        }
        var hour = fecha.getHours();
        var minute = fecha.getMinutes();

        if(hours!=null){
            for(i = 0; i<hours.length; i++){
                if(hours[i]['inlineCheckbox'+day] == 1){
                    if(parseInt(hours[i]['timei1ser'].split(':')[0]) <= hour) {
                        if(parseInt(hours[i]['timef1ser'].split(':')[0]) > hour) {
                            return true;
                        }
                        else if(parseInt(hours[i]['timef1ser'].split(':')[0]) == hour){
                            if(parseInt(hours[i]['timef1ser'].split(':')[1])>= minute){
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    function hourInSlider(active_hours) {
        let currentDay = moment().format('dddd').toLowerCase();
        let currentHour = hourToMinutes(moment().tz('America/Montevideo').format("HH:mm"));
        let isInSlider = true;
        let hasCurrentDay = false;

        $.each(active_hours, function (day, hours) {
            let startHour = hourToMinutes(hours.split('-')[0]);
            let endHour = hourToMinutes(hours.split('-')[1]);


            if (day === currentDay) {
                hasCurrentDay = true;
                if (currentHour <= startHour || currentHour >= endHour) {
                    isInSlider = false;
                }

            }
        });
        return (hasCurrentDay && isInSlider);

    }

    function hourToMinutes(hour) {
        return parseInt(hour.split(':')[0]) * 60 + parseInt(hour.split(':')[1]);
    }

    function parseResponse(text) {
        if (text === undefined || text === null || text === '') {
            return '-';
        }
        return text;
    }

    $(document).ready(function () {


        setBuildingListStatuses();
        // Add event listener for opening and closing details
        $('#buildings-table tbody').on('click', 'td', function () {
            var td = $(this).closest('td');
            var tr = $(this).closest('tr');
            var row = buildings_table.row(tr);


            if (tr.hasClass('shown') && td.index() > 0) {
                hideRow(row, tr);
            } else {
                showRow(row, tr);
            }


        });


        // Add event listener for opening and closing details
        $('.table-building-ids').on('click', function () {
            var tr = $(this).closest('tr');
            var row = buildings_table.row(tr);
            hideRow(row, tr);
        });

        function showRow(row, tr) {
            tr.addClass('shown').siblings().removeClass('shown');
            tr.addClass('row-selected').siblings().removeClass('row-selected');

            selected_building = row.data()[6]

            showBuildingContacts(row.data()[6]);
            showBuildingServices(row.data()[6]);
            showBuildingTemporaryContacts(row.data()[6]);
            showBuildingCases(row.data()[6]);
            showBuildingTracing(row.data()[6]);

        }

        function hideRow(row, tr) {
            tr.removeClass('shown');

            $(row.selector.rows['0']).css("background-color", "white");

            cleanContactsTable();
            addContactTableHeader();

            cleanServicesTable();
            addServiceTableHeader();

            cleanTemporaryContactsTable();
            addTemporaryContactTableHeader();
        }

        function setBuildingListStatuses() {
            @foreach(\App\Models\Building::where('has_active_hours', true)->with('activeHours')->get() as $building)
            setBuildingActiveStatus({!! $building !!});
            @endforeach
        }

        var buildings_table = $('#buildings-table').DataTable({
            paging: false,
            searching: false,
            bInfo : false,
            lengthChange: false,
            fixedHeader: {
                header: true,
            },
            language: {
                emptyTable: 'No hay datos disponibles en la tabla',
            },
            "dom": '<"toolbar">frti'
        });

        var contacts_table = $('#contacts-table').DataTable({
            paging: false,
            searching: false,
            fixedHeader: {
                header: true,
            },
            language: {
                emptyTable: 'No hay datos disponibles en la tabla',

            },
            "dom": '<"toolbar">frt'
        });

        var services_table = $('#services-table').DataTable({
            paging: false,
            searching: false,
            fixedHeader: {
                header: true,
            },
            language: {
                searchPlaceholder: "Búsqueda general",
                search: "",
                emptyTable: 'No hay datos disponibles en la tabla',

            },
            "dom": '<"toolbar">frt'
        });

        var temporary_contacts_table = $('#temporary-contacts-table').DataTable({
            paging: false,
            searching: false,
            fixedHeader: {
                header: true,
            },
            language: {
                emptyTable: 'No hay datos disponibles en la tabla',

            },
            "dom": '<"toolbar">frt'
        });

        setInterval(function () {
            reloadAllTables();
            searchAllTables();
        }, 600000);

        setInterval(function () {
            setBuildingListStatuses();
        }, 300000);

        function reloadAllTables() {

            buildings_table.rows().every(function () {
                // If row has details expanded
                if (this.child.isShown()) {
                    cleanContactsTable();
                    addContactTableHeader();

                    cleanServicesTable();
                    addServiceTableHeader();

                    cleanTemporaryContactsTable();
                    addTemporaryContactTableHeader();

                    showBuildingContacts(this.data()[0]);
                    showBuildingServices(this.data()[0]);
                    showBuildingTemporaryContacts(this.data()[0]);
                }
            });
        }

        $('input[name=onlyBuildingsSearchInput]').on('keydown', function (evt) {
            if (evt.key === 'Tab') {
                evt.preventDefault();
                $('input[name=contactsByFlatSearchInput]').focus();
                return false;
            }
            cleanContactsTable();
            addContactTableHeader();

            cleanServicesTable();
            addServiceTableHeader();

            cleanTemporaryContactsTable();
            addTemporaryContactTableHeader();

            if (evt.key === 'Enter') {
                evt.preventDefault();


                var tr = $('#buildings-table tbody tr').filter(':visible').first().first();
                var td = tr.children('td:eq(0)');
                td.click();
                return false;
            }
        });

    });

</script>

<script>
    //DATATABLES SCRIPT

    if (typeof deleteEntry != 'function') {
        $("[data-button-type=delete]").unbind('click');

        function deleteEntry(button) {
            // ask for confirmation before deleting an item
            // e.preventDefault();
            var button = $(button);
            var route = button.attr('data-route');
            var row = $("#crudTable a[data-route='" + route + "']").closest('tr');

            swal({
                title: "Warning",
                text: "¿Está seguro que desea eliminar este elemento?",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Cancelar",
                        value: null,
                        visible: true,
                        className: "bg-secondary",
                        closeModal: true,
                    },
                    delete: {
                        text: "Eliminar",
                        value: true,
                        visible: true,
                        className: "bg-danger",
                    }
                },
            }).then((value) => {
                if (value) {
                    $.ajax({
                        url: route,
                        type: 'DELETE',
                        success: function (result) {
                            if (result != 1) {
                                // Show an error alert
                                swal({
                                    title: "No se pudo eliminar",
                                    text: "Ha ocurrido un error. Puede que el elemento no haya sido eliminado.",
                                    icon: "error",
                                    timer: 2000,
                                    buttons: false,
                                });
                            } else {
                                // Show a success message
                                swal({
                                    title: "Elemento eliminado",
                                    text: "El elemento ha sido eliminado de manera correcta.",
                                    icon: "success",
                                    timer: 2000,
                                    buttons: false,
                                });

                                // Hide the modal, if any
                                $('.modal').modal('hide');

                                // Remove the details row, if it is open
                                if (row.hasClass("shown")) {
                                    row.next().remove();
                                }

                                // Remove the row from the datatable
                                row.remove();
                            }
                        },
                        error: function (result) {
                            // Show an alert with the result
                            swal({
                                title: "No se pudo eliminar",
                                text: "Ha ocurrido un error. Puede que el elemento no haya sido eliminado.",
                                icon: "error",
                                timer: 1000,
                                buttons: false,
                            });
                        }
                    });
                }
            });

        }
    }


    function removeAccents(str) {
        var accents = 'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž';
        var accentsOut = "AAAAAAaaaaaaOOOOOOOooooooEEEEeeeeeCcDIIIIiiiiUUUUuuuuNnSsYyyZz";
        str = str.split('');
        var strLen = str.length;
        var i, x;
        for (i = 0; i < strLen; i++) {
            if ((x = accents.indexOf(str[i])) != -1) {
                str[i] = accentsOut[x];
            }
        }
        return str.join('');
    }


</script>

<style>
    td {
        height: 45px!important;
    }
</style>
