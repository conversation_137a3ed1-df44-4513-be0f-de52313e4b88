@php
    $value = data_get($entry, $column['name']);
    $value = is_array($value) ? json_encode($value) : $value;
    $column['escaped'] = $column['escaped'] ?? true;
    $column['limit'] = $column['limit'] ?? 1000;
    $column['prefix'] = $column['prefix'] ?? '';
    $column['suffix'] = $column['suffix'] ?? '';
    $column['text'] = $column['prefix'].Str::limit($value, 40, '[...]').$column['suffix'];
    $column['title'] = $column['prefix'].Str::limit($value, $column['limit'], '[...]').$column['suffix'];
@endphp

@if (isset($column['options']) && is_array($column['options']))
    @foreach ($column['options'] as $key => $valueOption)
        @if( $value == $valueOption)
            <div class="{{ isset($field['inline']) && $field['inline'] ? 'form-check-inline' : '' }} ">
                <div class="d-flex align-items-center">
                    <label
                        style="margin-left: -8px"
                        class="{{ isset($field['inline']) && $field['inline'] ? 'radio-inline' : '' }}  form-check-label font-weight-normal">
                        {{ $valueOption }}
                    </label>
                    @if (\App\Models\Building::$buildingServiceIcons[$valueOption]['type'] == 'ion')
                        <ion-icon class="icon-in-table-no-action col-md-2 p-0"
                                  style="pointer-events:none;"
                                  name="{{ \App\Models\Building::$buildingServiceIcons[$valueOption]['icon'] }}">
                        </ion-icon>
                    @elseif (\App\Models\Building::$buildingServiceIcons[$valueOption]['type'] == 'svg')
                        <object data="{{ asset('svg/' . \App\Models\Building::$buildingServiceIcons[$value]['icon'] . '.svg') }}"
                                type="image/svg+xml"
                                class="icon-in-table-no-action col-md-2 p-0"
                                style="pointer-events:none; height: 24px;">
                        </object>
                    @endif
                </div>
            </div>
        @endif
    @endforeach
@endif

