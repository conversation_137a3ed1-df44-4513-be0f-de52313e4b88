@extends(backpack_view('blank'))
@php
    $defaultBreadcrumbs = [
        trans('backpack::crud.admin') => url(config('backpack.base.route_prefix'), 'dashboard'),
        $crud->entity_name_plural => url($crud->route),
        trans('backpack::crud.list') => false,
    ];
    // if breadcrumbs aren't defined in the CrudController, use the default breadcrumbs
    $breadcrumbs = $breadcrumbs ?? $defaultBreadcrumbs;


    $hideCreateButtonConfig = [
        'Intercomunicador' => ['Representante Atención al Cliente'],
        'Permiso temporal' => []
    ];

    $currentModel = $crud->entity_name;
    $currentUserRole = backpack_user()->getRoleNames();
    $hideCreateButton = false;

    if (array_key_exists($currentModel, $hideCreateButtonConfig)) {
        foreach ($currentUserRole as $role) {
            if (in_array($role, $hideCreateButtonConfig[$currentModel]) || $hideCreateButtonConfig[$currentModel] == []) {
                $hideCreateButton = true;
            }
        }
    }
@endphp

@include('current-site-title', ['title' => ucfirst($crud->entity_name_plural)])


@section('header')
    <link href="/css/base/themes/dark-layout.css" rel="stylesheet">
@endsection

@section('content')
    <div id="container-fluid-id" class="container-fluid row"
         style="z-index: 11; top: 100px; top: 30px; width: 40%; padding-top: 2%; margin-bottom: 0%;">
        <div class="col-md-6">
             <span class="text-capitalize titleTables titleTableList col-md-6">
                    {!! $crud->getHeading() ?? $crud->entity_name_plural !!}
            </span>
        </div>
        <div class="text-right row col-md-6">
            @if (!$hideCreateButton)
                <a id="create_button_design" class="btn create-model-button" style="border-radius: 25px;width: auto"
                   href="{{ url($crud->route) . '/create' }}"
                   @if($crud->entity_name_plural == 'casos') target="_blank" @endif>
                    + Nuevo
                </a>
            @endif
            <div id="datatable_search_stack" class="mt-sm-0 mt-2 d-print-none"
                 style="width:100px; margin-top: 4px!important;"></div>
        </div>

        <div class="">
            <small class="out-in-mobile" id="datatable_info_stack">
                {!! $crud->getSubheading() ?? '' !!}
            </small>
        </div>

    </div>
    <!-- Default box -->
    <div class="row" style="overflow: hidden; height: 71vh">
        <!-- THE ACTUAL CONTENT -->
        <div class="{{ $crud->getListContentClass() }}" style="height: 100%">

            <div style=" margin-top: 20px;" class="separate-to-mobile row col-md-12">

                <div id="up-col12" style="z-index: 99999;">
                </div>
                {{-- Backpack List Filters --}}
                @if ($crud->filtersEnabled())
                    <div style="z-index: 99999;" class="margin-in-movil-height">
                        @include('crud::inc.filters_navbar')
                    </div>
                @endif

            </div>


            <div id="div-table" class="contenedor"
                 style="height: 71vh; overflow-y: scroll; overflow-x: hidden; padding-bottom: 40px;">
                <table id="crudTable" class="table table-striped table-hover nowrap rounded shadow-xs border-xs mt-2">
                    <thead>
                    <tr>
                        {{-- Table columns --}}
                        @foreach ($crud->columns() as $column)
                            <th data-orderable="{{ true }}" data-priority="{{ $column['priority'] }}"
                                {{--
                               data-visible-in-table => if developer forced field in table with 'visibleInTable => true'
                               data-visible => regular visibility of the field
                               data-can-be-visible-in-table => prevents the column to be loaded into the table (export-only)
                               data-visible-in-modal => if column apears on responsive modal
                               data-visible-in-export => if this field is exportable
                               data-force-export => force export even if field are hidden
                           --}} {{-- If it is an export field only, we are done. --}}
                                @if (isset($column['exportOnlyField']) && $column['exportOnlyField'] === true) data-visible="false"
                                data-visible-in-table="false"
                                data-can-be-visible-in-table="false"
                                data-visible-in-modal="false"
                                data-visible-in-export="true"
                                data-force-export="true"
                                @else
                                    data-visible-in-table="{{ var_export($column['visibleInTable'] ?? false) }}"
                                data-visible="{{ var_export($column['visibleInTable'] ?? true) }}"
                                data-can-be-visible-in-table="true"
                                data-visible-in-modal="{{ var_export($column['visibleInModal'] ?? true) }}"
                                @if (isset($column['visibleInExport']))
                                    @if ($column['visibleInExport'] === false)
                                        data-visible-in-export="false"
                                data-force-export="false"
                                @else
                                    data-visible-in-export="true"
                                data-force-export="true" @endif
                                @else data-visible-in-export="true" data-force-export="false" @endif
                                    @endif
                            >
                                {!! $column['label'] !!}
                            </th>
                        @endforeach

                        @if ($crud->buttons()->where('stack', 'line')->count())
                            <th data-orderable="false" data-priority="{{ $crud->getActionsColumnPriority() }}"
                                data-visible-in-export="false">{{ trans('backpack::crud.actions') }}</th>
                        @endif
                    </tr>
                    </thead>
                    <tbody style="">
                    </tbody>

                </table>
                <table id="header-fixed"></table>
            </div>

            @if ($crud->buttons()->where('stack', 'bottom')->count())
                <div id="bottom_buttons" class="d-print-none text-center text-sm-left">
                    @include('crud::inc.button_stack', ['stack' => 'bottom'])

                    <div id="datatable_button_stack" class="float-right text-right hidden-xs"></div>
                </div>
            @endif


        </div>


    </div>
    <div id="table-bottom-paginated" class="" style="overflow: visible; margin-top: -1rem">
        <table id="crudTable2" class="bg-white table table-striped table-hover nowrap rounded shadow-xs border-xs mt-2"
               cellspacing="0">
            <tfoot>
            <tr>
            </tr>
            </tfoot>
        </table>
    </div>
    <div style="visibility: hidden; height: 5px;">
        @include('crud::inc.button_stack', ['stack' => 'top'])
    </div>

    <script>
        @if ($crud->entity_name_plural == 'edificios')
        $('body').on('click', 'td', function (e) {

            let length = $(this).closest('tr').children('td').length;
            let current_index = $(this).index() + 1;
            let building_number = $(this).closest('tr').children('td:first').text().trim();

            if (current_index === length) {
                // If it is the last (actions) columns, do nothing
                return;
            }
            if (building_number) {
                window.open("/admin/building/" + building_number + "/info-with-building-number", "_blank");
                return true;
            }

            return alert(
                'La columna del número de edificio debe estar activada (Vaya a visibilidad de columnas y activela)'
            );
        });
        @endif
        $('body').on('click', 'td[tabindex="0"]', function (e) {
            setTimeout(2);
            document.getElementsByClassName('dtr-bs-modal')[0].addEventListener('click', e => {
                $('.phpdebugbar-openhandler-overlay').hide();
                $('.modal.fade.dtr-bs-modal.show').remove();
            });
        });


        document.getElementsByClassName('navbar-toggler')[0].style.fontSize = '15px';

        if (screen.width <= 1000) {
            console.log(screen.height + 1);

            $('#container-fluid-id').appendTo('#up-col12');
        }
    </script>
@endsection

@section('after_styles')
    <!-- DATA TABLES -->
    <link rel="stylesheet" type="text/css"
          href="{{ asset('packages/datatables.net-bs4/css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" type="text/css"
          href="{{ asset('packages/datatables.net-fixedheader-bs4/css/fixedHeader.bootstrap4.min.css') }}">
    <link rel="stylesheet" type="text/css"
          href="{{ asset('packages/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css') }}">

    <link rel="stylesheet"
          href="{{ asset('packages/backpack/crud/css/crud.css') . '?v=' . config('backpack.base.cachebusting_string') }}">
    <link rel="stylesheet"
          href="{{ asset('packages/backpack/crud/css/form.css') . '?v=' . config('backpack.base.cachebusting_string') }}">
    <link rel="stylesheet"
          href="{{ asset('packages/backpack/crud/css/list.css') . '?v=' . config('backpack.base.cachebusting_string') }}">

    <style>
        html .content.app-content {
            padding: 0.5rem 2rem 0 !important;
        }
    </style>

    <!-- CRUD LIST CONTENT - crud_list_styles stack -->

    @stack('crud_list_styles')
@endsection

@section('after_scripts')
    @include('crud::inc.datatables_logic')

    <script
            src="{{ asset('packages/backpack/crud/js/crud.js') . '?v=' . config('backpack.base.cachebusting_string') }}">
    </script>
    <script
            src="{{ asset('packages/backpack/crud/js/form.js') . '?v=' . config('backpack.base.cachebusting_string') }}">
    </script>
    <script
            src="{{ asset('packages/backpack/crud/js/list.js') . '?v=' . config('backpack.base.cachebusting_string') }}">
    </script>

    <script>
        $('#tables-list-cruds').css('height', '0px');


        if (localStorage.getItem('cheked') == 1) {
            $('.hello_dashboard').css('left', '120');
            var img = document.getElementById("foxsys-logo-in-menu");
            img.style.width = '65px';
            img.style.marginTop = '-15px'
        } else {
            $('.hello_dashboard').css('left', '280');
            var img = document.getElementById("foxsys-logo-in-menu");
            img.style.width = '85px';
            img.style.marginTop = '-25px'
        }

        window.onload = function () {
            $('.row.d-print-none').appendTo('#table-bottom-paginated');
            $('#columns-names').appendTo('#table-up-paginated-thead');
            $('.dataTables_length').css('margin-left', '-54%');
            $('.dataTables_length label').css('margin-top', '-1rem');
            $('.row.d-print-none .col-sm-12.col-md-4').css('padding-left', '12%');
            $('.content-area-wrapper.container-xxl.p-0').css('overflow', 'hidden');
            $('#datatable_search_stack #crudTable_filter label input').attr("placeholder", "Buscar  " +
                "<?php echo $crud->entity_name_plural; ?>");
        }
    </script>

    <style>
        /* Estilos para motores Webkit y blink (Chrome, Safari, Opera... )*/


        th {
            position: sticky !important;
            top: -1px !important;
            /* Don't forget this, required for the stickiness */
            box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4);
            z-index: 11;

        }

        .contenedor::-webkit-scrollbar {
            -webkit-appearance: none;
        }

        .contenedor::-webkit-scrollbar:vertical {
            width: 10px;
        }

        .contenedor::-webkit-scrollbar-button:increment,
        .contenedor::-webkit-scrollbar-button {
            display: none;
        }

        .contenedor::-webkit-scrollbar:horizontal {
            height: 10px;
        }

        .contenedor::-webkit-scrollbar-thumb {
            background-color: #797979;
            border-radius: 20px;
            border: 2px solid #f1f2f3;
        }

        .contenedor::-webkit-scrollbar-track {
            border-radius: 10px;
        }

        .create-model-button {
            border: 1px solid #223A8F !important;
            background-color: transparent;
            color: #223A8F;
        }

        .dark-layout .create-model-button {
            border: 1px solid white !important;
            background-color: transparent;
            color: white;
        }

        #datatable_search_stack #crudTable_filter label input {
            border-radius: 5px !important;
        }

        #datatable_info_stack {
            padding-inline: 15px;
        }

        .titleTableList {
            font-size: 28px;
        }

        .cases-table-header-list {
            padding: 0px;
        !important
        }

        @media (max-width: 1000px) {
            .separate-to-mobile {
                /*margin-left: 515px;*/
                font-size: 15px;
            }

            .out-in-mobile {
                visibility: hidden;
            }

            .content-area-wrapper {
                overflow-y: auto !important;
                overflow-x: hidden !important;
            }

            #div-table {
                height: 100% !important;
                overflow: auto;
            }

            #container-fluid-id {
                /*top: 100px!important;*/
                /*z-index: 0!important;*/
                /*padding: 12px;*/
                height: 45px;
                position: inherit !important;


            }

            #h2-header-class {
                width: 80%;
            }

            #crudTable_filter {
                margin-left: 325%;
            }

        }

        @media (max-width: 490px) {
            .separate-to-mobile {
                /*margin-left: 515px;*/
                font-size: 15px;
            }

            .margin-in-movil-height {
                margin-left: 50%;
            }

            .out-in-mobile {
                visibility: hidden;
            }

            .content-area-wrapper {
                overflow-y: auto !important;
                overflow-x: hidden !important;
            }

            #div-table {
                height: 100% !important;
                overflow: auto;
            }

            #container-fluid-id {
                /*top: 100px!important;*/
                /*z-index: 0!important;*/
                /*padding: 12px;*/
                height: 45px;
                position: inherit !important;


            }

            #h2-header-class {
                width: 80%;
            }

            #crudTable_filter {
                visibility: hidden;
            }

        }
    </style>

    <!-- CRUD LIST CONTENT - crud_list_scripts stack -->
    @stack('crud_list_scripts')
@endsection
