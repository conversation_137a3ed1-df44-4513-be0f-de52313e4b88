@php
    $canUseIncomingCall = false;
        $intercomsModelsIncomingCall = [
            config('constants.akuvox_r29'),
            config('constants.akuvox_e18'),
        ];

        if(str_contains(url()->current(), '/edit') && isset($entry)) {
            $building = \App\Models\Building::query()->find($entry?->id);
            $canUseIncomingCall = $building && $building->intercoms()->whereIn('model', $intercomsModelsIncomingCall)->count() > 0 && $building->app_foxsys == 1;
        }
@endphp
@if($canUseIncomingCall)
    <div class="form-check form-switch" style="left: 15px; padding-bottom: 20px;margin-top: -10px;">
        <input name="{{$field['name']}}" onclick="setValIncomingCall({{$field['name']}})"
               value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? "0" }}"
               class="bool_fields_back form-check-input" type="checkbox" id="{{$field['name']}}">
        <label class="form-check-label" style="" for="{{$field['name']}}">{{$field['label']}}</label>
    </div>
@endif


<style>
    @media (max-width: 1638px) {
        .min-view {
            display: none !important;
        }
    }
</style>

<script>
    $(document).ready(() => {
        let pressedSwitchIncomingCall = $('#incoming_call').val() ?? 0

        if(pressedSwitchIncomingCall == '1')
            $('#incoming_call').click()

        $('#incoming_call').on('change', () => {
            $('#incoming_call').val(pressedSwitchIncomingCall == 1 ? 0 : 1)
            pressedSwitchIncomingCall = $('#incoming_call').val()
        })
    })
</script>
