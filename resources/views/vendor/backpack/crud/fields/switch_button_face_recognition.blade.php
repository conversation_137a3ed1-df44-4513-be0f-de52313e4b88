@php
    $canUseFacilRecognition = false;
        if(str_contains(url()->current(), '/edit') && isset($entry)) {
            $building = \App\Models\Building::query()->find($entry?->id);
            $canUseFacilRecognition = $building && $building->intercoms()->where('model', config('constants.akuvox_e18'))->count() > 0;
        }
@endphp
@if($canUseFacilRecognition)
    <div class="form-check form-switch" style="left: 15px; padding-bottom: 20px;margin-top: -10px;">
        <input name="{{$field['name']}}" onclick="setValFaceRecognition({{$field['name']}})"
               value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? "0" }}"
               class="bool_fields_back form-check-input" type="checkbox" id="{{$field['name']}}">
        <label class="form-check-label" style="" for="{{$field['name']}}">{{$field['label']}}</label>
    </div>
@endif



<style>
    @media (max-width: 1638px) {
        .min-view {
            display: none !important;
        }
    }
</style>

<script>
    let pressedFaceRecognition = document.querySelector('[id=face_recognition]')?.value ?? 0;

    function setValFaceRecognition(id) {
        if (pressedFaceRecognition == 0) {
            pressedFaceRecognition = 1;
            $('#' + id.id).val(1);
        } else {
            pressedFaceRecognition = 0;
            $('#' + id.id).val(0);
        }
    }

    if ($('#face_recognition').val() == '1') {
        $('#face_recognition').click();
    }
</script>

