<?php
$isEdit = str_contains(url()->current(), '/edit');
$contactBuildings = [];
if ($isEdit && $entry) {

    $contactBuildings = $entry->buildings;

    $mainBuildingText = "Edificio principal";
    if (count($contactBuildings) > 0 && $contactBuildings[0]['building_number'] != '') {
        $mainBuildingText = $entry->mainBuilding->building_number . ' - ' . $entry->mainBuilding->building_name . ' - ' . $mainBuildingText;
    }
}
?>


<div class="modal fade" id="ModalDeletBuildingContactRelation" tabindex="-1" role="dialog"
     aria-labelledby="exampleModalLabel" aria-hidden="true" style="z-index: 99999999999999999;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="">
                <h5 class="modal-title" id="exampleModalLabel" style="color: white;"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span style="color: white;" aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body row">
                <i style="color: #FF9F43; " class=" text-center la la-warning la-5x"></i>
                <div class="text-center">Desasociar edificio del contacto</div>
                <div class="text-center">El edificio se desasociará del contacto si usted confirma.</div>
            </div>
            <div class="row">
                <div class="col-md-6 text-center">
                    <button dusk="button-delete-building"
                            style="border-radius: 24px; background-color: #FF9F43!important;"
                            onclick="deleteActualBuilding()" type="button" class="btn create_edit_back_color col-md-8"
                            data-dismiss="modal"><i class="la la-trash"></i>Desasociar
                    </button>
                </div>
                <div class="col-md-6 text-center">
                    <button onclick="" style="background-color: rgba(207,207,207,.62); border-radius: 24px;"
                            type="button" class="btn waves-effect waves-float waves-light col-md-8"
                            data-dismiss="modal"><i
                            class="la la-ban"></i>Cancelar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="{{ $field['large-row'] ?? 'col-md-12' }}" id="select_diferent_building" style="">
    <div class="card">
        @if ($field['icon'])
            <i style="padding: 12px;padding-left: 15px; position: absolute; z-index: 1; color:gray"
               class="col-md-2 titleTables la la-2x la-{{ $field['icon'] }}"></i>
        @endif
        <select onchange="addBuildingToContactChange()" id="all_contact_buildings"
                class="select2 select2-hidden-accessible col-md-10" style="color:gray">
            <option value="0" style="color:gray">
                @if($contactBuildings)
                    <label class="titleTables"
                           style="{{ $field['attributes']['style'] }}">{{$mainBuildingText}} </label>
                @else
                    <label class="titleTables"
                           style="{{ $field['attributes']['style'] }}"> Edificio {{ $field['name'] }}</label>
                @endif
            </option>
            @foreach ($contactBuildings as $key => $building)
                @if ($key != 0)
                    <option value="{{ $key }}">
                        <label class="titleTables"
                               style="{{ $field['attributes']['style'] }}">{{$building['building_number']}}
                            - {{$building['name'] }}</label>
                    </option>
                @endif
            @endforeach
        </select>
    </div>
</div>
<input type="text" id="building_secret_form" name="building_secret_form" style="display: none">


<input type="number" value="999999" id="las_building_selected" name="las_building_selected" style="display: none">

<i dusk="add-new-building" onclick="addBuildingToContact()" title="Asociar un nuevo edificio al contacto"
   class="la la-2x la-plus titleTables "
   style="padding: 12px;
    color:gray;
    position: absolute;
    z-index: 1;
    width: 3%;
left: 83%;"></i>

<span dusk="delete-building" id="delete-building-button" class="delete-building hidden" data-toggle="modal"
      data-target="#ModalDeletBuildingContactRelation">- Desasociar edificio
</span>

<br>

<style>
    .select2-container {
        padding: 7px;
    }

    #select_diferent_building .select2.select2-container.select2-container--default .selection .select2-selection.select2-selection--single .select2-selection__rendered {
        color: #223A8F;
        font-weight: bold;
        padding-left: 15% !important;
        color: gray;
    }

    .select2-container.select2-container--default.select2-container--open {
        padding: 0px;
    }

    .delete-building {
        color: var(--ColorRed);
        cursor: pointer;
        padding-inline: 5%;
        margin-top: -10px;
        width: fit-content;
    }
</style>

{{-- START THE SECONDARYS BUILDINGS FIELDS --}}
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
    let contacts = []
    let buildings_data_json = []
    let contact = @js($isEdit ? $entry->id : null);
    const editContact = window.location.href.includes('/edit')
    buildings_data_json = contact ? getOldDataOfContactBuilding(contact) : [];
    let tags = []
    let newBuilding = false;
    let buildings_data_number = 1;
    let buildings_data_pointer = 1;
    let selected_building = 1;

    $(document).ready(async () => {
        // Prevent send form when Enter key is pressed
        $('form').on('keydown', (e) => {
            if (e.keyCode === 13) {
                e.preventDefault()
                return false
            }
        })

        $('input[name=email]').on('blur', () => {
            saveClaveCiEmail()
        })

        $('input[name=security_word]').on('blur', () => {
            saveClaveCiEmail()
        })

        $('input[name=ci]').on('blur', () => {
            saveClaveCiEmail()
        })

        if (!editContact) {
            addFieldToContactJson()
        }

    })

    function updateLocalStorageInfo() {
        const currentFlat = window.localStorage.getItem('current_flat')
        if (currentFlat != null && currentFlat != undefined) {
            let contactIndex = contacts.findIndex(contact => contact.flat_id == currentFlat)
            if (contactIndex > -1) {
                contacts[contactIndex].schedule = getScheduleItems()
                window.localStorage.setItem('contact_building_data', JSON.stringify(contacts))
            }
        }
    }

    const addFieldToContactJson = () => {
        let contact = {
            "building_id": "",
            "flat_id": "",
            "type_of_contact_in_building": 'Residencial',
            "owner_or_tenant": 'S/D',
            "contact_type": $('select[name=contact_type]').val(),
            "referrer": 0,
            "empty_flat": 0,
            "description": "",
            "notes_2": "",
            "notes_3": "",
            "phone_mobile": "",
            "phone_home": "",
            "dont_call": 0,
            "foreign_phone": 0,
            "home_is_primary_phone": 0,
            "contact_email": $('input[name=email]').val(),
            "address": "",
            "door_number": "",
            "address_flat_number": "",
            "access_code": "",
            "access_code_temporal": "",
            "start_date": "",
            "end_date": "",
            "dateRangeClicked": 0,
            "schedule": JSON.stringify([]),
            "intercom_incoming_call": 0,
            "intercom_name": "",
            "name_kazoo": "",
            "user_sip_kazoo": "",
            "password_kazoo": "",
            "number_kazoo": "",
            "temporary_contact": false
        }
        buildings_data_json.push(contact)

        $('#building_secret_form').val(JSON.stringify(buildings_data_json));
        window.localStorage.setItem('contact_building_data', JSON.stringify(buildings_data_json))
        contacts = buildings_data_json
    }

    const getTagsByFlat = async (buildings) => {
        let tags = []
        const newBuildings = await Promise.all(buildings.map(async item => {
            const res = await fetch('/api/tags/flat/' + item.flat_id)
            item.tags = await res.json()
            tags.push(item.tags)
            return item
        }))


        // const jsonData = await
        convertBuildingContactDataIntoJson(buildings)
            .then((jsonData) => {
                // window.localStorage.removeItem('contact_building_data')
                window.localStorage.setItem('contact_building_data', JSON.stringify(jsonData))

                $('#building_secret_form').val(JSON.stringify(jsonData))
                buildings_data_json = jsonData
                contacts = jsonData

                if (editContact) {
                    buildings_data_number = buildings_data_json.length;
                    buildings_data_pointer = buildings_data_json.length;
                    buildings_data_edit_variable = buildings_data_json.length + 1;
                    buildings_data_change_variable = 0;

                    const flat_id_url = new URL(window.location.href).searchParams.get('flat_id')

                    getIntercomIncomingCall(flat_id_url)

                    if (flat_id_url && contacts[0].flat_id != flat_id_url) {
                        const contactIndex = contacts.findIndex(item => item.flat_id == flat_id_url)
                        window.localStorage.setItem('current_flat', flat_id_url)
                        setTimeout(() => {
                            $('#all_contact_buildings').val(contactIndex).trigger('change')
                            setTimeout(() => {
                                $('#flat_id_select').val(flat_id_url).change()

                                getAccessCodes(flat_id_url)
                                // updateLocalStorageInfo()
                            }, 1600)
                        }, 1600)
                    } else {
                        $('#all_contact_buildings').trigger('change')
                        window.localStorage.setItem('current_flat', $('#flat_id_select').val())
                        if (contacts.length > 0) {
                            getAccessCodes(JSON.parse(window.localStorage.getItem('contact_building_data'))[0].flat_id)
                            getAuthorizedTime(JSON.parse(window.localStorage.getItem('contact_building_data'))[0].flat_id)
                        }
                    }

                } else {
                    buildings_data_number = 1;
                    buildings_data_pointer = 1;
                }
            })


    }

    function changeFlat(flat_id) {
        if (flat_id)
            window.livewire.emit('changeFlat', flat_id)
    }

    async function getOldDataOfContactBuilding(id) {
        if (id) {
            var buildings = '';
            $.ajax({
                url: '{{ url('/admin/contact/building/relation/') }}' + '/' + id,
                type: 'GET',
                dataType: "json",
                // async: false,
                success: async function (data) {
                    buildings = await data;
                    await getTagsByFlat(data)
                },
            });
            return buildings;
        }
        return [];
    }

    function getAuthorizedTime(flatId) {
        let buildingContact = contacts.find((contact) => contact.flat_id == flatId)
        if (buildingContact.dateRangeClicked == 1) {
            $("input[name=start_date]").val(buildingContact.start_date);
            $("input[name=end_date]").val(buildingContact.end_date);
            $('#start_end_date').ready(() => {
                bpFieldInitDateRangeElement($('#start_end_date'))
                document.getElementById('date_range_content').style.display = 'block';
                document.getElementById('icon_close_date').style.display = 'flex';
                document.getElementById('dateRangeClicked').value = 'true';
                document.getElementById('date_rage_label').style.display = 'none';
                $('#buttons-edit-delete-profile-image').addClass('d-none')
            })
        } else if ($('#contact_type').val()?.toLowerCase() == 'autorizado' && buildingContact.dateRangeClicked == 0) {
            $("input[name=start_date]").val(authorizedTimeFormatDate(new Date()));
            $("input[name=end_date]").val(authorizedTimeFormatDate(new Date(new Date()
                .setDate(new Date().getDate() + 1)), false));
            $('#start_end_date').ready(() => {
                bpFieldInitDateRangeElement($('#start_end_date'))
                document.getElementById('date_rage_label').style.display = 'block';
                document.getElementById('date_range_content').style.display = 'none';
                document.getElementById('icon_close_date').style.display = 'none';
                document.getElementById('dateRangeClicked').value = 'false';
            })
        } else {
            $("input[name=start_date]").val(authorizedTimeFormatDate(new Date()));
            $("input[name=end_date]").val(authorizedTimeFormatDate(new Date(new Date()
                .setDate(new Date().getDate() + 1)), false));
            $('#start_end_date').ready(() => {
                bpFieldInitDateRangeElement($('#start_end_date'))
                document.getElementById('date_rage_label').style.display = 'none';
                document.getElementById('date_range_content').style.display = 'none';
                document.getElementById('icon_close_date').style.display = 'none';
                document.getElementById('dateRangeClicked').value = 'false';
            })
        }
    }

    async function getSchedule(contact, flatId) {
        if (flatId && contact) {
            $.ajax({
                url: '{{ url('/admin/contact/') }}' + '/' + contact + '/schedule/flat/' + flatId,
                type: 'GET',
                dataType: "json",
                success: function (data) {
                    const {schedule} = data
                    $('input[name="schedule"]').val(JSON.stringify(schedule)).change()
                    getScheduleEdit(JSON.stringify(schedule))
                },
            });
        } else {
            $('input[name="schedule"]').val(JSON.stringify([])).change()
            getScheduleEdit(JSON.stringify([]))
        }
    }

    async function convertBuildingContactDataIntoJson(data) {
        let buildingContact = []
        data.forEach((item) => {
            buildingContact.push({
                "name_kazoo": item.name_kazoo,
                "user_sip_kazoo": item.user_sip_kazoo,
                "password_kazoo": item.password_kazoo,
                "number_kazoo": item.number_kazoo,
                "intercom_incoming_call": item.intercom_incoming_call == true || item.intercom_incoming_call == 1,
                "building_id": item.building_id,
                "flat_id": item.flat_id,
                "type_of_contact_in_building": item.type_of_contact_in_building ?? "Residencial",
                "owner_or_tenant": item.owner_or_tenant,
                "contact_type": item.contact_type,
                "referrer": item.referrer,
                "empty_flat": item.empty_flat ? 1 : 0,
                "description": item.description,
                "notes_2": item.notes_2,
                "notes_3": item.notes_3,
                "phone_mobile": item.phone_mobile,
                "phone_home": item.phone_home,
                "dont_call": item.dont_call ? 1 : 0,
                "foreign_phone": item.foreign_phone,
                "home_is_primary_phone": item.home_is_primary_phone ? 1 : 0,
                "contact_email": item.email,
                "address": item.address,
                "door_number": item.door_number,
                "address_flat_number": item.address_flat_number,
                "others_mobil_phones": item.others_mobil_phone,
                "access_code": item.access_code ?? null,
                "access_code_temporal_show": item.access_code_temporal_show ?? [],
                "schedule": item.schedule ?? [],
                "start_date": item.start_date ?? null,
                "end_date": item.end_date ?? null,
                "dateRangeClicked": item.start_date && item.end_date ? 1 : 0,
                'tags': item.tags,
                "intercom_name": item.intercom_name,
                "temporary_contact": item.temporary_contact
            })
        })
        return buildingContact
    }

    function addBuildingToContact() {
        if ($('select[name=building_id]').val() != '' && $('select[name=flat_id]').val() != '') {
            getInfoOfFieldsChange()
            $('#all_contact_buildings').append($("<option>", {
                value: buildings_data_number,
                text: 'Edificio secundario'
            }));
            buildings_data_number = buildings_data_number + 1;
            buildings_data_pointer = buildings_data_number;
            newBuilding = true
            setAdedBuildingForm(true, buildings_data_number - 1);
            cleanAllFields();
            addFieldToContactJson()
            if (selected_building > 0) {
                $('#delete-building-button').removeClass('hidden');
                $('#contact-profile-image').addClass('d-none')
            } else {
                $('#delete-building-button').addClass('hidden');
                $('#contact-profile-image').removeClass('d-none')
            }
            $('#phone_mobile').trigger('blur')
            $('#olds_keys_rows').addClass('hidden')
            $('input[data-repeatable-holder="schedule"]').empty()
        }
    }

    function changeBuilding(selected_building) {

        if ($('select[name=building_id]').val() != '' && $('select[name=building_id]').val() != null && contacts[
        selected_building - 1].flat_id != '') {
            setAdedBuildingForm(false, selected_building - 1);
            getInfoOfFieldsChange();
            window.localStorage.setItem('current_flat', $('#flats_id_selected').val())
            newBuilding = false
        }
    }

    function getInfoOfFieldsChange(changeBuilding = true) {
        if (editContact && buildings_data_change_variable == 0 && buildings_data_edit_variable >
            buildings_data_number) {
            buildings_data_pointer = 1;
            buildings_data_change_variable = 99999;
        }
        buildings_data_json[buildings_data_pointer - 1] = {
            "building_id": $('select[name=building_id]').val(),
            "flat_id": $('select[name=flat_id]').val(),
            "type_of_contact_in_building": $('input[value="Residencial"]').is(":checked") ? 'Residencial' : 'Oficina',
            "owner_or_tenant": $('input[name=owner_or_tenant]').val(),
            "contact_type": $('select[name=contact_type]').val(),
            "referrer": $('input[name=referrer]').val(),
            "empty_flat": $('input[own_id=empty_flatbox]').is(':checked'),
            "description": $('textarea[name=description]').val(),
            "notes_2": $('textarea[name=notes_2]').val(),
            "notes_3": $('textarea[name=notes_3]').val(),
            "phone_home": $('input[name=phone_home]').val(),
            "phone_mobile": $('input[name=phone_mobile]').val(),
            "dont_call": $('input[own_id=dont_callbox]').is(':checked'),
            "foreign_phone": $('input[name=foreign_phone]').val(),
            "home_is_primary_phone": $('input[name=home_is_primary_phone]').val(),
            "contact_email": $('input[name=email]').val(),
            "address": $('input[name=address]').val(),
            "door_number": $('input[name=door_number]').val(),
            "address_flat_number": $('input[name=address_flat_number]').val(),
            "access_code": $("input[name=code]").val(),
            "access_code_temporal": $("input[name=access_code_temporal]").val(),
            "schedule": getScheduleItems(),
            "start_date": $("input[name=start_date]").val(),
            "end_date": $("input[name=end_date]").val(),
            "dateRangeClicked": $("input[name=dateRangeClicked]").val() == 'true' ? 1 : 0,
            "intercom_name": $('input[name=intercom_name]').val(),
            "intercom_incoming_call": $('input[name=intercom_incoming_call]').val(),
            "name_kazoo": $('#name_kazoo').text() === '-' ? "" : $('#name_kazoo').text(),
            "user_sip_kazoo": $('#user_sip_kazoo').text() === '-' ? "" : $('#user_sip_kazoo').text(),
            "password_kazoo": $('#password_kazoo').text() === '-' ? "" : $('#password_kazoo').text(),
            "number_kazoo": $('#number_kazoo').text() === '-' ? "" : $('#number_kazoo').text(),
            "temporary_contact": $("input[name=start_date]").val() || $("input[name=end_date]").val() ? true : false
        }

        if (changeBuilding)
            saveSchedule(getScheduleItems(), $('#flat_id_select').val())

        $('#building_secret_form').val(JSON.stringify(buildings_data_json));
        window.localStorage.setItem('contact_building_data', JSON.stringify(buildings_data_json))
        contacts = buildings_data_json
    }

    function getScheduleItems() {
        let repeatableData = [];

        // Recorre los elementos del repeatable
        let repeatableElements = document.querySelectorAll('[data-repeatable-identifier="schedule"]');
        repeatableElements.forEach(function (element) {
            let item = {};

            // Recorre los campos dentro del elemento repeatable
            let fields = element.querySelectorAll('[data-repeatable-input-name]');
            fields.forEach(function (field) {
                let fieldName = field.getAttribute('data-repeatable-input-name');
                let fieldValue = field.value;
                item[fieldName] = fieldValue;
            });
            repeatableData.push(item);
        });

        return JSON.stringify(repeatableData);
    }

    function saveSchedule(schedule, flatId) {
        const isDeleting = window.localStorage.getItem('isDeleting') ?? false
        if (schedule && schedule != '[{}]' && schedule != '[]' && $('#contact_type').val().toLowerCase() == 'autorizado' && flatId && !isDeleting) {
            $.ajax({
                url: '{{ url('/admin/contact/') }}' + '/' + contact + '/schedule/flat/' + flatId,
                // dataType: 'json',
                method: 'POST',
                data: {
                    schedule,
                    userId: contact,
                    flatId
                },
                success: function (data) {
                    console.log(data)
                }
            })
        }
    }

    function setAdedBuildingForm(bool_var, selected_building) {
        if (selected_building == 'NaN') {
            selected_building = buildings_data_number - 1;
        }
        $("#all_contact_buildings option[value=" + selected_building + "]").attr("selected", true);
        if (bool_var) {
            new Noty({
                type: "success",
                text: 'Edificio y apartamento agregados',
            }).show();
        }
    }

    function addBuildingToContactChange() {
        selected_building = $('#all_contact_buildings').val() ?? 0;
        if (selected_building > 0) {
            $('#delete-building-button').removeClass('hidden');
            $('#contact-profile-image').addClass('d-none')
        } else {
            $('#delete-building-button').addClass('hidden');
            $('#contact-profile-image').removeClass('d-none')
        }

        if (editContact) {
            $('#olds_keys_rows').html('')
            const url = new URL(window.location.href)
            const params = new URLSearchParams(url.search)
            params.set('flat_id', contacts.length > 0 ? contacts[selected_building].flat_id : '')
            url.search = params.toString()
            window.history.replaceState(null, '', url.toString())
        }
        $('#las_building_selected').val(selected_building);
        changeBuilding(parseInt(selected_building) + 1);
        updateInfoOfTheBuildingSelected(parseInt(selected_building) + 1);
        $('#phone_mobile').trigger('blur')
    }

    function updateInfoOfTheBuildingSelected(selected_building) {
        if (buildings_data_json[selected_building - 1]) {
            const buildingContact = buildings_data_json[selected_building - 1]
            setTimeout(() => {
                if (buildingContact.building_id)
                    $("#building_id_select").val(buildingContact.building_id).change();

                setTimeout(() => {
                    if (buildingContact.flat_id)
                        $("#flat_id_select").val(buildingContact.flat_id).change();
                }, 2000);
            }, 800)

            $('#tyofco').val(buildingContact.type_of_contact_in_building)
            if (buildingContact.type_of_contact_in_building == 'Residencial') {
                $('input[value="Oficina"]').prop('checked', false)
                $('input[value="Residencial"]').trigger('click')
                $('input[value="Residencial"]').trigger('change')
            } else {
                $('input[value="Residencial"]').prop('checked', false)
                $('input[value="Oficina"]').trigger('click')
                $('input[value="Oficina"]').trigger('change')
            }

            $('#contact_type').val(buildingContact.contact_type).trigger('change')

            document.getElementsByName('owner_or_tenant')[0].value = buildingContact
                .owner_or_tenant;
            if (buildingContact.owner_or_tenant == 'S/D') {
                $('input[value="S/D"]').prop('checked', 'checked');
                $('input[value=Inquilino]').prop('checked', false);
                $('input[value=Propietario]').prop('checked', false);
                $('input[value=Apoderado]').prop('checked', false);
            } else if (buildingContact.owner_or_tenant == 'Propietario') {
                $('input[value="S/D"]').prop('checked', false);
                $('input[value=Inquilino]').prop('checked', false);
                $('input[value=Propietario]').prop('checked', 'checked');
                $('input[value=Apoderado]').prop('checked', false);
            } else if (buildingContact.owner_or_tenant == 'Inquilino') {
                $('input[value="S/D"]').prop('checked', false);
                $('input[value=Inquilino]').prop('checked', 'checked');
                $('input[value=Propietario]').prop('checked', false);
                $('input[value=Apoderado]').prop('checked', false);
            } else {
                $('input[value="S/D"]').prop('checked', false);
                $('input[value=Inquilino]').prop('checked', false);
                $('input[value=Propietario]').prop('checked', false);
                $('input[value=Apoderado]').prop('checked', 'checked');
            }

            $('input[name=referrer]').val(buildingContact.referrer == true || buildingContact.referrer == 1 ? 1 : 0);
            $('input[own_id=referrerbox]').prop('checked', buildingContact.referrer == 1 || buildingContact.referrer == true ?
                'checked' : '')

            $('input[name=empty_flat]').val(buildingContact.empty_flat == true || buildingContact.empty_flat == 1 ? 1 : 0);
            $('input[own_id=empty_flatbox]').prop('checked', buildingContact.empty_flat == 1 || buildingContact.empty_flat == true ? 'checked' : '')

            $('input[name=foreign_phone]').val(buildingContact.foreign_phone);
            $('input[own_id=foreign_phonebox]').prop('checked', buildingContact
                .foreign_phone != 0 ? 'checked' : '')

            $('input[name=home_is_primary_phone]').val(buildingContact.home_is_primary_phone == true || buildingContact.home_is_primary_phone == 1 ? 1 : 0);
            $('input[own_id=home_is_primary_phonebox]').prop('checked', buildingContact.home_is_primary_phone == true || buildingContact.home_is_primary_phone == 1 ? 'checked' : '')

            $('input[name=dont_call]').val(buildingContact.dont_call == true || buildingContact.dont_call == 1 ? 1 : 0);
            $('input[own_id=dont_callbox]').prop('checked', buildingContact.dont_call == true || buildingContact.dont_call == 1 ?
                'checked' : '')

            $("input[name=address]").val(buildingContact.address);
            $("input[name=door_number]").val(buildingContact.door_number);
            $("input[name=address_flat_number]").val(buildingContact.address_flat_number);
            $("input[name=phone_home]").val(buildingContact.phone_home);
            $("input[name=phone_mobile]").val(buildingContact.phone_mobile);

            $("textarea[name=description]").val(buildingContact.description);
            $("textarea[name=notes_2]").val(buildingContact.notes_2);
            $("textarea[name=notes_3]").val(buildingContact.notes_3);

            $('input[name=intercom_name]').val(buildingContact.intercom_name)
            $('input[name=intercom_incoming_call]').prop('checked', buildingContact.intercom_incoming_call == true || buildingContact.intercom_incoming_call == 1 ? 'checked' : '');
            $('input[own_id=intercom_incoming_callbox]').prop('checked', buildingContact.intercom_incoming_call == true || buildingContact.intercom_incoming_call == 1 ? 'checked' : '');

            $('#name_kazoo').text(buildingContact.name_kazoo == '' ? '-' : buildingContact.name_kazoo)
            $('#user_sip_kazoo').text(buildingContact.user_sip_kazoo == '' ? '-' : buildingContact.user_sip_kazoo)
            $('#password_kazoo').text(buildingContact.password_kazoo == '' ? '-' : buildingContact.password_kazoo)
            $('#number_kazoo').text(buildingContact.number_kazoo == '' ? '-' : buildingContact.number_kazoo)

            if (buildingContact.notes_2) {
                $('textarea[name=notes_2]').removeClass('hidden')
            } else {
                $('textarea[name=notes_2]').addClass('hidden')
            }
            if (buildingContact.notes_3) {
                $('textarea[name=notes_3]').removeClass('hidden')
            } else {
                $('textarea[name=notes_3]').addClass('hidden')
            }
            buildings_data_pointer = selected_building;
        }

    }

    function showElementOnChange() {
        const $usersTypeCanHaveImage = [
            'Residente',
            'No Residente',
            'Contacto Principal',
            'Contacto Principal - Comisión',
            'Comisión',
            'Responsable',
            'Empleado',
            'Foxsys',
            'Autorizado'
        ];

        if (!window.location.href.includes('authorized')) {
            $('#icon_close_date').hide()
            if ($('#contact_type').val() == 'Autorizado') {
                $('.show-on-change-base').show();
                $('#authorized_time').show()
                $('#date_rage_label').show()
                $('#contact_lastname').text('')
            } else {
                $('.show-on-change-base').hide();
                $('#contact-schedule-card').hide()
                $('#authorized_time').hide()
                $('#date_rage_label').hide()
                $('#icon_close_date').trigger('click')
                $('#contact_lastname').text('*')
                let $currentContactBuilding = contacts[$('#all_contact_buildings').val() ?? 0]
                if($currentContactBuilding) {
                    $currentContactBuilding.dateRangeClicked = 0
                    contacts[$('#all_contact_buildings').val() ?? 0] = $currentContactBuilding
                }
            }
        }

        if (!newBuilding && contacts && contacts.length > 0 && contacts[$('#all_contact_buildings').val() ?? 0].flat_id && contacts[$('#all_contact_buildings').val() ?? 0].flat_id != '') {
            getAuthorizedTime(contacts[$('#all_contact_buildings').val() ?? 0].flat_id)
        }

        let userCanHaveImage = $usersTypeCanHaveImage.find((item) => item == $('#contact_type')?.val())
        if (userCanHaveImage != null && userCanHaveImage != undefined) {
            $('#buttons-edit-delete-profile-image').removeClass('d-none')
        } else {
            $('#buttons-edit-delete-profile-image').addClass('d-none')
        }
    }

    function loadContactTypes() {
        const contact_type = document.getElementById("contact_type");
        if ($('input[type=radio][value="Residencial"]').is(':checked')) {
            contact_type.innerHTML = ""
            let op_oficinas = []
            op_oficinas = @json(\App\Models\User\Contact::$residentialContactTypes);

            op_oficinas?.forEach(item => {
                const op = document.createElement('option')
                op.value = item
                op.innerText = item
                if (contacts && contacts.length > 0 && contacts[$('#all_contact_buildings').val() ?? 0].contact_type &&
                    contacts[$('#all_contact_buildings').val() ?? 0].contact_type.toUpperCase() == item.toUpperCase()) {
                    op.selected = true
                }
                contact_type.appendChild(op)
            })
        } else {
            contact_type.innerHTML = ""
            let op_oficinas = []
            op_oficinas = @json(\App\Models\User\Contact::$officeContactTypes);
            op_oficinas?.forEach(item => {
                const op = document.createElement('option')
                op.value = item
                op.innerText = item
                if (contacts && contacts.length > 0 && contacts[$('#all_contact_buildings').val() ?? 0]
                    .contact_type.toUpperCase() == item.toUpperCase()) {
                    op.selected = true
                }
                contact_type.appendChild(op)
            })
        }

        setTimeout(() => {
            contact_type.style.transition = 'all 1.7s ease';
        }, 100)

        showElementOnChange()
    }

    function loadContactTypesFromContactTypeInBuilding() {
        if (contacts && contacts.length > 0) {
            if (contacts[$('#all_contact_buildings').val() ?? 0].type_of_contact_in_building == 'Residencial') {
                $('input[value="Oficina"]').prop('checked', false)
                $('input[value="Residencial"]').trigger('click')
                $('input[value="Residencial"]').trigger('change')
            } else {
                $('input[value="Residencial"]').prop('checked', false)
                $('input[value="Oficina"]').trigger('click')
                $('input[value="Oficina"]').trigger('change')
            }
        }
    }

    function getScheduleEdit(schedule) {
        $('div[data-repeatable-holder="schedule"]').empty()
        if (!schedule || JSON.parse(schedule).length === 0) return null
        const scheduleContact = JSON.parse(schedule)
        const div_repeatable_holder = document.querySelector('div[data-repeatable-holder="schedule"]')

        if (scheduleContact.length > 0) {
            $('#buttons-edit-delete-profile-image').addClass('d-none')
        }

        if (scheduleContact.length > 0 && $('#contact_type').val() == 'Autorizado')
            $('#contact-schedule-card').show()
        else
            $('#contact-schedule-card').hide()

        let allDays = 0

        scheduleContact.forEach((item, index) => {
            // div repeatable identifier
            const div_repeatable_identifier = document.createElement('div')
            div_repeatable_identifier.classList.add('col-md-12', 'well', 'repeatable-element', 'row', 'm-1',
                'p-2')
            div_repeatable_identifier.setAttribute('data-repeatable-identifier', 'schedule')

            // Agregar todo el html del repeatable
            // boton de cerrar
            const div_boton_cerrar = document.createElement('div')
            div_boton_cerrar.classList.add('controls')
            div_boton_cerrar.style.width = "10px"
            div_boton_cerrar.style.left = "88%"
            div_boton_cerrar.style.top = "1%"

            const button_close = document.createElement('button')
            button_close.style.zIndex = "0"
            button_close.classList.add('close', 'delete-element')
            button_close.onclick = function () {
                let container = $('[data-repeatable-identifier="schedule"]')
                container.find('.delete-element').click(function () {
                    container.find('input, select, textarea').each(function (i, el) {
                        // we trigger this event so fields can intercept when they are beeing deleted from the page
                        // implemented because of ckeditor instances that stayed around when deleted from page
                        // introducing unwanted js errors and high memory usage.
                        $(el).trigger('backpack_field.deleted');
                    });
                })
                $(this).closest('.repeatable-element').remove()
            }
            const span_close = document.createElement('span')
            span_close.ariaHidden = true
            span_close.innerText = "×"

            button_close.appendChild(span_close)

            div_boton_cerrar.appendChild(button_close)

            div_repeatable_identifier.appendChild(div_boton_cerrar)

            // Texto y switch todos los dias
            const div_row = document.createElement('div')
            div_row.style.zIndex = "9999"
            div_row.classList.add('row')

            const div_switch = document.createElement('div')
            div_switch.classList.add('form-check', 'form-switch', 'd-flex', 'justify-content-between')
            div_switch.style.padding = "10px"

            const label_switch = document.createElement('label')
            label_switch.classList.add('form-check-label')
            label_switch.innerText = "Horarios de autorización"

            const input_switch = document.createElement('input')
            input_switch.setAttribute('id', 'allDays');
            input_switch.setAttribute('class', 'form-check-input');
            input_switch.setAttribute('type', 'checkbox');
            input_switch.setAttribute('data-row-number', index);
            input_switch.setAttribute('value', 0)
            input_switch.onclick = function () {
                checkAllDays(this)
            }

            const label_all_days = document.createElement('label')
            label_all_days.classList.add('form-check-label')
            label_all_days.style.paddingTop = "3px"
            label_all_days.htmlFor = "flexSwitchCheckDefault"
            label_all_days.innerText = "Todos los días"

            const divContainerTextCheckbox = document.createElement('div')
            divContainerTextCheckbox.classList.add('d-flex', 'justify-items-center')
            divContainerTextCheckbox.appendChild(input_switch)
            divContainerTextCheckbox.appendChild(label_all_days)

            const hr = document.createElement('hr')

            div_switch.appendChild(label_switch)
            div_switch.appendChild(divContainerTextCheckbox)
            // div_switch.appendChild(label_all_days)

            div_row.appendChild(div_switch)
            div_row.appendChild(hr)

            div_repeatable_identifier.appendChild(div_row)

            let weekDays = ['L', 'M', 'X', 'J', 'V', 'S', 'D']

            Object.keys(item).forEach((key, indexKey) => {
                if (key.startsWith("inline")) {
                    const div_form_group = document.createElement('div')
                    div_form_group.classList.add('form-group', 'col-md-1')
                    div_form_group.style.paddingRight = "25px"
                    div_form_group.setAttribute('element', 'div')

                    if (indexKey > 0) {
                        div_form_group.setAttribute('id', 'inlinecheckday' + parseInt(indexKey + 1))
                    }

                    const div_checkbox = document.createElement('div')
                    div_checkbox.classList.add('checkbox', 'd-flex', 'flex-column',
                        'align-items-center')

                    const input_hidden = document.createElement('input')
                    input_hidden.type = 'hidden'
                    input_hidden.setAttribute('value', item[key])
                    input_hidden.setAttribute('data-repeatable-input-name', key)
                    input_hidden.setAttribute('data-row-number', index)

                    const randomNumber = Math.floor(100000 + Math.random() * 900000);
                    const formattedNumber = String(randomNumber).padStart(6, '0');

                    const input_checkbox = document.createElement('input')
                    input_checkbox.type = "checkbox"
                    input_checkbox.setAttribute('data-init-function', 'bpFieldInitCheckbox')
                    input_checkbox.checked = item[key] != 0
                    if (item[key] != 0) allDays++;
                    input_checkbox.setAttribute('data-row-number', index)
                    input_checkbox.setAttribute('column-order', index)
                    input_checkbox.setAttribute('data-initialized', 'true')
                    input_checkbox.setAttribute('id', 'checkbox_' + formattedNumber)
                    input_checkbox.onclick = function () {
                        toggleCkecked(input_hidden)
                    }

                    const label_checkbox = document.createElement('label')
                    label_checkbox.classList.add('form-check-label', 'font-weight-normal')
                    label_checkbox.htmlFor = "checkbox_" + formattedNumber
                    label_checkbox.innerText = weekDays[indexKey]


                    div_checkbox.appendChild(input_hidden)
                    div_checkbox.appendChild(input_checkbox)
                    div_checkbox.appendChild(label_checkbox)
                    div_form_group.appendChild(div_checkbox)

                    div_repeatable_identifier.appendChild(div_form_group)
                }
                if (key.includes('time')) {
                    const div_form_group = document.createElement('div')
                    div_form_group.classList.add('form-group', 'col-md-6')
                    div_form_group.setAttribute('element', 'div')

                    // label
                    const label_start = document.createElement('label')
                    const start_end_label = key.includes('f') ? 'final' : 'inicial'
                    label_start.innerText = 'Horario ' + start_end_label

                    const input_time = document.createElement('input')
                    input_time.classList.add('form-control')
                    input_time.type = 'time'
                    input_time.setAttribute('column-order', index)
                    input_time.setAttribute('data-repeatable-input-name', key)
                    input_time.setAttribute('data-row-number', index)
                    input_time.setAttribute('value', item[key])

                    div_form_group.appendChild(label_start)
                    div_form_group.appendChild(input_time)

                    div_repeatable_identifier.appendChild(div_form_group)
                }
            })

            // Agregar al nodo el repeatable
            div_repeatable_holder.appendChild(div_repeatable_identifier)

            if (allDays == 7) {
                input_switch.setAttribute('value', 1)
                $('#allDays').prop('checked', allDays === 7)
            }
        })
    }

    function getScheduleNew() {
        $('div[data-repeatable-holder="schedule"]').empty()
        const scheduleContact = [{
            "inlineCheckbox": "",
            "inlineCheckbox2": "",
            "inlineCheckbox3": "",
            "inlineCheckbox4": "",
            "inlineCheckbox5": "",
            "inlineCheckbox6": "",
            "inlineCheckbox7": "",
            "timei1ser": "",
            "timef1ser": "",
        }]
        const div_repeatable_holder = document.querySelector('div[data-repeatable-holder="schedule"]')

        scheduleContact.forEach((item, index) => {
            // div repeatable identifier
            const div_repeatable_identifier = document.createElement('div')
            div_repeatable_identifier.classList.add('col-md-12', 'well', 'repeatable-element', 'row', 'm-1',
                'p-2')
            div_repeatable_identifier.setAttribute('data-repeatable-identifier', 'schedule')

            // Agregar todo el html del repeatable
            // boton de cerrar
            const div_boton_cerrar = document.createElement('div')
            div_boton_cerrar.classList.add('controls')
            div_boton_cerrar.style.width = "10px"
            div_boton_cerrar.style.left = "91%"
            div_boton_cerrar.style.top = "1%"

            const button_close = document.createElement('button')
            button_close.style.zIndex = "0"
            button_close.classList.add('close', 'delete-element')
            button_close.onclick = function () {
                let container = $('[data-repeatable-identifier="schedule"]')
                container.find('.delete-element').click(function () {
                    container.find('input, select, textarea').each(function (i, el) {
                        // we trigger this event so fields can intercept when they are beeing deleted from the page
                        // implemented because of ckeditor instances that stayed around when deleted from page
                        // introducing unwanted js errors and high memory usage.
                        $(el).trigger('backpack_field.deleted');
                    });
                })
                $(this).closest('.repeatable-element').remove()
            }
            const span_close = document.createElement('span')
            span_close.ariaHidden = true
            span_close.innerText = "×"

            button_close.appendChild(span_close)

            div_boton_cerrar.appendChild(button_close)

            div_repeatable_identifier.appendChild(div_boton_cerrar)

            // Texto y switch todos los dias
            const div_row = document.createElement('div')
            div_row.style.zIndex = "9999"
            div_row.classList.add('row')

            const div_switch = document.createElement('div')
            div_switch.classList.add('contact-schedule-card', 'form-switch')
            // div_switch.style.paddingInline = "0px"
            div_switch.style.left = "45px"
            div_switch.style.padding = "10px"
            div_switch.style.display = "flex"

            const label_switch = document.createElement('label')
            label_switch.classList.add('form-check-label')
            label_switch.style.paddingBottom = "20px"
            label_switch.style.marginRight = "43%"
            label_switch.style.marginLeft = "-20px"
            label_switch.innerText = "Horarios de autorización"

            const input_switch = document.createElement('input')
            input_switch.setAttribute('id', 'allDays');
            input_switch.setAttribute('class', 'form-check-input');
            input_switch.setAttribute('type', 'checkbox');
            input_switch.setAttribute('data-row-number', index);
            input_switch.setAttribute('value', 0)
            input_switch.onclick = function () {
                checkAllDays(this)
            }

            const label_all_days = document.createElement('label')
            label_all_days.classList.add('form-check-label')
            label_all_days.style.paddingTop = "3px"
            label_all_days.htmlFor = "flexSwitchCheckDefault"
            label_all_days.innerText = "Todos los días"

            const divContainerTextCheckbox = document.createElement('div')
            divContainerTextCheckbox.appendChild(input_switch)
            divContainerTextCheckbox.appendChild(label_all_days)

            const hr = document.createElement('hr')

            div_switch.appendChild(label_switch)
            div_switch.appendChild(input_switch)
            div_switch.appendChild(label_all_days)

            div_row.appendChild(div_switch)
            div_row.appendChild(hr)

            div_repeatable_identifier.appendChild(div_row)

            let weekDays = ['L', 'M', 'X', 'J', 'V', 'S', 'D']

            Object.keys(item).forEach((key, indexKey) => {
                if (key.startsWith("inline")) {
                    const div_form_group = document.createElement('div')
                    div_form_group.classList.add('form-group', 'col-md-1')
                    div_form_group.style.paddingRight = "25px"
                    div_form_group.setAttribute('element', 'div')

                    if (indexKey > 0) {
                        div_form_group.setAttribute('id', 'inlinecheckday' + parseInt(indexKey + 1))
                    }

                    const div_checkbox = document.createElement('div')
                    div_checkbox.classList.add('checkbox', 'd-flex', 'flex-column',
                        'align-items-center')

                    const input_hidden = document.createElement('input')
                    input_hidden.type = 'hidden'
                    input_hidden.setAttribute('value', item[key])
                    input_hidden.setAttribute('data-repeatable-input-name', key)
                    input_hidden.setAttribute('data-row-number', index)

                    const randomNumber = Math.floor(100000 + Math.random() * 900000);
                    const formattedNumber = String(randomNumber).padStart(6, '0');

                    const input_checkbox = document.createElement('input')
                    input_checkbox.type = "checkbox"
                    input_checkbox.setAttribute('data-init-function', 'bpFieldInitCheckbox')
                    input_checkbox.checked = item[key] != 0
                    input_checkbox.setAttribute('data-row-number', index)
                    input_checkbox.setAttribute('column-order', index)
                    input_checkbox.setAttribute('data-initialized', 'true')
                    input_checkbox.setAttribute('id', 'checkbox_' + formattedNumber)
                    input_checkbox.onclick = function () {
                        toggleCkecked(input_hidden)
                    }

                    const label_checkbox = document.createElement('label')
                    label_checkbox.classList.add('form-check-label', 'font-weight-normal')
                    label_checkbox.htmlFor = "checkbox_" + formattedNumber
                    label_checkbox.innerText = weekDays[indexKey]


                    div_checkbox.appendChild(input_hidden)
                    div_checkbox.appendChild(input_checkbox)
                    div_checkbox.appendChild(label_checkbox)
                    div_form_group.appendChild(div_checkbox)

                    div_repeatable_identifier.appendChild(div_form_group)
                }
                if (key.includes('time')) {
                    const div_form_group = document.createElement('div')
                    div_form_group.classList.add('form-group', 'col-md-6')
                    div_form_group.setAttribute('element', 'div')

                    // label
                    const label_start = document.createElement('label')
                    const start_end_label = key.includes('f') ? 'final' : 'inicial'
                    label_start.innerText = 'Horario ' + start_end_label

                    const input_time = document.createElement('input')
                    input_time.classList.add('form-control')
                    input_time.type = 'time'
                    input_time.setAttribute('column-order', index)
                    input_time.setAttribute('data-repeatable-input-name', key)
                    input_time.setAttribute('data-row-number', index)
                    input_time.setAttribute('value', item[key])

                    div_form_group.appendChild(label_start)
                    div_form_group.appendChild(input_time)

                    div_repeatable_identifier.appendChild(div_form_group)
                }
            })


            // Agregar al nodo el repeatable
            div_repeatable_holder.appendChild(div_repeatable_identifier)

        })
    }

    function toggleCkecked(element) {
        element.value = element.value == 0 ? 1 : 0
        element.checked = element.value == 0 ? true : false
    }


    function generateRandomFourDigitCode(name) {
        const accessCodeType = name === 'code' ?
            'permanente' :
            'temporal'

        swal({
            title: 'Crear código de acceso ' + accessCodeType,
            text: 'Se creará un código de acceso para el usuario seleccionado y se le enviará por Whatsapp. Continuar?',
            icon: 'info',
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    visible: true,
                    closeModal: true
                },
                confirm: {
                    text: 'Aceptar',
                    value: true,
                    visible: true,
                }
            },
        }).then((result) => {
            if (result) {
                const number = Math.floor(10000 + Math.random() * 90000);
                var field_hidden = document.getElementById(name);
                field_hidden.value = number;

                var field = document.getElementById(name + '-generated');
                field.value = number.toString().charAt(0) + '****';

                swal.close()
            }
        })
    }

    function cleanAllFields() {
        $('#description_field').val('').change();
        $('#description_field_2').val('').change();
        $('#description_field_3').val('').change();
        $("#referrer").val(0);
        $('input[own_id=referrerbox]').prop("checked", false);
        $("#empty_flat").val(0);
        $('input[own_id=empty_flatbox]').prop("checked", false);
        $("#contact_type").val('Residente').change();
        document.getElementById('tyofco').value = 'Residencial';
        $('input[value="Residencial"]').prop('checked', 'checked');
        $('input[value="Oficina"]').prop('checked', false);
        document.getElementsByName('owner_or_tenant')[0].value = 'S/D';
        reloadContactTypes();
        $('input[value="S/D"]').prop('checked', 'checked');
        $('input[value=Inquilino]').prop('checked', false);
        $('input[value=Propietario]').prop('checked', false);
        $('input[value=Apoderado]').prop('checked', false);
        $("#building_id_select").val('-').change();
        $("#flat_id_select").val('').change();
        $('#flat_id_select').empty()
        $('input[name=phone_home]').val('').change();
        $('input[name=phone_mobile]').val('').change();
        $("#dont_call").val(0);
        $('input[own_id=dont_callbox]').prop("checked", false);
        $('input[name=foreign_phone]').val(0).change();
        $('input[own_id=foreign_phonebox]').prop('checked', false)
        $('input[name=home_is_primary_phone]').val(0).change();
        $('input[own_id=home_is_primary_phonebox]').prop('checked', false)
        $('input[name=address]').val('')
        $('input[name=door_number]').val('')
        $('input[name=address_flat_number]').val('')
        $('input[data-repeatable-identifier=others_mobil_phones]').val('')
        $('input[data-repeatable-identifier=schedule]').val('')
        $('textarea[name=description]').val('')
        $('textarea[name=notes_2]').val('')
        $('textarea[name=notes_3]').val('')
        $('input[name=code]').val('').change()
        $('input[name=code-generated]').val('')
        $('input[name=access_code_temporal]').val('')
        $('input[name=access_code_temporal-generated]').val('')
        $('textarea[name=notes_2]').addClass('hidden')
        $('textarea[name=notes_3]').addClass('hidden')
        $('#building_id_select').val('-').trigger('change')
        $('input[name=intercom_name]').val('')
        $('input[own_id=intercom_incoming_callbox]').prop('checked', false)
        $('#name_kazoo').text('-')
        $('#user_sip_kazoo').text('-')
        $('#password_kazoo').text('-')
        $('#number_kazoo').text('-')
        $('#buttons-edit-delete-profile-image').addClass('d-none')

        initSchedule()

        initAuthorizedTime()

        $('#icon_close_date').hide()
        $('#show-access-pin-warning').addClass('hidden')
        $('#phone_mobile').trigger('blur')

    }

    const initAuthorizedTime = () => {
        let today = new Date()
        let tomorrow = new Date(new Date().setDate(today.getDate() + 1))

        $('input[name="start_date"]').val(authorizedTimeFormatDate(today))
        $('input[name="end_date"]').val(authorizedTimeFormatDate(tomorrow, false))
        $('#dateRangeClicked').val('0')
        $('#date_range_content').hide()

        bpFieldInitDateRangeElement($('#start_end_date'))
    }

    const initSchedule = () => {
        $('div[data-repeatable-holder="schedule"]').empty()
    }

    const authorizedTimeFormatDate = (date, initial = true) => {
        let currentDate = moment(new Date(date)).format("YYYY-MM-DD")

        let hour = initial ? "00" : "23"
        let minutes = initial ? "00" : "59"

        return `${currentDate} ${hour}:${minutes}`
    }

    async function saveClaveCiEmail() {
        if (contact) {
            $.ajax({
                url: `{{url('admin/contact/save-email-ci-security_word/${contact}')}}`,
                method: "POST",
                data: {
                    email: $('input[name=email]').val(),
                    ci: $('input[name=ci]').val(),
                    security_word: $('input[name=security_word]').val(),
                }
            })
        }
    }

    function deleteActualBuilding() {
        selected_building = $('#all_contact_buildings').val();

        if (buildings_data_json[selected_building].building_id === "" || buildings_data_json[selected_building]
            .building_id === null) {
            buildings_data_json.splice(selected_building, 1)

            window.localStorage.setItem('contact_building_data', JSON.stringify(buildings_data_json))
            $('#all_contact_buildings').find('option[value="' + selected_building + '"]').remove()
            buildings_data_number -= 1

            $("#all_contact_buildings").val(0).trigger('change');
            return
        }

        let all_buildings_information = $('#building_secret_form').val();
        let all_buildings_information_json = JSON.parse(all_buildings_information);
        let deletedBuildingInformation = all_buildings_information_json.splice(selected_building, 1);
        if (editContact) {
            window.localStorage.setItem('isDeleting', true)
            $.ajax({
                url: '{{ url('/admin/contact/building/relation/contact') }}' + '/' + contact + '/' + deletedBuildingInformation[0].flat_id,
                type: 'delete',
            })
                .done(function (data) {
                    new Noty({
                        type: "success",
                        text: 'Edificio desasociado del contacto.',
                    }).show();

                    $.ajax({
                        method: "DELETE",
                        url: '{{ url('/admin/access-by-pin/contact/') }}' + '/' +
                            {{ $isEdit ? $entry->id : null }} +
                            '/flat/' + deletedBuildingInformation[0].flat_id,
                        success: function (data) {

                        },
                        error: function (xhr, status, error) {
                            console.error('An error has occurred:', error);
                            return
                        }
                    })
                    window.localStorage.removeItem('isDeleting')
                    window.location = '{{url("/admin/contact/")}}' + '/' + {{isset($entry) ? $entry->id : null}} + "/edit"

                })
                .fail(function (jqXHR, textStatus, errorThrown) {
                    new Noty({
                        type: "error",
                        text: 'No se ha podido desasociar. Intente nuevamente.',
                    }).show();
                });
        } else {
            let left_buildings_data_json = buildings_data_json.filter(
                contact => contact.flat_id != deletedBuildingInformation[0].flat_id
            );

            window.localStorage.removeItem('contact_building_data');
            window.localStorage.setItem('contact_building_data', JSON.stringify(left_buildings_data_json));
            console.log('localStorage', JSON.parse(window.localStorage.getItem('contact_building_data')));

            buildings_data_json = left_buildings_data_json;

            if ($('#all_contact_buildings').find('option[value="' + selected_building + '"]').length > 0) {
                $('#all_contact_buildings').find('option[value="' + selected_building + '"]').remove();
            }

            $('#building_secret_form').val(JSON.stringify(left_buildings_data_json));
        }

        $("#all_contact_buildings").val(0)

        buildings_data_number -= 1

        $("#all_contact_buildings").val(0).trigger('change');
    }

    function reloadContactTypes() {
        const contact_type = document.getElementById("contact_type");
        contact_type.innerHTML = ""
        if ($('input[type=radio][value="Residencial"]').is(':checked')) {
            const op_oficinas = @json(\App\Models\User\Contact::$residentialContactTypes ?? []);
            op_oficinas.forEach((item) => {
                const op = document.createElement('option')
                op.value = item
                op.innerText = item
                contact_type.appendChild(op)
            })
            setTimeout(() => {
                contact_type.style.transition = 'all 1.7s ease';
            }, 100)
        } else {
            const op_oficinas = @json(\App\Models\User\Contact::$officeContactTypes ?? []);
            op_oficinas.forEach(item => {
                const op = document.createElement('option')
                op.value = item
                op.innerText = item
                contact_type.appendChild(op)
            })
            setTimeout(() => {
                contact_type.style.transition = 'all 1.7s ease';
            }, 100)
        }
        showElementOnChange()
    }

    function getIntercomIncomingCall(flatId) {
        if (editContact && flatId) {
            fetch('/admin/contact/' + {{ $isEdit ? $entry->id : null }} + '/' + flatId + '/' + 'intercom-incoming-call')
                .then(async (res) => await res.json())
                .then(data => {
                    const {data: intercom_incoming_call} = data
                    $('input[own_id=intercom_incoming_callbox]').val(intercom_incoming_call)
                    $('input[name=intercom_incoming_call]').val(intercom_incoming_call)
                    getInfoOfFieldsChange()
                    $('input[name=intercom_incoming_call]').prop('checked', intercom_incoming_call == true || intercom_incoming_call == 1 ? 'checked' : '');
                    $('input[own_id=intercom_incoming_callbox]').prop('checked', intercom_incoming_call == true || intercom_incoming_call == 1 ? 'checked' : '');
                })
        }
    }

    function getAccessCodes(flat_id) {
        if (editContact && $('#flat_id_select').val() != '' && $('#flat_id_select').val() != '-') {
            $.ajax({
                url: '/admin/access-by-pin/contact/' + {{ $isEdit ? $entry->id : null }} + '/flat/' + $('#flat_id_select').val(),
                method: 'GET',
                dataType: 'json',
                success: function (data) {
                    const {
                        access_by_pin,
                        code,
                        access_code_temporal_show
                    } = data
                    if (access_by_pin === 1) {
                        fillPermanentCode(code)
                        fillTemporaryCodes(access_code_temporal_show)
                        window.localStorage.setItem("flat_id_access_code", flat_id)
                    } else {
                        $("#building-has-access-by-pin-temporal").addClass('hidden')
                        $("#building-has-access-by-pin-permanent").addClass('hidden')
                        window.localStorage.removeItem("flat_id_access_code")
                    }
                }
            })
        } else {
            fillPermanentCode(null)
            fillTemporaryCodes([])
        }
    }

    function fillPermanentCode(code) {
        if (code) {
            $('#code').val(code?.code).change()
            $('#code-generated').val(code?.code.charAt(0) + '****').change()
        } else {
            $('#code').val('').change()
            $('#code-generated').val('').change()
        }

        if (!code && $('#phone_mobile').val().length === 0)
            $('#building-has-access-by-pin-permanent').addClass('hidden')
        else {
            $('#building-has-access-by-pin-permanent').removeClass('hidden')
        }

        if ($('#phone_mobile').val().length === 0) {
            $('.access-code-button-edit').addClass('hidden')
        } else {
            $('.access-code-button-edit').removeClass('hidden')
        }
    }

    function fillTemporaryCodes(temporaryCodes) {
        document.getElementById('temp_access_code').innerHTML = ""
        if (temporaryCodes.length == 0 && $('#phone_mobile').val().length === 0) {
            $("#building-has-access-by-pin-temporal").addClass('hidden')
        } else {
            $("#building-has-access-by-pin-temporal").removeClass('hidden')
        }
        temporaryCodes.forEach((item, index) => {
            addTemporaryCode(index, item.code, item.expires_at_parsed)
        })
        if ($('#phone_mobile').val().length === 0) {
            $('.access-code-button-edit').addClass('hidden')
        } else {
            $('.access-code-button-edit').removeClass('hidden')
        }
    }

    function addTemporaryCode(index, code, expiration) {
        const div_access_code_temporal = document.createElement('div')
        div_access_code_temporal.setAttribute('id', `div_access_code_temporal_${index}`)
        div_access_code_temporal.classList.add('access-code-container', 'mb-1')

        const div_col_md_2 = document.createElement('div')
        div_col_md_2.classList.add('col-md-2')
        const input_access_code_temporal = document.createElement('input')
        input_access_code_temporal.setAttribute('id', `access_code_temporal_${index}`)
        input_access_code_temporal.setAttribute('name', `access_code_temporal[]`)
        input_access_code_temporal.setAttribute('value', code)
        input_access_code_temporal.setAttribute('type', 'hidden')
        input_access_code_temporal.style.width = "80px"
        input_access_code_temporal.style.height = "38px"
        input_access_code_temporal.style.marginBottom = "5px"

        div_col_md_2.appendChild(input_access_code_temporal)

        const input_access_code_temporal_generated = document.createElement('input')
        input_access_code_temporal_generated.setAttribute('id', `access_code_temporal_${index}-generated`)
        input_access_code_temporal_generated.setAttribute('name', `access_code_temporal-generated[]`)
        input_access_code_temporal_generated.setAttribute('type', 'text')
        input_access_code_temporal_generated.setAttribute('value', code.charAt(0) + '****')
        input_access_code_temporal_generated.readOnly = true
        input_access_code_temporal_generated.classList.add('form-control')
        input_access_code_temporal_generated.style.width = "80px"
        input_access_code_temporal_generated.style.height = "38px"
        input_access_code_temporal_generated.style.marginBottom = "5px"
        input_access_code_temporal_generated.style.marginLeft = "-15px"

        div_col_md_2.appendChild(input_access_code_temporal_generated)

        div_access_code_temporal.appendChild(div_col_md_2)

        const div_col_md_10 = document.createElement('div')
        div_col_md_10.classList.add('col-md-10')

        const div_buttons = document.createElement('div')
        div_buttons.classList.add('access-code-container')

        // Expiration date
        const expirationDate = document.createElement('div')
        expirationDate.style.marginLeft = "1rem"

        const spanExpira = document.createElement('span')
        spanExpira.textContent = "Expira "
        const bExpira = document.createElement('b')
        bExpira.setAttribute('id', `temporaryCode_${index}`)
        bExpira.innerText = expiration

        expirationDate.appendChild(spanExpira)
        expirationDate.appendChild(bExpira)

        // eliminar
        const deleteTemporaryCode = document.createElement('div')
        deleteTemporaryCode.innerHTML = `<a onclick="cleanLabelCode('access_code_temporal_${index}', {{ $isEdit ? $entry->id : null }}, contacts[$('#all_contact_buildings').val()].building_id)"
            style="margin-top: 1rem; font-size: 12px; color: red;">Eliminar</a>`
        if (editContact) {
            div_buttons.appendChild(expirationDate)
            div_buttons.appendChild(deleteTemporaryCode)
            div_col_md_10.appendChild(div_buttons)
        }


        div_access_code_temporal.appendChild(div_col_md_10)

        document.getElementById('temp_access_code').appendChild(div_access_code_temporal)

        $(`#code-temporal`).val('').change()
        $(`#code-temporal-generated`).val('').change()
    }

    function cleanLabelCode(name, userId, building_id) {
        if (!building_id) {
            building_id = contacts[$('#all_contact_buildings').val()].building_id
        }
        const accessCodeType = name === 'code' ?
            'permanente' :
            'temporal'

        swal({
            title: "Eliminar código de acceso",
            text: "¿Está seguro que desea eliminar el código de acceso " + accessCodeType + "?",
            icon: 'warning',
            dangerMode: true,
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    visible: true,
                    closeModal: true
                },
                confirm: {
                    text: 'Eliminar',
                    value: true,
                    visible: true,
                }
            },
        }).then((result) => {
            if (result) {
                const field_hidden = document.getElementById(name);
                const code = field_hidden.value;

                if (code === '') {
                    swal({
                        icon: 'error',
                        text: "No se puede eliminar un código que no se ha generado",
                        button: "OK"
                    })
                    return
                }

                $.ajax({
                    type: "DELETE",
                    url: `/api/user/delete-access-code/${userId}/${code}/${building_id}`,
                    success: function (data) {
                        new Noty({
                            type: "success",
                            text: 'Código de acceso eliminado correctamente.',
                        }).show();

                        field_hidden.value = '';

                        const field = document.getElementById(name + '-generated');
                        field.value = '';
                        document.getElementById("temp_access_code").removeChild(document
                            .getElementById('div_' + name))
                    },
                    error: function (xhr, status, error) {
                        console.error('An error has occurred:', error);
                    }
                });
                swal.close()
            }
        })
    }
</script>
