@php
    $isEdit = str_contains(url()->current(), '/edit');
@endphp
<!-- number input -->
@include('crud::fields.inc.wrapper_start')
<label>{!! $field['label'] !!}</label>
@if (isset($field['required']) &&$field['required'])
    <span style='color:red'>*</span>
@endif
@include('crud::fields.inc.translatable_icon')

@if(isset($field['prefix']) || isset($field['suffix']))
    <div class="input-group"> @endif
        @if(isset($field['prefix']))
            <div class="input-group-prepend"><span class="input-group-text">{!! $field['prefix'] !!}</span></div>
        @endif
        <input
            type="number"
            name="{{ $field['name'] }}"
            value="{{ old_empty_or_null($field['name'], '') ??  $field['value'] ?? $field['default'] ?? '' }}"
            style="width: 60px; height: 47px;border-radius: 5px; padding: 16px; margin-left: -10px; margin-top: @if($isEdit) 10px; @else 20px; @endif"
            @include('crud::fields.inc.attributes')
        >
        @if(isset($field['suffix']))
            <div class="input-group-append"><span class="input-group-text">{!! $field['suffix'] !!}</span></div>
        @endif

        @if(isset($field['prefix']) || isset($field['suffix'])) </div>
@endif

{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')
