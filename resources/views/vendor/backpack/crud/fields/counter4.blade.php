<div class="col-md-3" style="">
    <label style="margin-left: -13px;">{{$field['label']}}</label>
    <div class="row">
        <div class="col-md-2 text-center create_edit_back_color" onclick="subKey4()" style="border-radius: 5px; padding: 8px; margin-right: -16px; z-index: 1; cursor: pointer;">-</div>
        <input class="col-md-10 text-center white-field" style="padding-inline: 0px; border-radius: 5px;;" value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? 0 }}" id="{{$field['name']}}" type="number" min="0" name="{{$field['name']}}">
        <div onclick="addKey4()" class="col-md-2 text-center create_edit_back_color" style="border-radius: 5px; padding: 8px; margin-left: -16px; z-index: 1; cursor: pointer;">+</div>
    </div>
</div>


<script>
    countKey4 = 0;
    function addKey4(){
        countKey4 = 1 + countKey4;
        document.getElementById('<?php echo $field['name'] ?>').value = countKey4;
    }

    function subKey4(){
        if(countKey4 > 0){
            countKey4 = countKey4 - 1;
            document.getElementById('<?php echo $field['name'] ?>').value = countKey4;
        }

    }
</script>

