@php
    $usersToEdit = false;
    $isAtc = backpack_user()->hasRole('Representante Atención al Cliente');
    $area = isset($entry) ? \App\Models\Area::query()->find($entry?->area_id) : null;
    if (isset($entry)) {
        $usersArea =$area?->responsables() ?? null;
        $usersToEdit = \App\Models\Area::checkIfUserIsInArea($usersArea) || ($isAtc && $area?->name == 'Monitoreo');
    }
    $isAdmin = backpack_user()->hasRole('Admin');
    $isEdit = str_contains(url()->current(), '/edit')
@endphp
<div class="form-group col-md-12 white-field ">
    <div class="mr-sm-2" style="margin-bottom: 8px;">Asignar a</div>
    <select name="area_id" id="select-case-area" onchange="changeAreaForResponsables(this.value)">
        @foreach(\App\Models\Area::all() as $area)
            <option value="{{$area->id}}" @if(isset($entry) && $area->id == $entry->area_id) selected
                    @endif @if(!$isAdmin && !$usersToEdit && $isEdit) disabled @endif>{{$area->name}}</option>
        @endforeach
    </select>
</div>

@push('crud_fields_scripts')
    <!-- include select2 js-->
    <script src="{{ asset('packages/select2/dist/js/select2.full.min.js') }}"></script>
    @if (app()->getLocale() !== 'en')
        <script src="{{ asset('packages/select2/dist/js/i18n/' . app()->getLocale() . '.js') }}"></script>
    @endif
    <script>
        let lastCategorySelectedCreateEdit = null

        @if($isEdit)
        changeAreaForResponsables('{{$entry?->area_id}}')
        @endif

        function changeAreaForResponsables(value) {
            getResponsablesCreateEdit(value)
        }

        async function getResponsablesCreateEdit(cat) {
            if (!cat) return;
            if (cat != lastCategorySelectedCreateEdit) {
                lastCategorySelectedCreateEdit = cat
                $('#responsablec').html('')
                fetch('/admin/area/' + cat + '/responsables')
                    .then(async data => {
                        const responsables = await data.json()
                        loadSelect2CaseResponsableCreateEdit(responsables)
                    })
            }
        }

        function loadSelect2CaseResponsableCreateEdit(responsible) {
            $('#responsablec').select2({
                allowClear: true,
                placeholder: "Seleccionar una persona",
            });

            let select = $('#responsablec');
            select.empty();
            if (responsible) {
                responsible.forEach(user => {
                    let option = new Option(user.complete_name, user.id, true, true);
                    @if(isset($entry) && $entry->responsable) option.selected = true;
                    @endif
                    select.append(option)
                })
            }
            $('#responsablec').val('')
        }

        function loadSelect2CaseArea() {
            $('#select-case-area').select2({
                allowClear: true,
                placeholder: "Seleccionar un área",
            });
            $('#select-case-area').val('').change();
        }

        function loadSelect2CaseAreaToEdit() {
            $('#select-case-area').select2({
                allowClear: true,
                placeholder: "Seleccionar un área",
            });
        }
    </script>
@endpush
