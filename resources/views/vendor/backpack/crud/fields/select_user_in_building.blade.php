<!-- select -->
@php
    $entry = $entry ?? null;
        $current_value = old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? '';
        $entity_model = $crud->getRelationModel($field['entity'],  - 1);

        if (is_object($current_value) && is_subclass_of(get_class($current_value), 'Illuminate\Database\Eloquent\Model') ) {
            $current_value = $current_value->getKey();
        }


        if ($entry) {
            $users = \App\Models\User\Contact::whereHas('contacts', function($q) use($entry) {
                $q->where('building_id', $entry->building_id)
                ->where('flat_id', $entry->flat_id);
            })->whereNull('deleted_at')->get();
        }


$contact_id_url = $_GET['contact_id'] ?? null;

@endphp

@include('crud::fields.inc.wrapper_start')

<label>{!! $field['label'] !!} @if(array_key_exists('required', $field) && $field['required'])
        <span style='color:red'>*</span>
    @endif </label>

@include('crud::fields.inc.translatable_icon')

<select
    id="select_users"
    name="{{ $field['name'] }}"
    onchange="changePhoneAndEmail()"
    class="white-field form-control"
    @include('crud::fields.inc.attributes')
>
    @if ($entry)
        <option selected="selected" disabled value=""></option>
        @foreach ($users as $user)
            @if($entry->user_id)
                <option value="{{ $user->id }}"
                        @if ($user->id == $entry->user_id)
                            selected
                    @endif
                >
                    {{ $user->complete_name }}
                </option>
            @else
                <option value="{{ $user->id }}">
                    {{ $user->complete_name }}
                </option>
            @endif
        @endforeach
    @endif
</select>


{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif

@push('crud_fields_styles')
    <!-- include select2 css-->
    <link href="{{ asset('packages/select2/dist/css/select2.min.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('packages/select2-bootstrap-theme/dist/select2-bootstrap.min.css') }}" rel="stylesheet"
          type="text/css"/>
@endpush

{{-- FIELD JS - will be loaded in the after_scripts section --}}
@push('crud_fields_scripts')
    <!-- include select2 js-->
    <script src="{{ asset('packages/select2/dist/js/select2.full.min.js') }}"></script>
@endpush

<script>

    const isEdit = window.location.href.includes('/edit')
    let global_flat_id = isEdit ? '{{$entry?->flat_id}}' : ''
    let global_building_id = isEdit ? '{{$entry?->building_id}}' : ''
    let first_time = isEdit
    let currentUser = '{{$entry?->user_id}}'

    function updateUsersbyBuildingFlats() {
        flat = $('#select2-flat_id_select-container');
        building = $('#select2-building_id_select-container').val();
        putUsersFlats()
    }

    updateUsersbyBuildingFlats()

    function getUsersByBuilding(building_id) {
        var name = '';
        $.ajax({
            url: '/admin/building/' + building_id + '/users',
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                name = data;
            },
        });
        return name.original.data;
    }

    function getUsersByFlatsOrBuildings(flat_id) {
        let name = '';
        const $contactIdUrl = new URLSearchParams(window.location.search).get('contact_id')
        $('#select_users').val('')
        if (flat_id) {
            $('#select_users').prop('disabled', true)
            fetch('/admin/building/flats/' + flat_id + '/users')
                .then((res) => {
                    if (res.ok) {
                        return res.json()
                    }
                }).then((data) => {
                const users = data.data
                $('#select_users').empty()

                $('#select_users').append('<option value=""> - </option>');

                for (i = 0; i < users.length; i++) {
                    const opt = document.createElement('option');
                    if (users[i] != null) {
                        opt.value = users[i].id;
                        opt.innerHTML = users[i].complete_name;
                        if (($contactIdUrl && users[i].id == $contactIdUrl)) {
                            opt.selected = true;
                            $('#select_users').val($contactIdUrl)
                        } else if (isEdit && users[i].id == currentUser) {
                            opt.selected = true;
                            $('#select_users').val($contactIdUrl)
                        }
                        $('#select_users').append(opt);
                    }
                }
                deleteSelectError(flat_id, 'flat_id_select')
                $('#select_users').prop('disabled', false)
                $('#select_users').trigger('change');
            })

        }
    }

    function deleteSelectError(value, fieldClass) {
        let titleError = document.querySelector('.error-message-' + fieldClass);
        if (value != '') {
            titleError && titleError.remove();
        }
    }

    async function getPhoneAndEmail(contact_id) {
        let user = null
        const res = await fetch('/admin/building/contact/user/' + contact_id + '/' + global_flat_id)
        if (res.ok)
            user = await res.json()

        setTimeout(() => {
            let url = removeURLParameter(window.location.href, 'flat_id')
            url = removeURLParameter(url, 'contact_id')
            url = removeURLParameter(url, 'building_id')
            window.history.replaceState(null, '', url.toString())
        }, 2000)

        return user;
    }

    function putUsers(value) {
        $('input[name=phone_number]').val('')
        $('input[name=mail]').val('')
        $('#select_users').empty()
        deleteSelectError(value, 'building_id_select')
        setTimeout(() => {
            let url = removeURLParameter(window.location.href, 'building_id')
            window.history.replaceState(null, '', url.toString())
        }, 1000)
    }

    function putUsersFlats() {
        building_id = isEdit && first_time ? global_building_id : $('#building_id_select').val()
        let flat_id = new URLSearchParams(window.location.search).get('flat_id')

        if (!flat_id) {
            flat_id = isEdit && first_time ? global_flat_id : $('#flat_id_select').val()
        }
        if (isEdit && first_time)
            first_time = false

        if ($('#flats_id_selected').val() != flat_id && $('#flats_id_selected').val() != undefined)
            flat_id = $('#flat_id_select').val()

        global_flat_id = flat_id

        $('input[name=phone_number]').val('')
        $('input[name=mail]').val('')
        $('#select_users').val('').trigger('change');
        getUsersByFlatsOrBuildings(flat_id);


    }

    function removeURLParameter(url, parameter) {
        var urlparts = url.split('?');

        if (urlparts.length >= 2) {
            var prefix = encodeURIComponent(parameter) + '=';
            var pars = urlparts[1].split(/[&;]/g);

            for (var i = pars.length; i-- > 0;) {
                if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                    pars.splice(i, 1);
                }
            }

            url = urlparts[0] + (pars.length > 0 ? '?' + pars.join('&') : '');
        }

        return url;
    }


    function loadContactCase() {
        $('#select_users').val('').trigger('change');
    }

    async function changePhoneAndEmail() {
        let email = document.getElementById('text_email');
        let phone_number = document.getElementById('text_phone_number');
        if (!$('#select_users').val() && email && phone_number) {
            email.innerHTML = '';
            email.value = '';
            phone_number.innerHTML = '';
            phone_number.value = '';
            return
        }

        if ($('#select_users').val()) {
            const user = await getPhoneAndEmail($('#select_users').val())
            const contact = user?.data
            email.innerHTML = contact ? contact.email : ''
            email.value = contact ? contact.email : ''
            phone_number.innerHTML = contact ? contact.phone_mobile : ''
            phone_number.value = contact ? contact.phone_mobile : ''
        }
    }

    function loadSelect2CaseContact() {
        $('#select_users').select2({
            allowClear: true,
            placeholder: "",
        });
    }
</script>

@include('crud::fields.inc.wrapper_end')
