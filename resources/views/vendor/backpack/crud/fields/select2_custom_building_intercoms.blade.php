@php
    $current_value =  $entry?->intercom_type ?? null;
    if ($current_value != ''){
        $current_value = \App\Models\Building::where('building_number',$current_value)->first()->id;
    }

    if (is_object($current_value) && is_subclass_of(get_class($current_value), 'Illuminate\Database\Eloquent\Model') ) {
        $current_value = $current_value->getKey();
    }
    if (!isset($field['options'])) {
        $options = $field['model']::all();
    } else {
        $options = call_user_func($field['options'], $field['model']::query());
    }

    $field['allows_null'] = $field['allows_null'] ?? $crud->model::isColumnNullable($field['name']);
@endphp

@include('crud::fields.inc.wrapper_start')

<label>{!! $field['label'] !!}</label>
@include('crud::fields.inc.translatable_icon')

<select
    name="{{ $field['name'] }}"
    id="building_intercom_select"
    style="width: 100%"
    data-field-is-inline="{{var_export($inlineCreate ?? false)}}"
    data-init-function="bpFieldInitSelect2Element"
    data-language="{{ str_replace('_', '-', app()->getLocale()) }}"
    @include('crud::fields.inc.attributes', ['default_class' =>  'form-control select2_field'])
    onchange="changeAdamLogicIntercom(this)"
>
    @if ($field['allows_null'])
        <option value="">-</option>
    @endif

    @if (count($options))
        @foreach ($options as $option)
            @if($current_value == $option->getKey())
                <option value="{{ $option->getKey() }}" selected>{{ $option->{$field['attribute1']} }}
                    - {{ $option->{$field['attribute2']} }}</option>
            @else
                <option value="{{ $option->getKey() }}">{{ $option->{$field['attribute1']} }}
                    - {{ $option->{$field['attribute2']} }}</option>
            @endif
        @endforeach
    @endif
</select>

{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')

{{-- ########################################## --}}
{{-- Extra CSS and JS for this particular field --}}
{{-- If a field type is shown multiple times on a form, the CSS and JS will only be loaded once --}}
@if ($crud->fieldTypeNotLoaded($field))
    @php
        $crud->markFieldTypeAsLoaded($field);
    @endphp

    {{-- FIELD CSS - will be loaded in the after_styles section --}}
    @push('crud_fields_styles')
        <!-- include select2 css-->
        <link href="{{ asset('packages/select2/dist/css/select2.min.css') }}" rel="stylesheet" type="text/css"/>
        <link href="{{ asset('packages/select2-bootstrap-theme/dist/select2-bootstrap.min.css') }}" rel="stylesheet"
              type="text/css"/>
    @endpush

    {{-- FIELD JS - will be loaded in the after_scripts section --}}
    @push('crud_fields_scripts')
        <!-- include select2 js-->
        <script src="{{ asset('packages/select2/dist/js/select2.full.min.js') }}"></script>
        @if (app()->getLocale() !== 'en')
            <script
                src="{{ asset('packages/select2/dist/js/i18n/' . str_replace('_', '-', app()->getLocale()) . '.js') }}"></script>
        @endif
        <script>
            function bpFieldInitSelect2Element(element) {
                if (!element.hasClass("select2-hidden-accessible")) {
                    let $isFieldInline = element.data('field-is-inline');

                    element.select2({
                        theme: "bootstrap",
                        dropdownParent: $isFieldInline ? $('#inline-create-dialog .modal-content') : document.body
                    });
                }
            }

            function hideDefaultAndShowAdamFieldsIntercom() {
                $('#num-ak').closest('div.form-group').hide();
                $('#extension_3cx').closest('div.form-group').hide();
                $('#asterisk_extension_number').closest('div.form-group').hide();
                $('#delay').closest('div.form-group').hide();
                $('#order_group').closest('div.form-group').hide();
                $('#access_group').closest('div.form-group').hide();
                $('#switch').closest('div.form-group').hide();


                $('#user').closest('div.form-group').show();
                $('#password').closest('div.form-group').show();
                $('#action').closest('div.form-group').show();
            }

            function hideAdamFieldsAndShowDefaultFieldsIntercom() {
                $('#num-ak').closest('div.form-group').show();
                $('#extension_3cx').closest('div.form-group').show();
                $('#asterisk_extension_number').closest('div.form-group').show();
                $('#delay').closest('div.form-group').show();
                $('#order_group').closest('div.form-group').show();
                $('#access_group').closest('div.form-group').show();
                $('#switch').closest('div.form-group').show();


                $('#user').closest('div.form-group').hide();
                $('#password').closest('div.form-group').hide();
                $('#action').closest('div.form-group').hide();
            }

            function changeAdamLogicIntercom(element) {
                // 3 intercom Adam
                if (element.value == 3) {
                    hideDefaultAndShowAdamFieldsIntercom();
                } else {
                    hideAdamFieldsAndShowDefaultFieldsIntercom();
                }
            }

            document.addEventListener('DOMContentLoaded', function () {
                let selectElement = document.getElementById('building_intercom_select');
                if (selectElement.value == 3) {
                    changeAdamLogicIntercom(selectElement);
                }
            });

            let alphanumericInputs = document.querySelectorAll('#user, #password', '#action');

            function validateAlphanumeric(event) {
                var value = event.target.value;
                event.target.value = value.replace(/[^a-zA-Z0-9]/g, '');
            }

            alphanumericInputs.forEach(function (input) {
                input.addEventListener('input', validateAlphanumeric);
            });
        </script>
    @endpush

@endif
{{-- End of Extra CSS and JS --}}
{{-- ########################################## --}}
