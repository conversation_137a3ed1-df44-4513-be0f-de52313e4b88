@php
    $has_keyboard = false;
    $is_akuvox = false;
    if ($crud->entry) {

        foreach ($crud->entry->intercoms as $intercom) {
            if ($intercom->has_keyboard) {
                $has_keyboard = true;
                break;
            }
        }

        $is_akuvox = $crud->entry->intercoms()?->first()?->intercomModel->type == 'AKUVOX';
    }
    $field['value'] = old_empty_or_null($field['name'], '') ?? ($field['value'] ?? ($field['default'] ?? ''));
@endphp

@include('crud::fields.inc.wrapper_start')
@include('crud::fields.inc.translatable_icon')

@if ($has_keyboard || $is_akuvox)
    <div class="checkbox form-check form-switch col-md-10" style="left: 30px; padding: 5px 20px">
        <input type="hidden" name="{{ $field['name'] }}" value="0">
        <input type="checkbox" name="{{ $field['name'] }}" value="1" class="form-switch form-check-input"
            @if ((bool) $field['value']) checked @endif
            @if (isset($field['attributes'])) @foreach ($field['attributes'] as $attribute => $value)
                   {{ $attribute }}="{{ $value }}"
        @endforeach @endif>
        <label class="form-check-label">{!! $field['label'] !!}</label>
    </div>
@endif

@include('crud::fields.inc.wrapper_end')

<style>
    @media (max-width: 1638px) {
        .min-view {
            display: none !important;
        }
    }
</style>

<script>
    let access_by_pin = document.getElementById('{{ $field['name'] }}');

    access_by_pin?.addEventListener('change', function() {

        if (this.checked) {
            alertAccessPin(
                '¿Este cambio activará el acceso por PIN incluyendo su ofrecimiento por WhatsApp para permisos temporales.?',
                this, false);
        } else {
            alertAccessPin(
                '¿Este cambio desactivará el acceso por PIN para todos los residentes que lo tienen activo.?',
                this, true)
        }
    });

    function alertAccessPin(msg, checkbox, param) {
        let resultado = confirm(msg);
        if (!resultado) {
            checkbox.checked = param;
        }
    }
</script>
