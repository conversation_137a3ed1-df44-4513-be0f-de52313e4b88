<div class="col-md-3" style="">
    <label title="Administración" style="margin-left: -13px;">{{$field['label']}}</label>
    <div class="row">
        <div class="col-md-2 text-center create_edit_back_color" onclick="subKey2()" style=" border-radius: 5px; padding: 8px; margin-right: -16px; z-index: 1; cursor: pointer;">-</div>
        <input class="col-md-10 text-center white-field" style="padding-inline: 0px; border-radius: 5px;;" value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? 0 }}" id="{{$field['name']}}" id="{{$field['name']}}" type="number" min="0" name="{{$field['name']}}">
        <div onclick="addKey2()" class="col-md-2 text-center create_edit_back_color" style=" border-radius: 5px; padding: 8px; margin-left: -16px; z-index: 1; cursor: pointer;">+</div>
    </div>
</div>


<script>
    countKey2 = 0;
    function addKey2(){
        countKey2 = 1 + countKey2;
        document.getElementById('<?php echo $field['name'] ?>').value = countKey2;
    }

    function subKey2(){
        if(countKey2 > 0){
            countKey2 = countKey2 - 1;
            document.getElementById('<?php echo $field['name'] ?>').value = countKey2;
        }

    }
</script>

