<?php
$start_date = $entry->start_date ?? null;
$end_date = $entry->end_date ?? null;
$displayStyleDataRange = ($start_date && $end_date) ? 'block' : 'none';
$boolValueClicked = ($start_date && $end_date) ? 'true' : 'false';
?>
<div style="position: relative;">
    @if(isset($field['new_item_label']))
        <div type="button" id="date_rage_label"
             class="btn btn-link add-repeatable-element-button show-on-change-base"
             style="text-align: left;">
            {{$field['new_item_label']}}
        </div>
    @endif
    @if(!str_contains(url()->current(), 'authorized'))
        <div id="icon_close_date" onclick="deleteTimeDateRange()">
            <ion-icon name="close-outline" style="zoom:1.5"></ion-icon>
        </div>
    @endif
</div>
<div id="date_range_content" style="display: block;" class="p-0">
    @include('crud::fields.date_range_pro_authorized_contacts')
</div>
<script>
    document.getElementById('date_rage_label')?.addEventListener('click', function () {
        document.getElementById('date_rage_label').style.display = 'none';
        document.getElementById('date_range_content').style.display = 'block';
        document.getElementById('icon_close_date').style.display = 'flex';
        document.getElementById('dateRangeClicked').value = 'true';
    });

    setTimeout(function () {
        let dataRange = $('#date_range_content').css('display');
        if (dataRange === 'block') {
            $('#date_rage_label').css('display', 'none');
            $('#icon_close_date').css('display', 'block');
        }
    }, 500);

    function deleteTimeDateRange() {
        document.getElementById('date_rage_label').style.display = 'block';
        document.getElementById('date_range_content').style.display = 'none';
        document.getElementById('icon_close_date').style.display = 'none';
        document.getElementById('dateRangeClicked').value = 'false';
    }

    document.getElementById('contact_type')?.addEventListener('change', function () {
        document.getElementById('date_range_content').style.display = 'none';
        document.getElementById('icon_close_date').style.display = 'none';
    });

</script>
<style>
    #icon_close_date {
        position: absolute;
        top: -5px;
        right: 20px;
        cursor: pointer;
        z-index: 1;
        display: none;
        background: #e8ebf0;
        border-radius: 50%;

    }

    #icon_close_date:hover {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 50%;
    }
</style>
