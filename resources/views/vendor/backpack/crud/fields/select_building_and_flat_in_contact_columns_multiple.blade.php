@php
    $currentContact = old($field['name']) ?? $field['value'] ?? $field['default'] ?? null;
    $current_building = null;
    if ($currentContact){
        $currentContact = \App\Models\User\Contact::findOrFail($currentContact);
        $current_contact_name = $currentContact->name;
        $current_building = $currentContact->building_id;
        $current_flat = $currentContact->flat_id;
    }
    else {
        $current_flat = -1;

    }

    if(array_key_exists('building_id',$_GET)){
        $current_building = $_GET['building_id'];
    }
@endphp
@include('crud::fields.inc.wrapper_start')
<div class="card"
     style="">

    <div id="contact_non_registered" class="row">
        <div class="col-md-6">
            <label>Edificio <span style='color:red'>*</span></label>
            @include('crud::fields.inc.translatable_icon')
            <select
                name="building_id"
                id="building_id_select"
                style="width: 100%"
                class="select2"
                onchange="type_buildings()"
                @include('crud::fields.inc.attributes', ['default_class' =>  'form-control select2_field'])
            >
                <option value="">-</option>

                @foreach (\App\Models\Building::all() as $building)

                    @if($current_building ?? null)
                        @if($current_building == $building->getKey())
                            <option value="{{ $building->id }}" selected>{{ $building->name }}
                                - {{\App\Helpers\Helper::threeFiguresNumber($building->building_number)}}</option>
                        @else
                            <option value="{{ $building->id }}">{{ $building->name }}
                                - {{\App\Helpers\Helper::threeFiguresNumber($building->building_number)}}</option>
                        @endif

                    @else
                        <option value="{{ $building->id }}">{{ $building->name }}
                            - {{\App\Helpers\Helper::threeFiguresNumber($building->building_number)}}</option>
                    @endif
                @endforeach
            </select>
        </div>
        <br>
        <br>
        <div class="col-md-6">
            <label>Apartamento <span style='color:red'>*</span></label>
            @livewire('select-flat', ['flatSelected' => $current_flat, 'currentBuilding' => $current_building])
        </div>
    </div>

</div>
@include('crud::fields.inc.wrapper_end')
{{-- ########################################## --}}
{{-- Extra CSS and JS for this particular field --}}
{{-- If a field type is shown multiple times on a form, the CSS and JS will only be loaded once --}}
@if ($crud->fieldTypeNotLoaded($field))
    @php
        $crud->markFieldTypeAsLoaded($field);
    @endphp
    @push('crud_fields_styles')
        <!-- include select2 css-->
        <link href="{{ asset('packages/select2/dist/css/select2.min.css') }}" rel="stylesheet" type="text/css"/>
        <link href="{{ asset('packages/select2-bootstrap-theme/dist/select2-bootstrap.min.css') }}" rel="stylesheet"
              type="text/css"/>
    @endpush

    {{-- FIELD JS - will be loaded in the after_scripts section --}}
    @push('crud_fields_scripts')
        <!-- include select2 js-->
        <script src="{{ asset('packages/select2/dist/js/select2.full.min.js') }}"></script>
        @if (app()->getLocale() !== 'en')
            <script src="{{ asset('packages/select2/dist/js/i18n/' . app()->getLocale() . '.js') }}"></script>
        @endif
        <script>
            function bpFieldInitSelect2Element(element) {
                // element will be a jQuery wrapped DOM node
                if (!element.hasClass("select2-hidden-accessible")) {
                    element.select2({
                        theme: "bootstrap"
                    });
                }
            }
        </script>
    @endpush

@endif
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script>

    let createContactFromBuilding = new URLSearchParams(window.location.search).get('building_id')
    if (createContactFromBuilding) $('#building_id_select').trigger('change')

    function loadFlats() {
        var building_id = $("#building_id_select").val();
        var flat_id = $("#flat_id_select").val();
        if (building_id) {
            $.get('{{url('admin/building/')}}' + '/' + building_id + '/flats', function (data) {
                $("#flat_id_select").html(formatFlats(data));
                $("#building_id_select").val(building_id);
                if (!window.location.href.includes('authorized'))
                    buildingHasAccessByPin($('#building_id_select').val())
            });
        }
    }

    function formatFlats(data) {
        var html = '<option value=""> - </option>';
        sortJSON(data, "number", 'ASC');
        for (var i = 0; data.length > i; i++) {
            if (data[i].id == '{{$current_flat}}') {
                html += '<option value="' + data[i].id + '" selected>' + data[i].number_with_tower + '</option>';
            } else {
                html += '<option value="' + data[i].id + '">' + data[i].number_with_tower + '</option>';
            }
        }
        return html;
    }

    function sortJSON(data, key, orden) {
        return data.sort(function (a, b) {
            var x = a[key],
                y = b[key];
            if (orden === 'ASC') {
                return ((parseInt(x) < parseInt(y)) ? -1 : ((parseInt(x) > parseInt(y)) ? 1 : 0));
            }
            if (orden === 'DESC') {
                return ((parseInt(x) > parseInt(y)) ? -1 : ((parseInt(x) < parseInt(y)) ? 1 : 0));
            }
        });
    }

    function loadContacts() {
        var flat_id = $("#flat_id_select").val();
        if (flat_id) {
            $.get('{{url('admin/flat/')}}' + '/' + flat_id + '/contacts', function (data) {
                $("#flat_id_select").val(flat_id);
                $('.select2').select2();
            });
        }
    }

    function formatContacts(data) {
        var html = '<option value=""> - </option>';
        for (var i = 0; data.length > i; i++) {
            html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        return html;
    }

    loadFlats();
    loadContacts();

    function saveContact() {
        let contact_type = $('#contact_type option:selected').text();
        let owner_or_tenant = $('#owner_or_tenant option:selected').text();
        if (contact_type == 'Abrir a') {
            alert('No se puede guardar como contacto permanente un contacto de tipo "Abrir a"');
        } else if (confirm("Seguro que desea ingresar el contacto temporal como un contacto permanente?")) {
            $.ajax({
                type: "POST",
                url: '{{url("/admin/contact/")}}',
                data: {
                    name: $('#contact_name').val(),
                    phone_mobile: $('#contact_phone').val(),
                    phone_home: $('#phone_home').val(),
                    foreign_phone: document.getElementsByName('foreign_phone')[0].checked ? 1 : 0,
                    home_is_primary_phone: document.getElementsByName('home_is_primary_phone')[0].checked ? 1 : 0,
                    foreign_document: document.getElementsByName('foreign_document')[0].checked ? 1 : 0,
                    under_age: document.getElementsByName('under_age')[0].checked ? 1 : 0,
                    email: $('#contact_mail').val(),
                    ci: $('#contact_ci').val(),
                    description: $('#contact_description').val(),
                    password: '123654',
                    password_confirmation: '123654',
                    building_id: $('#building_id_select').val(),
                    flat_id: $('#flat_id_select').val(),
                    contact_type: contact_type,
                    owner_or_tenant: owner_or_tenant,
                    temporary_contact: true,
                },
                success: function () {
                    alert('Contacto agregado al sistema exitosamente!');
                    window.location.href = "/admin/temporary_contact";
                },
            })
            ;
        }
    }

    function buildingHasAccessByPin(building_id) {
        const url = `/admin/building/access-by-pin/${building_id}`
        $.ajax({
            url: url,
            method: 'GET',
            success: (data) => {
                const {
                    access_by_pin
                } = data
                if (access_by_pin) {
                    if ($('#phone_mobile').val().trim().length === 0 && $('#building_id_select').val() != '' && $('#building_id_select').val() != '-') {
                        $('#show-access-pin-warning').removeClass('hidden')
                        $('.access-code-button-edit').addClass('hidden')
                        getAccessCodes($('#flat_id_select').val())
                    } else {
                        $('#show-access-pin-warning').addClass('hidden')
                        $('.access-code-button-edit').removeClass('hidden')
                        $('#building-has-access-by-pin-permanent').removeClass('hidden')
                        $('#building-has-access-by-pin-temporal').removeClass('hidden')
                    }
                } else {
                    $('#show-access-pin-warning').addClass('hidden')
                    $("#building-has-access-by-pin-temporal").addClass('hidden')
                    $("#building-has-access-by-pin-permanent").addClass('hidden')
                }
            }
        })
    }

    function hideSaveContactButton() {
        $('#add_non_registered_contact').hide();
    }

    $(document).ready(function () {
        $('.select2').select2();
        $('#building_id_select').on('change', function () {
            loadFlats();
        });
    });

    function getBuildingName() {
        var e = document.getElementById('building_id_select');
        let name = ''
        if (e.selectedIndex > -1) {
            name = e.options[e.selectedIndex].text;
        }
        // var name = e.options[e.selectedIndex].text;
        return name;
    }

    function getBuildingInfo(name) {
        var building = '';
        console.log(name)
        $.ajax({
            url: '{{url('/admin/building/info_of')}}' + '/' + name,
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                building = data;
            },
        });


        return building;
    }

    async function getBuildingInfoById(id) {
        let building = null;
        const res = await fetch('{{url('/admin/building/info_of/building')}}' + '/' + id)
        if (res)
            building = res.json()

        return building;
    }

    async function type_buildings() {
        $(document).ready(async () => {
            if ($('#building_id_select').val() != '' && $('#building_id_select').val() != null) {
                const buildings = await getBuildingInfoById($('#building_id_select').val());
                const {building_type, building_number, building_name} = buildings

                $select2_building_text =  `${building_number} - ${building_name}`
                if ($('#all_contact_buildings').val() == 0) {
                    $select2_building_text += ' - Edificio principal'
                }

                $('#all_contact_buildings').find('option[value="' + $('#all_contact_buildings').val() + '"]').text($select2_building_text)
                $('#all_contact_buildings').select2();

                if (building_type && building_type.toUpperCase() === 'OFICINA') {
                    $('#tyofco').val('Oficina')
                    $('input[type=radio][value="Oficina"]').prop('checked', true).trigger('change')
                    $('input[type=radio][value="Residencial"]').prop('checked', false)
                } else {
                    $('#tyofco').val('Residencial')
                    $('input[type=radio][value="Residencial"]').prop('checked', true).trigger('change')
                    $('input[type=radio][value="Oficina"]').prop('checked', false)
                }

                if (!window.location.href.includes('authorized'))
                    deleteSelectError(this.value, 'building_id_select')
                else {
                    $('#building_verificate').text($('#select2-building_id_select-container').text().split('-').reverse().join(' - '))
                }
            } else {
                $('#building_verificate').text('-')
            }
        })
    }
</script>


{{-- End of Extra CSS and JS --}}
{{-- ########################################## --}}
