@php
    $optionValue = old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? '';
    $field['wrapper'] = $field['wrapper'] ?? $field['wrapperAttributes'] ?? [];
    $field['wrapper']['data-init-function'] = $field['wrapper']['data-init-function'] ?? 'bpFieldInitRadioElement';
    $buildingServiceIcons = \App\Models\Building::$buildingServiceIcons;
@endphp

@include('crud::fields.inc.wrapper_start')

<input type="hidden" value="{{ $optionValue }}" name="{{$field['name']}}"/>

@if (isset($field['options']) && is_array($field['options']))
    <label>{!! $field['label'] !!}</label>
    @foreach ($field['options'][0] as $key => $value)
        <div class="ml-2 {{ isset($field['inline']) && $field['inline'] ? 'form-check-inline' : '' }} ">
            <input type="radio"
                   style="z-index: 2"
                   class="form-check-input"
                   value="{{ $value }}"
                   @if($optionValue == $value) checked @endif
                @include('crud::fields.inc.attributes')
            >
            <div class="d-flex">
                <label
                    style="margin-left: -8px"
                    class="{{ isset($field['inline']) && $field['inline'] ? 'radio-inline' : '' }} col-md-10 form-check-label font-weight-normal">
                    {{ $value }}
                </label>
                @if (\App\Models\Building::$buildingServiceIcons[$value]['type'] == 'ion')
                    <ion-icon class="icon-in-table-no-action col-md-2 p-0"
                              style="pointer-events:none;"
                              name="{{ \App\Models\Building::$buildingServiceIcons[$value]['icon'] }}">
                    </ion-icon>
                @elseif (\App\Models\Building::$buildingServiceIcons[$value]['type'] == 'svg')
                    <object data="{{ asset('svg/' . \App\Models\Building::$buildingServiceIcons[$value]['icon'] . '.svg') }}"
                            type="image/svg+xml"
                            class="icon-in-table-no-action col-md-2 p-0"
                            style="pointer-events:none; height: 24px;">
                    </object>
                @endif
            </div>
        </div>
    @endforeach
@endif

@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif

@include('crud::fields.inc.wrapper_end')

@if ($crud->fieldTypeNotLoaded($field))
    @php
        $crud->markFieldTypeAsLoaded($field);
    @endphp
    @push('crud_fields_scripts')
        <script>
            function bpFieldInitRadioElement(element) {
                var hiddenInput = element.find('input[type=hidden]');
                var value = hiddenInput.val();
                var id = 'radio_' + Math.floor(Math.random() * 1000000);

                // set unique IDs so that labels are correlated with inputs
                element.find('.form-check input[type=radio]').each(function (index, item) {
                    $(this).attr('id', id + index);
                    $(this).siblings('label').attr('for', id + index);
                });

                // when one radio input is selected
                element.find('input[type=radio]').change(function (event) {
                    hiddenInput.val($(this).val()).change();
                    element.find('input[type=radio]').not(this).prop('checked', false);
                });

                element.find('input[type=radio][value="' + value + '"]').prop('checked', true);
            }
        </script>
    @endpush
@endif
