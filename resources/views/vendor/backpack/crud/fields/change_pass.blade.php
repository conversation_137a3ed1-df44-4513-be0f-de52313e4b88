@php
    $isEdit = $entry ?? false;
    $isMonitoreo = backpack_user()->hasRole('Operador Monitoreo')
@endphp

@if($isEdit)
    @include('crud::fields.inc.wrapper_start')

    <div>
        <a class="{{$isMonitoreo ? 'd-none' : ''}}" href="#" onclick="resetPassword('{{$entry->email ?? '<EMAIL>'}}')">{!! $field['label'] !!}</a>
    </div>

    <script>
        function resetPassword(email) {
            if (confirm("Se enviara un mail para que el usuario resetee la contraseña")) {
                $.ajax({
                    type: 'POST',
                    url: '/admin/contact/reset/pass/email',
                    data: {
                        'email': email,
                    },
                    success: function (result) {
                        new Noty({
                            type: "success",
                            text: 'Contraseña reseteada',
                        }).show();
                    },
                    error: function (result) {
                        new Noty({
                            type: "error",
                            text: 'Algo salio mal',
                        }).show();
                    }
                });
            } else {
                new Noty({
                    type: "warning",
                    text: 'El mail no fue envíado',
                }).show();
            }

        }
    </script>


    @include('crud::fields.inc.wrapper_end')
@endif
