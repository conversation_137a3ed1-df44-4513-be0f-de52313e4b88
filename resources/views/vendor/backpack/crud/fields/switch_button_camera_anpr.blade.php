<div class="form-check form-switch" style="left: 16px; padding-bottom: 20px;margin-top: -10px;">
    <input name="{{$field['name']}}" onclick="setValCameraANPR({{$field['name']}})"
           value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? "0" }}"
           class="bool_fields_back form-check-input" type="checkbox" id="{{$field['name']}}">
    <label class="form-check-label" style="" for="{{$field['name']}}">{{$field['label']}}</label>
</div>


<style>
    @media(max-width: 1638px){
        .min-view{
            display: none!important;
        }
    }
</style>

<script>
    $(document).ready(() => {
        let pressedSwitchCameraANPR = $('#camera_anpr').val() ?? 0

        if(pressedSwitchCameraANPR == '1')
            $('#camera_anpr').click()

        $('#camera_anpr').on('change', () => {
            $('#camera_anpr').val(pressedSwitchCameraANPR == 1 ? 0 : 1)
            pressedSwitchCameraANPR = $('#camera_anpr').val()
        })
    })
</script>
