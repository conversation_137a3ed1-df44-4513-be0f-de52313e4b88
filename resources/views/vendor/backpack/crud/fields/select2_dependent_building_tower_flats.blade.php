@php
    $buildings = \App\Models\Building::all();
if($crud->entry){
    $flats= $crud->entry;
    $building_name=\App\Models\Building::find($flats->building_id);

}

@endphp

<label>{!! $field['label'] !!}</label>
@if($crud->entry)
    <select id="selectBuilding" class="select2-custom col-md-3" onclick="loadFlats(this.value)" name="building_id"
            disabled>
        <option value="{{$flats->building_id}}">{{$building_name->name}}</option>
    </select>
@else
    <select id="selectBuilding" class="select2-custom col-md-3" onchange="loadFlats(this.value)" name="building_id">
        @foreach($buildings as $b)
            <option value="{{$b->id}}">{{$b->name}}</option>
        @endforeach
    </select>
@endif
<br>
<br>
<br>
<label id="labelTower" style="display: none">Denominación de la torre</label>
<label id="labelWithoutTower">No tiene torre </label>
<select id="selectFlat" class="select2-custom col-md-6" name="tower_id" onchange="selectT()">
</select>

<br>
<br>
<br>

<input name="tower_id" id="towerSelected" @if($crud->entry) value="{{$flats->tower_id}}" @endif hidden>
@push('after_scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>

    <script>

        function loadFlats(idBuilding) {
            $('#selectFlat').empty();
            $.ajax({
                url: '/api/building-flats/' + idBuilding,
                dataType: 'json',
                type: 'GET',
                success: function (data) {
                    if (data != '') {
                        for (let i in data) {
                            $("#selectFlat").append("<option value='" + data[i].id + "'>" + data[i].tower_denomination + "</option>");
                        }
                        let tower_id = $("#towerSelected").val();
                        if (tower_id != '') {
                            selectTowerId()
                        } else {
                            selectT()
                        }
                        showInputTower()
                    } else {
                        hideInputTower()
                    }
                }
            })
        };

        function selectTowerId() {
            let tower_id = $("#towerSelected").val();
            $("#selectFlat").val(tower_id).change();
        }

        function selectT() {
            let id = $("#selectFlat").val();
            $("#towerSelected").val(id).change()
        }

        function hideInputTower() {
            $('#labelTower').hide()
            $('#labelWithoutTower').show()
            $('#towerSelected').val('').change()
            $('#selectFlat').prop("disabled", true);
            let newOption = new Option('No tiene torre', false);
            $('#selectTower').append(newOption).trigger('change');

        }

        function showInputTower() {
            $('#labelTower').show()
            $('#labelWithoutTower').hide()
            $('#selectFlat').prop("disabled", false);
        }

        $(document).ready(function () {
            let building = $("#selectBuilding").val();

            loadFlats(building);

        });


    </script>
@endpush
