@php
    $currentStateBuilding = $currentStateBuilding ?? null;
    $installationDate = $building->installation_date ?? $entry->installation_date ?? null;
    $agreedStartDate = $building->agreed_start_date ?? $entry->agreed_start_date ?? null;
    $agreedStartTime = $building->agreed_start_time ?? $entry->agreed_start_time ?? '15:00';
    $bannerDate = false;
    $bannerTime = false;
    $textBanner = '';

    if ($agreedStartDate){
        $bannerDate = \Carbon\Carbon::parse($agreedStartDate)->format('d/m/Y -');
        $bannerTime = \Carbon\Carbon::parse($agreedStartTime)->format('H:i') ;
        $textBanner = 'Fecha de incio acordada:';
    } else if($installationDate){
        $bannerDate = \Carbon\Carbon::parse($installationDate)->addDays(30)->setHour(15)->setMinute(0)->format('d/m/Y - H:i');
        $textBanner = 'Fecha de incio estimada:';
    }

    if (!$currentStateBuilding) {
        $statesItems = \App\Models\Building::BUILDING_STATE;
        if(isset($entry))
            $currentStateBuilding = $statesItems[$entry->building_state] ?? '';
    }
@endphp
@if($bannerDate)
    @if(!empty($currentStateBuilding) && $currentStateBuilding['text'] != 'Activo' && $currentStateBuilding['text'] != 'Inactivo')
        <div class="estimated-start-date-building" style="width: 70%; background-color: #FF9F43; {{$field['attributes']['style']??''}}">
            <ion-icon name="today-outline" style="padding-bottom: 2px;"></ion-icon>
            <span class="open-sans">
                <b class="buidling-state-span">{{$textBanner}}</b>
                <span class="buidling-state-span">{{$bannerDate}} {{$bannerTime}}</span>
            </span>
        </div>
    @endif
@endif
