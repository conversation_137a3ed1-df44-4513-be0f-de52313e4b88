<div>
    <div class="cases-related-headder">
        <h1 class="titleTablesCreate font-ibm"
            style="font-size: 18px">
            Casos relacionados
        </h1>

        <a href="#"
           data-toggle="modal"
           data-target="#exampleModalChildCase"
           class=""
           onclick="putPlaceholderToCases()">
            + Relacionar caso
        </a>
    </div>

    <div class="cases-table-header ">
        <div>
            <a id="filter-button-1" wire:click="handleAddFilter(null)"
               class="  btn btn-link waves-effect waves-float waves-light  @if ($state == '') filter-button-selected @endif "
               style="margin-left: -15px;"><i style="margin-right: 5px;" class="la la-search"></i>Todos</a>
            <a id="filter-button-2" wire:click="handleAddFilter('pendiente')"
               class="btn btn-link  waves-effect waves-float waves-light color-red-cases  @if ($state == 'pendiente') filter-button-selected @endif">Pendiente</a>
            <a id="filter-button-3" wire:click="handleAddFilter('en_curso')"
               class="btn btn-link  waves-effect waves-float waves-light color-blue-cases @if ($state == 'en_curso') filter-button-selected @endif ">En
                curso</a>
            <a id="filter-button-4" wire:click="handleAddFilter('solicitar_info')"
               class="btn btn-link  waves-effect waves-float waves-light color-orange-cases @if ($state == 'solicitar_info') filter-button-selected @endif">Solicitar
                Info</a>
            <a id="filter-button-5" wire:click="handleAddFilter('aviso_al_cliente')"
               class="btn btn-link  waves-effect waves-float waves-light color-green-cases  @if ($state == 'aviso_al_cliente') filter-button-selected @endif">Aviso
                al cliente</a>
            <a id="filter-button-6" wire:click="handleAddFilter('finalizado')"
               class="btn btn-link waves-effect waves-float waves-light color-grey-cases @if ($state == 'finalizado') filter-button-selected @endif">Finalizado</a>
        </div>

        <div class="col-md-3"
             style="padding-left: 3%;">
            <input wire:model.debounce.500ms="search"
                   class="form-control"
                   id="search_cases"
                   type="text"
                   style="border-radius: 6px; border: solid 1px #bdbdbd; height: 32px; width: 100%; position: relative"
                   placeholder="Buscar Casos"
            >
        </div>
    </div>
    <div class="scroll-content"
         style="overflow: auto; max-height: 550px;">
        <table id="cases-child-table" class="table-striped table">
            <thead>
            <tr>
                <th style="" scope="col" class="sorting recolor-service" onclick="sortServicesTable(0)">
                    #
                    <i class="la la-sort"></i>
                </th>
                <th style="" scope="col" class="sorting recolor-service" onclick="sortServicesTable(1)">
                    Categoria
                    <i class="la la-sort"></i>
                </th>
                <th id="child-cases-description-th" scope="col" class="sorting recolor-service"
                    onclick="sortServicesTable(2)">
                    Titulo
                    <i class="la la-sort"></i>
                </th>
                <th style="" scope="col" class="sorting recolor-service" onclick="sortServicesTable(3)">
                    Fecha
                    <i class="la la-sort"></i>
                </th>
                <th style="" scope="col" class="sorting recolor-service" onclick="sortServicesTable(4)">
                    Estado
                    <i class="la la-sort"></i>
                </th>
                <th style="" scope="col" class="sorting recolor-service">
                    Eliminar<i class="la la-sort"></i>
                </th>
                <th style="" scope="col" class="sorting recolor-service" onclick="sortServicesTable(6)">
                    Relac./Depend.
                    <i class="la la-sort"></i>
                </th>
            </tr>
            </thead>
            <tbody>
            @foreach ($cases as $case)
                <tr>
                    <td>
                        <a href="/admin/case/{{ $case->id }}/info" target="_blank"
                           class="btn-link">{{ $case->id }}
                        </a>
                    </td>
                    <td>
                        {{ Str::limit($case->last_category_name, 10) }}
                    </td>

                    <td>

                        {{ Str::limit($case->title, 60) }}

                    </td>
                    <td>
                        {{ Str::limit($case->created_at, 10, '') }}
                    </td>
                    <td>
                        <b style="border-radius: 5px;"
                           class="column-state
                            @switch($case->state)
                                @case('pendiente')
                                    btn-danger
                                    @break
                                @case('en_curso')
                                    btn-primary
                                    @break
                                @case('solicitar_info')
                                btn-warning
                                    @break

                                @case('aviso_al_cliente')
                                btn-success
                                    @break
                                @case('finalizado')
                                btn-secondary
                                    @break

                                @case('CERRADO')
                                btn-secondary
                                    @break
                                @case('default')
                                btn-primary
                                    @break
                                @case('ABIERTO')
                                btn-danger
                                    @break
                                @case('REABIERTO')
                                btn-danger
                                    @break

                                @default

                            @endswitch  ">
                            @switch($case->state)
                                @case('pendiente')
                                    Pendiente
                                    @break

                                @case('en_curso')
                                    En curso
                                    @break

                                @case('solicitar_info')
                                    Solicitar info
                                    @break

                                @case('aviso_al_cliente')
                                    Aviso al cliente
                                    @break

                                @case('finalizado')
                                    Finalizado
                                    @break

                                @case('CERRADO')
                                    Cerrado
                                    @break

                                @case('default')
                                    Default
                                    @break

                                @case('ABIERTO')
                                    Pendiente
                                    @break

                                @case('REABIERTO')
                                    Pendiente
                                    @break

                                @default
                            @endswitch
                        </b>
                    </td>
                    <td>
                            <span wire:ignore wire:click="deleteCaseRelated({{ $case->id }})" style="cursor: pointer">
                                <ion-icon name="trash-outline"></ion-icon>
                            </span>
                    </td>
                    <td>
                            <span>
                                {{ $case->parent_case_id === $currentCase->parent_case_id ? "Relacionado" : "Dependiente"  }}
                            </span>
                    </td>
                </tr>
            @endforeach

            </tbody>
        </table>
    </div>

    @if ($cases instanceof Illuminate\Pagination\LengthAwarePaginator)
        {{ $cases->links('vendor.livewire.bootstrap') }}
    @endif

    <div wire:ignore.self class="modal fade" id="exampleModalChildCase" tabindex="-1" role="dialog"
         aria-labelledby="exampleModalChildCase" aria-hidden="true" style="z-index: 99999999999999999;">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="width: 500px;">
                <div class="modal-header"
                     style="background: linear-gradient(90deg, rgba(32, 49, 111, 1) 0%, rgba(32, 49, 111, 1) 0%, rgba(82, 106, 192, 1) 100%) !important; color: white;">
                    <h5 class="modal-title" id="exampleModalLabel" style="color: white;">Relacionar caso</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span style="color: white;" aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body row">
                    <div class="special_color_of_select2_border form-group col-md-12 m-auto"
                         style="padding-inline: 15px;">
                        <div class="text-right">
                            <a style="padding-right: 0px;" href="#" class="btn-link btn"
                               data-dismiss="modal" data-toggle="modal"
                               onclick="loadSelectRelatedChild()" data-target="#exampleModalChildCaseCreate">+ Nuevo
                                caso</a>
                        </div>
                        <div style="padding-left: 3%;">
                            <input wire:model.debounce.50ms="searchCase" class="form-control" type="text"
                                   id="searchCaseInput"
                                   style="border-radius: 6px; border: solid 1px #bdbdbd; height: 32px; width: 100%; position: relative"
                                   placeholder="Buscar Casos">

                        </div>
                        <div class="p-1">
                            @forelse ($casesSearched ?? [] as $caseOnSearch)
                                @if ($caseOnSearch->description)
                                    @if($caseOnSearch->id != $currentCase->id)
                                        <div class="d-flex  align-items-center">
                                            <p class="mr-auto ">
                                                {{ $caseOnSearch->id . ' - ' . Str::limit($caseOnSearch->description, 40) }}

                                            </p>
                                            <button style="border-radius: 24px;width: auto;"
                                                    wire:click="addCaseChieldToParent({{ $caseOnSearch->id }})"
                                                    type="button"
                                                    class="btn create_edit_back_color col-md-8 my-1"
                                                    data-dismiss="modal"><i
                                                    class="la la-save"></i>Asignar

                                            </button>
                                        </div>
                                    @endif
                                @endif
                            @empty
                            @endforelse
                        </div>

                    </div>
                </div>
                <div class="row" style="height: 70px; padding-top: 20px;">
                    <div class="col-md-6 text-center">
                        <button style="border-radius: 24px;" type="button"
                                class="btn create_edit_back_color col-md-8"><i class="la la-save"></i>Guardar
                        </button>
                    </div>
                    <div class="col-md-6 text-center">
                        <button onclick="clearAllPopUpFieldsChild2()"
                                style="background-color: rgba(207,207,207,.62); border-radius: 24px;" type="button"
                                class="btn waves-effect waves-float waves-light col-md-8" data-dismiss="modal"><i
                                class="la la-ban"></i>Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="exampleModalChildCaseCreate" role="dialog"
         aria-labelledby="exampleModalChildCaseCreate" aria-hidden="true" style="z-index: 99999999999999999;">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="width: 500px">
                <div class="modal-header"
                     style="background: linear-gradient(90deg, rgba(32, 49, 111, 1) 0%, rgba(32, 49, 111, 1) 0%, rgba(82, 106, 192, 1) 100%) !important; color: white;">
                    <h5 class="modal-title" id="exampleModalLabel" style="color: white;">Crear caso relacionado</h5>
                    <button id="closeModalChield" type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span style="color: white;" aria-hidden="true">&times;</span>
                    </button>
                </div>
                @include('show.case_create_fields')

                <div class="modal-body row">
                    <div class="special_color_of_select2_border form-group col-md-12 m-auto"
                         style="padding-inline: 15px;">
                        <div class="card">
                            <div class="p-1 first_column_of_create_and_edit row col-md-12"
                                 style="margin: 0px; margin-left: 0px;">
                                <div class="cases-comments-table-header">
                                    <div>
                                        <ion-icon style="zoom: 1.7" name="chatbubble-outline" wire:ignore></ion-icon>
                                        <label>Comentarios</label>
                                    </div>
                                    <a href="/">Ver historial</a>
                                </div>
                                <div class="form-group col-md-12 row"
                                     data-init-function="bpFieldInitUploadMultipleElement" data-field-name="images"
                                     element="div" data-initialized="true">
                                    <div class="form-group col-md-12" element="div">
                                        <textarea placeholder="Agregue aquí el comentario" style="width: inherit;"
                                                  class="input-dark form-control" id="comment_comment"></textarea>
                                    </div>
                                    <div class="form-group col-md-12" element="div">
                                        <select id="comment_category"
                                                class="select2-custom select2 select-dark form-control">
                                            <option value=null>Etiquetar comentario</option>
                                            @foreach (\App\Models\Category::where('comment_check', 1)->get() as $category)
                                                <option value="{{ $category->name }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row" style="height: 70px; padding-top: 20px;">
                    <div class="col-md-6 text-center">
                        <button style="border-radius: 24px;" onclick="createNewCaseChild()" type="button"
                                class="btn create_edit_back_color col-md-8"><i class="la la-save"></i>Guardar
                        </button>
                    </div>
                    <div class="col-md-6 text-center">
                        <button onclick="clearAllPopUpFieldsChild2()"
                                style="background-color: rgba(207,207,207,.62); border-radius: 24px;" type="button"
                                class="btn waves-effect waves-float waves-light col-md-8" data-dismiss="modal"><i
                                class="la la-ban"></i>Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('after_scripts')
    <script>

        case_father_id = <?php echo $currentCase->id ?? '0'; ?>;

        function sortServicesTable(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.getElementById("cases-child-table");
            switching = true;
            dir = "asc";
            while (switching) {
                switching = false;
                rows = table.rows;
                for (i = 1; i < (rows.length - 1); i++) {
                    shouldSwitch = false;
                    x = rows[i].getElementsByTagName("TD")[n];
                    y = rows[i + 1].getElementsByTagName("TD")[n];
                    if (dir == "asc") {
                        if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
                if (shouldSwitch) {
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    switchcount++;
                } else {
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
        }

        function createNewCaseChild() {
            let last_category_id = $("#select1_child").val();
            let aux = 0;
            if ($("#select3_child").val() != null) {
                last_category_id = $("#select3_child").val();
            } else if ($("#select2_child").val() != null) {
                last_category_id = $("#select2_child").val();
            }

            if (last_category_id == '' || last_category_id == null) {
                document.getElementById('errorCategory').style.display = 'flex';
                aux = 1;
            }
            if ($('#title_child').val() == '') {
                document.getElementById('errorTitulo').style.display = 'flex';
                aux = 1;
            }

            if ($('#case-relation-building').val() == null) {
                document.getElementById('errorBuilding').style.display = 'flex';
                aux = 1;
            }


            if ($('#case-relation-flat').val() == null) {
                document.getElementById('errorFlat').style.display = 'flex';
                aux = 1;
            }

            if (aux) return;


            let radio_priority_val = 'DEFECTO';

            if ($("#radio_alta").is(':checked')) {
                radio_priority_val = $("#radio_alta").val();
            } else if ($("#radio_media").is(':checked')) {
                radio_priority_val = $("#radio_media").val();
            } else if ($("#radio_baja").is(':checked')) {
                radio_priority_val = $("#radio_baja").val();
            }

            let building_id = $('#case-relation-building').val();
            let flat_id = $('#case-relation-flat').val();
            let user_id = $('#case-relation-contact').val();


            if (case_father_id == 0) {
                new Noty({
                    type: "error",
                    text: 'ERROR - No se puede asignar como padre un caso que no se guardo',
                }).show();
            } else {
                let caseData = new FormData();
                caseData.append('state', $('#state-value_child').val() ?? 'en_curso');
                caseData.append('priority', radio_priority_val);
                caseData.append('limited_date', $('#limited_date_child').val());
                caseData.append('warnings_days', $('#warnings_days_related').val());
                caseData.append('description', $('#description_child').val());
                caseData.append('responsable', $('#responsablec_child').val());
                caseData.append('comment', $('#comment_comment').val());
                caseData.append('comment_category', $('#comment_category').val());
                caseData.append('last_category_id', last_category_id);
                caseData.append('parent_case_id', case_father_id);
                caseData.append('title', $('#title_child').val());
                caseData.append('access_category_id', $('#select-case-access-child').val());
                caseData.append('building_id', building_id);
                caseData.append('flat_id', flat_id);
                caseData.append('user_id', user_id);
                caseData.append('mail', $('#text_email_relation').val());
                caseData.append('phone_number', $('#text_phone_number_relation').val());
                let filesLength = 0;

                jQuery.each(jQuery('#file-related')[0].files, function (i, file) {
                    caseData.append('file_' + i, file);
                    filesLength++;
                });
                caseData.append('file_length', filesLength);


                $.ajax({
                    type: 'POST',
                    url: '/admin/case/child',
                    data: caseData,
                    processData: false,
                    contentType: false,
                    success: function (result) {

                        //    $('#exampleModalChildCase').modal('hide')
                        $(this).hide();
                        //  let modalBackdrop = document.querySelector(".modal-backdrop");
                        //  modalBackdrop.style.zIndex = "-1";
                        // modalBackdrop.style.zIndex = "-1";
                        let modalBackdrop = document.querySelector(".modal-backdrop");
                        if (modalBackdrop) {
                            modalBackdrop.remove();
                        }

                        window.livewire.emit('newCaseRelated')
                        new Noty({
                            type: "success",
                            text: '¡El caso hijo fue creado con exito!',
                        }).show();
                        clearAllPopUpFieldsChild2();

                        $('#closeModalChield').click();

                        window.livewire.emit('loadCases')
                    },
                    error: function (result) {
                        new Noty({
                            type: "error",
                            text: 'ERROR - No se puede asignar como padre un caso que no se guardo',
                        }).show();
                    }
                });
            }
        }

        function clearAllPopUpFieldsChild2() {
            $('#title_child').val('');
            $('#state-value_child').val('en_curso');
            $('#comment_comment').val('');
            $('#comment_category').val('');
            $('#service_id_child').val('');
            $('#limited_date_child').val('');
            $('#warnings_days_child').val('');
            $('#description_child').val('');
            $('#images_child').val('');
            $('#responsablec_child').val('');
            $('#select1_child').val('3').change();
        }

        function loadSelectRelatedChild() {

            $('#responsablec_child').select2({
                allowClear: true,
                placeholder: "Seleccionar una persona",
                dropdownParent: $('#exampleModalChildCaseCreate'),

            });
            $('#select-case-access-child').select2({
                allowClear: true,
                placeholder: "Seleccionar una persona",
                dropdownParent: $('#exampleModalChildCaseCreate'),
            });

            $('#area_view_child').select2({
                allowClear: true,
                placeholder: "Seleccione un Area",
                dropdownParent: $('#exampleModalChildCaseCreate'),

            });
            $('#select1_child').select2({
                allowClear: true,
                placeholder: "Seleccione un Categoria",
                dropdownParent: $('#exampleModalChildCaseCreate'),

            });
            $('#select1_child').val('').trigger('change');


            $("#case-relation-building").select2({
                allowClear: true,
                placeholder: "Selecciona un edificio",
                dropdownParent: $('#exampleModalChildCaseCreate'),
            });

            $('#case-relation-building').val()

            $('#case-relation-flat').select2({
                allowClear: true,
                placeholder: "Seleccionar un apartamento",
                dropdownParent: $('#exampleModalChildCaseCreate'),
            });
            loadSelect2CaseAllBuildingRelated(getAllBuildings());

            loadContactCaseRelation()

        }

        function putPlaceholderToCases() {
            $('#selectionCases').select2({
                placeholder: "Buscar",
            });

            window.livewire.on('select2', () => {
                $('#selectionCases').select2();
            });
        }

        $('#case-relation-building').select2({
            dropdownParent: $('#exampleModalChildCaseCreate'),
            dropdownPosition: 'below',
        }).on('select2:open', function (e) {
            let select = $(this).data('select2');
            let selectDropdown = $(select.$dropdown);
            let selectOffset = $(select.$container).offset();
            let spaceBelow = $(window).height() - (selectOffset.top + select.$container.outerHeight());
            let spaceAbove = selectOffset.top;

            if (spaceBelow < selectDropdown.outerHeight() && spaceAbove > selectDropdown.outerHeight()) {
                selectDropdown.addClass('select2-dropdown-above').removeClass('select2-dropdown-below');
            } else {
                selectDropdown.addClass('select2-dropdown-below').removeClass('select2-dropdown-above');
            }
        });

        $(document).on('select2:open', function() {
            setTimeout(() => {
                document
                    .querySelector('.select2-container--open .select2-search__field')
                    ?.focus();
            }, 0);
        });

    </script>
@endpush

@push('after_styles')
    <style>
        .add-child-case-button {
            margin: 0px !important;
            width: 100%;
        }

        .modal-backdrop.fade.show {
            background-color: #000000;
            z-index: 999999999;
        }

        .select2-dropdown.select2-dropdown--below {
            z-index: 9999999999;
        }

        .special_color_of_select2_border .select2-container .selection .select2-selection {
            border-color: #d8d6de !important;
        }

        .select2-container--open {
            z-index: 99999999999;
        }
    </style>
@endpush
