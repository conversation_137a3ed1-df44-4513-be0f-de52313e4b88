<div id="first-middle-table" class="col-md-12" style="padding: 0px;">
    <div class="container-fluid row " style="z-index: 11; width: 100%;--bs-gutter-x: 0;">
        <div class="header-table-contact background-color-mode" style="border-radius: 0px; padding: 18px;">
            <h4 class="text-capitalize titleTables font-weight-bold" style="margin-top: 10px;">Casos</h4>
            <div onclick="createNewCase()" class="new-case-building-show" id="building-case-btn-save">
                Nuevo Caso
            </div>
            <div style="margin-left: 0px" class="custom-dropdown-container">
                <button class="custom-dropdown-btn">
                    <ion-icon name="chevron-down-outline" style="color:var(--ColorDarkBlue); margin-top: 8px;"
                              wire:ignore></ion-icon>
                </button>
                <div class="custom-dropdown-content">
                    @foreach(\App\Models\Category::where('quick_case_building',1)->get() as $category)
                        <a href="/admin/case/create?building_id={{$building->id}}&category_id={{$category->id}}">{{\App\Services\CategoryService::getCategoryHierarchy($category->id)}}</a>
                    @endforeach
                </div>
            </div>
            <div class="search-container">
                 <span id="clearSearchCaseTable"
                       wire:click="clearSearchCaseTable"
                       class="clear-icon cursor-pointer
                       @if(!$delateIconSearch) d-none @endif"
                 >
                    &times;
                 </span>
                <input id="search_cases" type="text" placeholder="Buscar" class="search-input"
                       wire:model.live.debounce.500ms="search">
                <div class="shortcuts-container">
                    <div id="shortcuts-case" class="shortcuts @if($search) d-none @endif">
                        <span>Ctrl</span> + <span>2</span>
                    </div>
                    <div class="search-button">
                        <ion-icon name="search-outline" wire:ignore></ion-icon>
                    </div>
                </div>
            </div>
            <div id="cases-filter-edificio-all" class="d-flex flex-column ml-auto align-items-center">
                @if($existsEdificioApto)
                    <div class="d-flex ml-auto @if($edificioFilter) filter-building @endif"
                         style="cursor: pointer; gap: 5px;" wire:click="getCasesEdificio">
                        <ion-icon style="vertical-align: middle; pointer-events: none; margin-top: 2px;"
                                  title="Edificios" name="business-outline"
                                  role="img" class="md hydrated @if($edificioFilter) filter-building @endif"
                                  aria-label="create outline"></ion-icon>
                        Edificio
                        @if($edificioFilter)
                            <div class="filter-building" style="cursor: pointer; margin-left: 5px;">
                                <ion-icon wire:click="getCasesEdificio" name="close-outline" style="margin-top: 3px;"
                                          class="filter-building font-weight-bold"></ion-icon>
                            </div>
                        @endif
                    </div>
                @endif
                <div id="open-cases" class="btn-casos cursor-pointer">
                    <ion-icon style="vertical-align: middle;pointer-events:none" title="Ver casos" name="open-outline"
                              role="img" class="md hydrated btn-casos" aria-label="create outline"></ion-icon>
                    <span>Ver todos</span>
                </div>
                <a id="cases-to-open" title="Ver casos"
                   href='/admin/case?building=%5B"{{ $building->building_number }}"%5D'
                   target="_blank"
                   class="btn-casos d-none" style="position: absolute;right: 24px;">
                    <ion-icon style="vertical-align: middle;pointer-events:none" title="Ver casos" name="open-outline"
                              role="img" class="md hydrated btn-casos" aria-label="create outline"></ion-icon>
                    Ver todos
                </a>
            </div>
        </div>
    </div>
    <div class="background-color-mode-building-tables col-md-12 ">
        <div class="row tupakl scroll-content" id="building-cases-table-scroll"
             style="max-height: 25vh; overflow-y: auto; overflow-x: hidden; font-size: 14px;">
            @include('.vendor.backpack.widgets.tables.building-show-cases-table')
        </div>
    </div>
</div>

@push('after_scripts')
    <script>
        const casesActions = document.querySelectorAll('.action-case');

        let $building = @js($building);
        let $buildingId = $building.id, $buildingNumber = $building.building_number;

        document.addEventListener('updateCasesLinks', (building) => {
            $buildingId = building.detail.id
            $buildingNumber = building.detail.building_number
        })

        window.addEventListener("keydown", function (e) {
            if ((e.ctrlKey && e.key === '2')) {
                e.preventDefault();
                $('#search_cases').trigger('focus');
            }
        })

        $('#search_cases').on('input', (e) => {
            if (e.target.value.trim().length != 0) $('#shortcuts-case').addClass('d-none')
            else $('#shortcuts-case').removeClass('d-none')
        })

        function createNewCase() {
            window.open(window.origin + `/admin/case/create?building_id=${$buildingId}`, '_blank', 'noopener,noreferrer')
        }

        function showCase(caseId, edit = false) {
            const link = document.createElement('a')
            link.href = window.origin + `/admin/case/${caseId}/${edit ? 'edit' : 'info'}`
            link.target = "_blank"
            link.rel = 'noopener noreferrer'
            link.click()
            window.focus()
        }

        $('#open-cases').on('click', () => {
            window.open(`/admin/case?building=%5B"${$buildingNumber}"%5D`)
        })


        $(document).on('mousedown', '#building-case-btn-save', (e) => {
            if (e.which == 2) {
                const url = window.location.pathname.split('/')
                if (url.includes('building')) {
                    const buildingId = url[url.length - 2]
                    createNewCase(buildingId)
                }
            }
        })

        document.addEventListener('scrollCasesContainer', () => {
            document.querySelector("#building-cases-table-scroll").scrollTop = 0
        })

        $('#clearSearchCaseTable').on('click', () => {
            Livewire.emit('clearSearchCaseTable')
        })

    </script>
@endpush
