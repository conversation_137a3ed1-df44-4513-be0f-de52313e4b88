<div id="cont-building-contact">
    @include('buildings.show.components.modals.extra_phone')
    @include('buildings.show.components.modals.announcements')
    <div>
        @include('buildings.show.components.tables.components.contact-header')
        <div class="table-wrapper building-contact-table scroll-content" id="building-contact-table">
            <table class="table table-hover compact nowrap table-full-width dataTable background-color-mode">
                <thead>
                @include('buildings.show.components.tables.components.contact-table-header')
                </thead>
                <tbody id="loader-tbody" class="d-none">
                @include('buildings.show.components.tables.components.contact-loader')
                </tbody>
                <tbody id="data-tbody">

                @forelse($contacts as $contact)
                    @include('buildings.show.components.tables.components.contact-row', ['contact' => $contact])
                @empty
                    @include('buildings.show.components.tables.components.contact-empty')
                @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@push('after_scripts')
    <script>

        document.addEventListener('DOMContentLoaded', function () {
            const tableDiv = document.querySelector('#building-contact-table');
            let isLoading = false;
            let noMoreData = false;

            if (tableDiv) {
                const checkScroll = () => {
                    if (isLoading || noMoreData) return;

                    const scrollPosition = tableDiv.scrollTop + tableDiv.clientHeight;
                    const scrollThreshold = tableDiv.scrollHeight - 100;

                    if (scrollPosition >= scrollThreshold) {
                        isLoading = true;
                        window.livewire.emit('handleLoadNextPage');
                    }
                };

                let scrollTimeout;
                tableDiv.addEventListener('scroll', () => {
                    if (scrollTimeout) return;

                    scrollTimeout = setTimeout(() => {
                        checkScroll();
                        scrollTimeout = null;
                    }, 100);
                });

                setTimeout(checkScroll, 500);

                window.addEventListener('noMoreData', () => {
                    noMoreData = true;
                });

                window.addEventListener('resetNoMoreData', () => {
                    noMoreData = false;
                    isLoading = false;
                });

                window.addEventListener('scrollToTop', () => {
                    tableDiv.scrollTop = 0;
                });

                window.livewire.hook('message.processed', () => {
                    isLoading = false;
                    checkScroll();
                });
            }
        });


        $(document).ready(function () {
            $('#searchContactByFlatInput').select2();

            $('#searchContactByFlatInput').on('select2:open', function () {
                document.querySelector('.select2-search__field').focus();
            });

            $('#searchContactByFlatInput').on('select2:unselecting', function(e) {
                $(this).data('unselecting', true);
            });

            $('#searchContactByFlatInput').on('select2:opening', function(e) {
                if ($(this).data('unselecting')) {
                    $(this).removeData('unselecting');
                    e.preventDefault();
                }
            });
        });

        document.addEventListener('scrollToTop', () => {
            document.querySelector("#building-contact-table").scrollTop = 0
        })



        const showModals = () => {
            const plusIcon = document.querySelectorAll('.icon-container');
            const phoneModal = document.getElementById('phoneModal');
            const announcementsModal = document.getElementById('announcementsModal');
            let timeOutId;

            const hideModalsTimeOut = () => {
                timeOutId = setTimeout(() => {
                    phoneModal.style.display = 'none';
                    announcementsModal.style.display = 'none';
                }, 4000);
            }

            plusIcon.forEach(icon => {
                icon.addEventListener('mouseover', (e) => {
                    const menuCollapsed = document.body.classList.contains('menu-collapsed')
                    const rect = icon.getBoundingClientRect();
                    const offsetTop = rect.top + rect.height + window.scrollY + 2;
                    const offsetLeft = menuCollapsed ? rect.left + window.scrollX - 50 : rect.left + window.scrollX - 230;

                    if (e.target.closest('.icon-container').dataset.announcements !== undefined) {

                        const announcements = e.target.closest('.icon-container').dataset.announcements;

                        let announcementsArray;

                        try {
                            announcementsArray = JSON.parse(announcements);
                        } catch (e) {
                            announcementsArray = [{title: 'Announcement', description: announcements}];
                        }

                        const formattedAnnouncements = announcementsArray

                            .map(announcement => {
                                const title = announcement.title ? `<h5 class="contact-announcement-title" style="font-weight: bold; font-size: 16px">${announcement.title}</h5>` : '';
                                const description = announcement.description.replace(/\n/g, '<br>');
                                return `${title}  ${description}`;
                            }).join('<hr class="hover-separator" />');

                        announcementsModal.style.left = `${offsetLeft}px`;
                        announcementsModal.style.top = `${offsetTop}px`;
                        announcementsModal.style.display = 'block';

                        document.getElementById('announcementsDetails').innerHTML = formattedAnnouncements;

                        const viewportHeight = window.innerHeight;
                        const rectGestionesModal = announcementsModal.getBoundingClientRect()
                        if (rectGestionesModal.top + announcementsModal.offsetHeight > viewportHeight) {
                            announcementsModal.style.top = `${rectGestionesModal.top - announcementsModal.offsetHeight}px`;
                        }
                        if (rectGestionesModal.top < announcementsModal.offsetHeight) {
                            announcementsModal.style.top = '10px';
                        }
                    }
                })

                icon.addEventListener('mouseout', function () {
                    if(timeOutId) {
                        clearTimeout(timeOutId)
                        announcementsModal.style.display = 'none';
                    }
                    announcementsModal.style.display = 'none';
                });

                icon.addEventListener('click', function (event) {
                    const menuCollapsed = document.body.classList.contains('menu-collapsed')
                    const rect = icon.getBoundingClientRect();
                    const offsetTop = rect.top + rect.height + window.scrollY + 2;
                    const offsetLeft = menuCollapsed ? rect.left + window.scrollX - 80 : rect.left + window.scrollX - 250;

                    if (event.target.closest('.icon-container').dataset.announcements !== undefined) {

                        const announcements = event.target.closest('.icon-container').dataset.announcements;

                        let announcementsArray;

                        try {
                            announcementsArray = JSON.parse(announcements);
                        } catch (e) {
                            announcementsArray = [{title: 'Announcement', description: announcements}];
                        }

                        const formattedAnnouncements = announcementsArray

                            .map(announcement => {
                                const title = announcement.title ? `<h5 class="contact-announcement-title" style="font-weight: bold; font-size: 16px">${announcement.title}</h5>` : '';
                                const description = announcement.description.replace(/\n/g, '<br>');
                                return `${title}  ${description}`;
                            }).join('<hr class="hover-separator" />');

                        announcementsModal.style.left = `${offsetLeft}px`;
                        announcementsModal.style.top = `${offsetTop}px`;
                        announcementsModal.style.display = 'block';

                        document.getElementById('announcementsDetails').innerHTML = formattedAnnouncements;

                        const viewportHeight = window.innerHeight;
                        const rectGestionesModal = announcementsModal.getBoundingClientRect()
                        if (rectGestionesModal.top + announcementsModal.offsetHeight > viewportHeight) {
                            announcementsModal.style.top = `${rectGestionesModal.top - announcementsModal.offsetHeight}px`;
                        }
                        if (rectGestionesModal.top < announcementsModal.offsetHeight) {
                            announcementsModal.style.top = '10px';
                        }
                    }

                    if (event.target.closest('.icon-container')?.dataset.phones !== undefined) {
                        const iconContainer = event.target.closest('.icon-container');
                        const phonesRaw = iconContainer.dataset.phones;
                        const contact = iconContainer.dataset.contact
                        let phones = [];

                        if(timeOutId) {
                            clearTimeout(timeOutId)
                            phoneModal.style.display = 'none';
                        }

                        try {
                            phones = phonesRaw.split('\n').filter(phone => phone.trim() !== '');
                        } catch (error) {
                            console.error('Error parsing phones:', error);
                        }

                        const id = iconContainer.dataset.id;

                        phoneModal.style.left = `${offsetLeft}px`;
                        phoneModal.style.top = `${offsetTop}px`;
                        phoneModal.style.display = 'block';

                        phoneModal.querySelector('#phoneDetails').innerHTML = '';

                        phones.forEach(phone => {
                            const phoneLink = document.createElement('a');
                            phoneLink.textContent = phone;
                            phoneLink.style.display = 'block';
                            phoneLink.onclick = () => interactionCall3CX(id, phone, contact)
                            phoneModal.querySelector('#phoneDetails').appendChild(phoneLink);
                        });
                    }

                    hideModalsTimeOut()

                });
            });
        }

        showModals()

        function interactionCall3CX(id, phone, contact) {
            if (typeof contact == 'object')
                contact = JSON.stringify(contact)
            Livewire.emit('showModalInteractionsOtherPhones', JSON.parse(contact), phone, true)
        }

        function openWittyBotPage(buildingId, flatId, contactId) {
            const url = `/admin/send-wittybot-sms?building_id=${buildingId}&flat_id=${flatId}&contact_id=${contactId}`;
            window.open(url, '_blank');
        }

        document.addEventListener('keydown', function (event) {

            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                document.getElementById('searchContactByCompleteNameInput').focus();
            }

            if (event.ctrlKey && event.key === 'g') {
                event.preventDefault();

                $('#searchContactByFlatInput').select2('open');

                setTimeout(function () {
                    $('.select2-search__field')[0].focus();
                }, 50);

            }
        })

        $('#searchContactByFlatInput').select2().on('select2:open', function() {
            $('.select2-search__field').on('input', function(e) {
                if(e.target.value.trim().length == 0) $('#shortcuts-flats').removeClass('d-none')
                else $('#shortcuts-flats').addClass('d-none')
            });
        });

        $('#searchContactByFlatInput').on('change', function (event) {
            if (event.target.id === 'searchContactByFlatInput') {
                if(event.target.value.trim().length != 0) $('#shortcuts-flats').addClass('d-none')
                else $('#shortcuts-flats').removeClass('d-none')
                Livewire.emit('handleFlatSelectInput', event.target.value);
            }
        })

        function debounce(func, wait) {
            var timeout
            return function () {
                var context = this,
                    args = arguments

                var later = function later() {
                    timeout = null
                    func.apply(context, args)
                }

                clearTimeout(timeout)
                timeout = setTimeout(later, wait)
            }
        }

        $('#searchContactByCompleteNameInput').on('input', debounce(function (e) {
            const completeNameValue = e.target.value
            Livewire.emit('handleTextSearchInput', completeNameValue);
        }, 500))

        $('#searchContactByCompleteNameInput').on('input', function (e) {
            const completeNameValue = e.target.value
            if(completeNameValue.trim().length != 0) $('#shortcuts-input-name').addClass('d-none')
            else $('#shortcuts-input-name').removeClass('d-none')
        })

        $('#clearSearch').on('click', () => {
            $('#searchContactByCompleteNameInput').val('')
            const completeNameValue = $('#searchContactByCompleteNameInput').val()
            $('#shortcuts-input-name').removeClass('d-none')

            Livewire.emit('handleTextSearchInput', completeNameValue);
        })


        function initSelect2SearchFlat() {
            $('#searchContactByFlatInput').select2({
                placeholder: "Apto",
                allowClear: true
            });

        }

        document.addEventListener('livewire:update', function () {
            initSelect2SearchFlat();
            showModals()
        });
        document.addEventListener('livewire:load', function () {
            initSelect2SearchFlat();
            showModals()
        });

        window.addEventListener('alert', event => {
            const {type, message} = event.detail;
            new Noty({
                type: event.detail.type,
                text: event.detail.message,
                timeout: 1500,
                layout: 'topRight'
            }).show();
        });


    </script>
@endpush
@push('after_styles')
    <style>

        .hover-separator:last-child {
            display: none;
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            float: left !important;
            margin-right: 5px !important;
        }

        .icon-package {
            position: absolute;
            top: 12px;
            right: -5px;
            background-color: var(--ColorBlueTitle);
            color: white;
            font-size: 9px;
            font-weight: bold;
            height: 12px;
            border-radius: 50%;
            padding: 0;
            width: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-add-package {
            color: black !important;
            font-size: 20px !important;
        }

        .icon-wsp {
            color: #182b88;
            font-size: 20px !important;

        }


        ion-icon {
            pointer-events: none;
            visibility: visible !important;
        }


        .advertisements {
            padding: 4px;
            background: #FB9F43;
            border-radius: 50%;
            color: white;
            z-index: 9;
        }

        .icon-container {
            display: flex;
            align-items: center;
            gap: 4px;

            span {
                background: var(--ColorGreyIcon);
                border-radius: 50%;
                color: white;
                padding: 2px 4px;
                font-size: 12px;
                font-weight: 700;
            }
        }


        .header-table-contact {
            padding: 16px 24px;
            background-color: white;
            display: flex;
            gap: 0.7rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }


        .search-container {
            display: flex;
            align-items: center;
            border: 1px solid #ccc;
            border-radius: 50px;
            padding: 10px 12px;
            background-color: #fff;
            width: 230px;
            height: 40px;
            position: relative;

            .select2-container--classic.select2-container--open .select2-selection--single, .select2-container--default.select2-container--open .select2-selection--single {
                border: none;
                border-left: 1px solid #ccc;
                border-right: 1px solid #ccc;
                border-radius: 15px 18px 0px 0px;
                border-color: #ccc !important;
            }

            .select2-container--default .select2-selection--single {
                border-radius: 50px;
                background: #fff;
                border: transparent;
                width: -webkit-fill-available;
            }

        }


        .search-input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 14px;
            color: #333;
        }

        .shortcuts-container {
            display: flex;
            align-items: center;
            gap: 8px;
            position: absolute;
            right: 12px;
        }

        .search-input::placeholder {
            color: #aaa;
        }

        .shortcuts {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 12px;
            color: #555;
        }

        .shortcuts span {
            background: var(--Secundarios-Gris-claro-5);
            border-radius: 4px;
            padding: 4px 6px;
            color: #727D8A;
            font-feature-settings: 'liga' off, 'clig' off;
            font-size: 9px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;

        }

        .search-button {
            display: flex;
        }

        .search-button ion-icon {
            zoom: 1;
            color: black;

        }

        .btn-new-contact {
            display: flex;
            padding: 8px 16px;
            border-radius: 50px;
            background: var(--Primarios-Azul-oscuro-Foxsys);
            color: white;
            width: max-content;
            min-width: 141px;
            border: none;
        }


        .building-contact-table {


            .menu-active {
                color: #223A8F !important;

                ion-icon {
                    color: #223A8F !important;
                }
            }

            .menu {
                display: flex;
                align-items: center;
                gap: 16px;
                padding: 10px;
            }

            .menu-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                color: #333;
                position: relative;
            }

            .menu-item ion-icon {
                font-size: 1.2rem !important;
                color: #666;
            }

            table {
                font-weight: bold;
            }


        }

        .dark-layout {

            .col-descripcion *,
            .col-nombre * {
                color: white;
            }

            .menu-active {
                color: #97C4FF !important;

                ion-icon {
                    color: #97C4FF !important;
                }
            }

            .modal-header {
                background-color: #27272b !important;
                color: white !important;
                border: none;
            }

            .modal-body * {
                color: white;
            }

            .modal-body .text-danger {
                color: red !important;
            }

            .building-contact-table {

                .menu-item {
                    color: white;
                }
            }

            #building-contact-table table tbody#data-tbody tr:nth-child(odd) td {
                background-color: #27272B;
            }

            #building-contact-table table tbody#data-tbody tr:nth-child(even) td {
                background-color: #1C1C24;
            }

            .building-contact-table table tr.contact-temporal td {
                background-color: #21398f !important;
                color: white !important;
            }

            .btn-new-contact {
                color: #97c4ff !important;
                background: transparent;
                border: 1px solid #97c4ff;
            }

            .building-contact-table table tr {
                background-color: #1e1e1e;
            }

            .building-contact-table table tr:hover {
                background-color: dimgrey !important;
            }


            .building-contact-table .header-table-contact, .building-contact-table {
                background-color: #27272b;
            }

            .header-table-contact .search-container {
                border: 1px solid #3D3D42;
                background-color: #27272b !important;
            }

            .shortcuts span {
                background-color: #3D3D42;
                color: white;
            }

            .header-table-contact .search-container .select2-container {
                background-color: #27272b !important;
            }

            .header-table-contact .search-container .select2-container .select2-selection--single {
                background-color: #27272b !important;
                color: #ffffff !important;
                border: 1px solid #444444 !important;
            }

            .header-table-contact .search-container .select2-container .select2-selection--single .select2-selection__rendered {
                color: #ffffff !important;
            }

            .header-table-contact .search-container .select2-container .select2-selection--single .select2-selection__placeholder {
                color: #aaaaaa !important;
                background-color: transparent !important;

            }

            .header-table-contact .search-container .select2-dropdown {
                background-color: #27272b !important;
                border: 1px solid #444444 !important;
            }

            .header-table-contact .search-container .select2-container .select2-results__options {
                background-color: #27272b !important;
                color: #ffffff !important;
            }

            .header-table-contact .search-container .select2-container .select2-results__option--highlighted {
                background-color: #444444 !important;
                color: #ffffff !important;
            }

            .table-wrapper {
                border: none;
            }

            .icon-package {
                background-color: #97c4ff;
            }

            .icon-add-package {
                color: #97c4ff !important;
                font-size: 20px !important;
            }

            .icon-wsp {
                color: #97c4ff !important;
                font-size: 20px !important;
            }

            .header-table-contact {
                background-color: #27272b;
            }

            .col-apto, .col-doc {
                color: white !important;
            }

        }

        .avatar-sm {
            width: 40px;
            height: 40px;
            object-fit: cover;
        }

        .actions .btn {
            margin-right: 5px;
            border: none;
        }

        .actions .btn i {
            font-size: 1.2rem;
        }

        .col-apto {
            width: 7rem !important;
            min-width: 7rem !important;

            span > span {
                margin-right: 5px;
            }
        }

        .col-paquetes {
            width: 4.5rem !important;
            min-width: 4.5rem !important;
            text-align: center;
        }

        .col-nombre {
            width: 23rem !important;
            min-width: 23rem !important;
        }

        .col-nombre *,
        .col-descripcion * {
            color: #6e6b7b;
        }

        .col-apto,
        .col-doc {
            color: #6e6b7b;
        }

        .col-doc {
            width: 8rem !important;
            min-width: 8rem !important;
        }


        .col-descripcion {
            width: 15rem !important;
            min-width: 15rem !important;
        }

        .col-tipo {
            width: 4rem !important;
            min-width: 4rem !important;
            text-align: center;

            && ion-icon {
                font-size: 1.3rem !important;
            }

        }

        .col-telefono {
            width: 7rem !important;
            min-width: 7rem !important;
        }

        .col-acciones {
            width: 5.5rem !important;
            min-width: 5.5rem !important;
            text-align: center;
        }


        /********Table********/
        .building-contact-table {


            .table tr {
                height: auto;
                min-height: 20px;
            }


            .table tbody td {
                padding: 0.5rem 0.9rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: middle;
                max-width: 200px;
                min-width: 100px;
            }

            #building-contact-table table tbody#data-tbody tr:nth-child(odd) td {
                background-color: #FBFBFC ;
            }

            #building-contact-table table tbody#data-tbody tr:nth-child(even) td {
                background-color: white ;

            }

            table tr.contact-temporal td,
            table tr.contact-temporal td * {
                background-color: #21398f !important;
                color: white !important;

                & ion-icon.advertisements {
                    padding: 4px !important;
                    background: #FB9F43 !important;
                    border-radius: 50% !important;
                    color: white !important;
                    z-index: 9 !important;
                }
            }

            .table-wrapper {
                max-height: var(--table-wrapper-max-height, 42vh); /* Valor predeterminado */
                overflow-y: auto;
                position: relative;
                /*background-color: #f9f9f9;*/
                border: none;
                width: 100%;
            }

            .building-contact-table table {
                border-collapse: separate;
                width: 100%;
                border-spacing: 0 10px;
                table-layout: auto;
            }

            .table thead th {
                position: sticky;
                top: 0;
                background-color: #f4f4f4;
                border-bottom: 2px solid #ccc;
                z-index: 10;
                text-align: left;
                color: #333;
                font-weight: bold;
                padding: 1rem !important;
                gap: 15px;
            }
        }

        /********End table********/

    </style>
@endpush
