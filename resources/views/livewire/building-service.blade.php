<div id="first-middle-table" class="col-md-12 p-0">
    <div class="container-fluid row" style="z-index: 11; width: 100%;">
        <div class="header-table-contact background-color-mode d-flex justify-content-between rounded-0" style="padding: 20px; margin-left: 10px;">
            <h4 class="text-capitalize titleTables font-weight-bold">Servicios</h4>
            <div class="search-container">
                <input id="search_services" type="text" placeholder="Buscar" class="search-input" wire:model.live.debounce.500ms="search">
                <div class="shortcuts-container d-flex align-items-center">
                    <div id="shortcuts-services" class="shortcuts @if($search) d-none @endif"><span>Ctrl</span> + <span>3</span></div>
                    <div class="search-button"><ion-icon name="search-outline" wire:ignore></ion-icon></div>
                </div>
            </div>
        </div>
    </div>
    <div class="background-color-mode-building-tables col-md-12">
        <div class="row tupakl scroll-content" style="max-height: 25vh; overflow-y: auto; font-size: 14px;">
            @include('vendor.backpack.widgets.tables.building-show-service-table')
        </div>
    </div>
</div>

@push('after_scripts')
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            document.addEventListener("keydown", (e) => {
                if (e.ctrlKey && e.key === '3') {
                    e.preventDefault();
                    document.getElementById('search_services').focus();
                }
            });
        });

        function openWindow(path, id) {
            window.open(`${window.origin}/admin/${path}/${id}/show`, '_blank', 'noopener,noreferrer');
        }

        $('#search_services').on('input', (e) => {
            if(e.target.value.trim().length != 0) $('#shortcuts-services').addClass('d-none')
            else $('#shortcuts-services').removeClass('d-none')
        })
    </script>
@endpush
