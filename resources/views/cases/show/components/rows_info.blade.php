<?php
?>

<div class="card no-padding no-border show-case-info-container-jr">
    <div class="card">
        <div class="width-less-padding">
            <div class="show-row row">
                <div class="col-md-2">
                    <strong>{!! 'ESTADO' !!}:</strong>
                </div>
                <div class="col-md-2">
                    @include('vendor.backpack.crud.columns.state_of_case')
                </div>
                <div class="col-md-2">
                    <strong>{!! 'PRIORIDAD' !!}:</strong>
                </div>
                <div class="col-md-6">
                    @include('vendor.backpack.crud.columns.priority_of_case')
                </div>

            </div>
            <div class="show-row row">
                @if (sizeof($entry->last_category->getParentAndGrandParent()) == 2)
                    <div class="col-md-2">
                        <strong>{!! 'CATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-2">
                        {{ $entry->last_category->getParentAndGrandParent()[1] }}
                    </div>
                    <div class="col-md-2">
                        <strong>{!! 'SUBCATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-2">
                        {{ $entry->last_category->getParentAndGrandParent()[0] }}
                    </div>
                    <div class="col-md-2">
                        <strong>{!! 'SUBCATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-2">
                        {{ $entry->last_category->name }}
                    </div>
                @elseif(sizeof($entry->last_category->getParentAndGrandParent()) == 1)
                    <div class="col-md-2">
                        <strong>{!! 'CATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-2">
                        {{ $entry->last_category->getParentAndGrandParent()[0] }}
                    </div>
                    <div class="col-md-2">
                        <strong>{!! 'SUBCATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-6">
                        {{ $entry->last_category->name }}
                    </div>
                @else
                    <div class="col-md-2">
                        <strong>{!! 'CATEGORÍA' !!}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ $entry->last_category->name }}
                    </div>
                @endif
            </div>
            <div class="show-row row">
                <div class="col-md-2">
                    <strong>{!! 'EDIFICIO' !!}:</strong>
                </div>
                <div class="col-md-2">
                    {{ ($entry->building->building_number ?? '') . ' - ' . ($entry->building->name ?? 'Sin edificio asociado') }}
                </div>
                <div class="col-md-2">
                    <strong>{!! 'APARTAMENTO' !!}:</strong>
                </div>
                <div class="col-md-4">
                    {{ $entry->flat->number ?? 'Sin apartamento asociado' }}
                </div>
            </div>
            <div class="show-row row">
                <div class="col-md-2">
                    <strong>{!! 'CREACIÓN' !!}:</strong>
                </div>
                <div class="col-md-2">
                    <div class="col-md-12" style="padding: 0px">
                        {{ $entry->creator }}
                    </div>
                    <div class="col-md-12" style="padding: 0px">
                        {{ $entry->created_at }}
                    </div>
                </div>
                <div class="col-md-2">
                    <strong>{!! 'RESPONSABLE' !!}:</strong>
                </div>
                <div class="col-md-4">
                    {{ \App\Models\User\Contact::find($entry->responsable)->complete_name ?? 'Sin responsable asociado' }}
                </div>
            </div>
            <div class="show-row row">
                <div class="col-md-2">
                    <strong>{!! 'DETALLE' !!}:</strong>
                </div>
                <div class="col-md-10">
                    <div class="col-md-12 bold pb-2">
                        {{ $entry->title }}
                    </div>
                    <div class="col-md-12">
                        {{ $entry->description }}
                    </div>
                </div>
            </div>
            @if ($entry->service)
                <div class="show-row row">
                    <div class="col-md-2">
                        <strong>{!! 'ACCESOS' !!}:</strong>
                    </div>
                    <div class="col-md-10">
                        {{ ($entry->service->service ?? '') . ' - ' . ($entry->service->provider ?? 'Sin servicio asociado') }}
                    </div>
                </div>
            @endif
            @if ($entry->limited_date)
                <div class="show-row row">
                    <div class="col-md-2">
                        <strong>{!! 'FECHA LIMITE' !!}:</strong>
                    </div>
                    <div class="col-md-2">
                        {{ $entry->limited_date }}
                    </div>
                    <div class="col-md-2">
                        <strong>{!! 'RECORDATORIO' !!}:</strong>
                    </div>
                    <div class="col-md-6">
                        {{ $entry->warnings_days }}
                    </div>
                </div>
            @endif
            <div class="show-row last-show-row row">
                <div class="col-md-2">
                    <strong>{!! 'ADJUNTOS' !!}:</strong>
                </div>
                <div class="col-md-10 row">
                    @foreach ($entry->images ?? [] as $image)
                        <?php
                        $bucket = env('AWS_BUCKET', 'foxsys-crm-backup');
                        $imageUrl = "https://{$bucket}.s3.amazonaws.com/{$image}";
                        ?>
                        <a target="_blank"
                            href="https://{{ env('AWS_BUCKET') }}.s3.amazonaws.com/{{ $image }}">{{ $image }}</a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
