<?php
    $currentUserName = '';
    if(isset($entry)) {
        $userCloseCase = \App\Models\User\User::find($entry->close_cases_user_id) ? \App\Models\User\User::find($entry->close_cases_user_id)?->complete_name : '';
        $currentUserName = $entry?->close_cases_user_id == config('constants.user_id_crm_daemons', 35304) ? 'Cerrado automáticamente por CRM' : $userCloseCase;
    }
?>

<div class="cases-description-table card fields-back">
    <label>Descripción</label>
    <span>{{ $entry->title ?? '' }}</span>

    <textarea style="width: 100%;" readonly class="auto-size-text-area-description auto-size-text-area">{{ $entry->description ?? '' }}</textarea>
    <label>Adjuntos</label>


    <div id="myModal" class="modal">
        <img class="modal-content" id="img01">
        <div id="caption"></div>
    </div>


    @foreach ($images as $img)
        <?php
        $extension = pathinfo($img, PATHINFO_EXTENSION);
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg', 'webp'];

        $bucket = env('AWS_BUCKET', 'foxsys-crm-backup');
        $imageUrl = "https://{$bucket}.s3.amazonaws.com/{$img}";
        ?>
        <div class="cases-description-img-conteiner">
            @if (!in_array(strtolower($extension), $imageExtensions))
                <a href="{{ $bucket ? $imageUrl : '#' }}" target="_blank">
                    {{ $img }}
                </a>
            @else
                @if ($bucket)
                    <img src="{{ $imageUrl }}" onclick="showImg(this)" id="{{ $img }}">
                @else
                    <p>Image not available</p>
                @endif
                <p>{{ $img }}</p>
            @endif
        </div>
    @endforeach
    {{--    <label>Habilita Accesos</label> --}}
    {{--    <div class="cases-description-createdby"> --}}
    {{--        <span>27/5/18 16:30</span> --}}
    {{--        <p>Santiago Ferreyra</p> --}}
    {{--    </div> --}}

    <hr style="border: 1px solid #EBE9F1;">
    <label>Creación</label>
    <div class="cases-description-createdby">
        <span>{{ \Carbon\Carbon::parse($entry->created_at)->format('d/m/Y H:i') ?? '' }}</span>
        <p>{{ \App\Models\User\User::find($entry->created_by) ? \App\Models\User\User::find($entry->created_by)->complete_name : '' }}
        </p>
    </div>
    @if ($entry->end_date)
        <label>Finalización</label>
        <div class="cases-description-createdby">
            <span>{{ \Carbon\Carbon::parse($entry->end_date)->format('d/m/Y H:i') ?? '' }}</span>
            <p>{{ $currentUserName }}
            </p>
        </div>
    @endif
</div>


<script>
    $('.auto-size-text-area-description').each(function() {
        this.setAttribute('style', 'height:' + (this.scrollHeight + 100) + 'px;overflow-y:hidden;width: 100%');
    }).on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    function getFileExtension2(filename) {
        return filename.id.split('.').pop();
    }

    function showImg(file) {

        getFileExtension2(file);

        var modal = document.getElementById("myModal");

        var img = document.getElementById(file.id);
        var modalImg = document.getElementById("img01");
        var captionText = document.getElementById("caption");


        img.onclick = function() {
            modal.style.display = "block";
            modalImg.src = this.src;
            captionText.innerHTML = this.alt;
        }


        modal.onclick = function() {
            modal.style.display = "none";
        }

    }
</script>
<style>
    #fav-show-image {
        cursor: pointer;
        transition: 0.3s;
    }

    #fav-show-image {
        opacity: 0.7;
    }


    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        padding-top: 100px;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0, 0, 0);
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 9999999;
    }

    .modal-content {
        margin: auto;
        display: block;
        width: 25%;
        max-width: 700px;

    }

    #caption {
        margin: auto;
        display: block;
        width: 0%;
        max-width: 700px;
        text-align: center;
        color: #ccc;
        padding: 10px 0;
        height: 150px;
    }

    .modal-content,
    #caption {
        -webkit-animation-name: zoom;
        -webkit-animation-duration: 0.6s;
        animation-name: zoom;
        animation-duration: 0.6s;
    }
</style>
