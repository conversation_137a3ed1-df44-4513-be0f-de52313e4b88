@extends(backpack_view('blank'))

<?php
$all_buildings = \App\Models\Building::all();
$buildings_with_towers = [];
foreach ($all_buildings as $building) {
    if ($building->towers->first() != null) {
        array_push($buildings_with_towers, $building->id);
    }
}
?>

@section('header')
    <title>Enviar mails de registro de la APP</title>
@endsection

@section('content')
    <div class="app-body">


        <main class="main pt-2">

            <nav aria-label="breadcrumb" class="d-none d-lg-block">
                <ol class="breadcrumb bg-transparent p-0 justify-content-end">
                </ol>
            </nav>

            <section class="container-fluid text-center">
                <h2>

                    <span class="">Enviar mensaje de WhatssApp a través de WittyBots</span>
                    <br>
                    <br>


                </h2>
            </section>

            <div class="container-fluid animated fadeIn text-center">
                <div class="row text-center cont-loader-wrapper">
                    <div class="col-md-8 bold-labels m-auto row cont-loader">
                        <div class="card col-md-5">
                            <h4 style="padding-top: 50px;">Template</h4>
                            <hr>
                            <div class="container text-left" id="screen_template">
                                {{--                                {!! nl2br(\App\Models\WittyTemplate::first()->body) !!} --}}
                            </div>
                        </div>
                        <div class="card col-md-7">
                            <div class="card-body row">

                                <div class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el mensaje a enviar
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select onchange="showVariables()" name="template" id="template"
                                                style="width: 75%" required class="form-control select2">
                                                <option value="" selected="selected" disabled>Elegir mensaje
                                                </option>
                                                @foreach ($templates as $template)
                                                    <option value='{{ $template->key }}'>{{ $template->name }}</option>
                                                @endforeach
                                            </select>

                                        </div>

                                        <hr>
                                    </div>

                                </div>

                                <div style="display: none;" id="div_variables_aviso_averia" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el motivo de la averia
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center">
                                        <div class="form-group col-md-6 m-auto">
                                            <select name="variable" id="variableAvisoAveria" style="width: 75%"
                                                class="form-control select2" required>
                                                <option value="null" selected="selected" disabled>Elegir mensaje
                                                </option>
                                                <option value="ascensor">ascensor</option>
                                                <option value="la rampa">la rampa</option>
                                                <option value="la puerta principal">la puerta principal</option>
                                                <option value="portón del garaje">portón garaje</option>
                                                <option value="la caldera">la caldera</option>
                                                <option value="tanque de agua">tanque de agua</option>
                                                <option value="la bomba de agua">la bomba de agua</option>
                                                <option value="portero eléctrico">portero eléctrico</option>
                                            </select>

                                        </div>

                                        <hr>
                                    </div>

                                </div>

                                <div style="display: none;" id="div_variables_solucion_averia" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el motivo de la averia
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select name="variable" id="variableSolucionAveria" style="width: 75%"
                                                class="form-control select2" required>
                                                <option value="null" selected="selected" disabled>Elegir mensaje
                                                </option>
                                                <option value="El ascensor">El ascensor</option>
                                                <option value="La rampa">La rampa</option>
                                                <option value="La puerta principal">La puerta principal</option>
                                                <option value="El portón del garaje">El portón garaje</option>
                                                <option value="La caldera">La caldera</option>
                                                <option value="El tanque de agua">El tanque de agua</option>
                                                <option value="La bomba de agua">La bomba de agua</option>
                                                <option value="El portero eléctrico">El portero eléctrico</option>
                                            </select>

                                        </div>

                                        <hr>
                                    </div>

                                </div>

                                <div style="display: none;" id="div_variables_respuesta_corte" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el motivo de la averia
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select name="variable" id="variableRespuestaCorte" style="width: 75%"
                                                class="form-control select2" required>
                                                <option value="null" selected="selected" disabled>Elegir mensaje
                                                </option>
                                                <option value="internet">internet</option>
                                                <option value="enegría">energía</option>
                                            </select>

                                        </div>
                                        <hr>
                                    </div>
                                </div>

                                <div style="display: none;" id="div_variables_respuesta_averia" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el motivo de la averia
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select name="variable" id="variableRespuestaAveria" style="width: 75%"
                                                class="form-control select2" required>
                                                <option value="null" selected="selected" disabled>Elegir mensaje
                                                </option>
                                                <option value="la puerta principal">la puerta principal</option>
                                                <option value="portón de garaje">portón de garaje</option>
                                            </select>

                                        </div>

                                        <hr>
                                    </div>
                                </div>

                                <div style="display: none;" id="div_variables2" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el técnico asignado
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select name="variable2" id="variable2" style="width: 75%"
                                                class="form-control select2" required>
                                                <option value="null" selected="selected" disabled>Elegir técnico
                                                </option>
                                                @foreach ($security as $s)
                                                    <option value='{{ $s->id ?? null }}'>{{ $s->complete_name ?? ' ' }}
                                                    </option>
                                                @endforeach
                                            </select>

                                        </div>

                                        <hr>
                                    </div>

                                </div>

                                <div style="display: none;" id="div_variables3" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Selecciona el precio
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <input id="variable3" name="variable3" type="number"
                                                placeholder="Seleccione precio">

                                        </div>

                                        <hr>
                                    </div>

                                </div>

                                <div style="display: none;" id="div_variables_activar_desactivar_tag" class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Código de TAG
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <input id="variable_tags" name="variable_tags" type="text"
                                                placeholder="Código">
                                        </div>

                                        <hr>
                                    </div>

                                </div>


                                <div class="col-md-12">
                                    <div class="col-md-6 m-auto">
                                        <span role="presentation" aria-hidden="true">
                                            Enviar mensaje a todos los contactos habilitados del edificio seleccionado
                                        </span> &nbsp;
                                    </div>

                                    <div class="col-md-12 text-center ">
                                        <div class="form-group col-md-6 m-auto">
                                            <select onchange="changeSelect2()" name="buildings" id="buildings"
                                                style="width: 75%" required class="form-control select2">
                                                <option value="" selected="selected" disabled>Elegir edificio
                                                </option>
                                                @foreach ($all_buildings as $building)
                                                    <option value='{{ $building->id }}'>{{$building->building_number}} - {{ $building->parsedName() }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6 m-auto">
                                            <button id="send-building" class="btn btn-success m-2">
                                                <span>¡Enviar!</span>
                                            </button>
                                        </div>
                                        <hr>
                                    </div>
                                </div>

                                <div class="col-md-12" id="select_towers_options" style="display: none">
                                    <span role="presentation" aria-hidden="true">
                                        Mensaje a todos los contactos habilitados de la torre seleccionada
                                    </span> &nbsp;
                                    <div class="col-md-12 text-center">

                                        <div class="form-group col-md-6 m-auto">
                                            <select name="tower" id="tower" style="width: 75%" required
                                                class="form-control select2" multiple>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6 m-auto">
                                            <button id="send-tower" class="btn btn-success m-1">
                                                <span>¡Enviar!</span>
                                            </button>
                                        </div>
                                        <hr>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <span role="presentation" aria-hidden="true">
                                        Enviar mensaje a los contactos de comisión del edificio seleccionado
                                    </span> &nbsp;
                                    <div class="form-group col-md-6 m-auto">
                                        <button id="send-comision" class="btn btn-success m-1">
                                            <span>¡Enviar!</span>
                                        </button>
                                    </div>
                                    <hr>
                                </div>
                            </div>


                            <div class="col-md-12">
                                <span role="presentation" aria-hidden="true">
                                    Mensaje a contactos individuales del edificio seleccionado
                                </span> &nbsp;
                                <div class="col-md-12 text-center">

                                    <div class="form-group col-md-6 m-auto">
                                        <select name="contacts" id="contacts" style="width: 75%" required
                                            class="form-control select2" multiple>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-6 m-auto">
                                        <button id="send-contacts" class="btn btn-success m-1">
                                            <span>¡Enviar!</span>
                                        </button>
                                    </div>
                                    <hr>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="loader"></div>
                    <h2 class="loader-text">No cierre la página hasta que termine de mandar los menajes</h2>

                </div>
            </div>

        </main>
    </div>

    <style>
        .cont-loader-wrapper {
            position: relative;
        }

        .loader-text {
            position: absolute;
            top: 57%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            display: none;
        }

        .cont-loader {
            opacity: 1;
        }

        .loader,
        .loader-text {
            position: absolute;
            z-index: 10;
        }


        .loader {
            border: 10px solid #f3f3f3;
            border-top: 10px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            position: absolute;
            z-index: 10;
            left: 48%;
            top: 45%;
            display: none;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .loader-opacity {
            opacity: 0.1;
        }

        .select2-container {
            box-sizing: border-box;
            display: inline-block;
            margin: 0;
            position: relative;
            vertical-align: middle
        }

        .select2-container .select2-selection--single {
            box-sizing: border-box;
            cursor: pointer;
            display: block;
            height: 28px;
            user-select: none;
            -webkit-user-select: none
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            display: block;
            padding-left: 8px;
            padding-right: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap
        }

        .select2-container .select2-selection--single .select2-selection__clear {
            position: relative
        }

        .select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
            padding-right: 8px;
            padding-left: 20px
        }

        .select2-container .select2-selection--multiple {
            box-sizing: border-box;
            cursor: pointer;
            display: block;
            min-height: 32px;
            user-select: none;
            -webkit-user-select: none
        }

        .select2-container .select2-selection--multiple .select2-selection__rendered {
            display: inline-block;
            overflow: hidden;
            padding-left: 8px;
            text-overflow: ellipsis;
            white-space: nowrap
        }

        .select2-container .select2-search--inline {
            float: left
        }

        .select2-container .select2-search--inline .select2-search__field {
            box-sizing: border-box;
            border: none;
            font-size: 100%;
            margin-top: 5px;
            padding: 0
        }

        .select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
            -webkit-appearance: none
        }

        .select2-dropdown {
            background-color: white;
            border: 1px solid #aaa;
            border-radius: 4px;
            box-sizing: border-box;
            display: block;
            position: absolute;
            left: -100000px;
            width: 100%;
            z-index: 1051
        }

        .select2-results {
            display: block
        }

        .select2-results__options {
            list-style: none;
            margin: 0;
            padding: 0
        }

        .select2-results__option {
            padding: 6px;
            user-select: none;
            -webkit-user-select: none
        }

        .select2-results__option[aria-selected] {
            cursor: pointer
        }

        .select2-container--open .select2-dropdown {
            left: 0
        }

        .select2-container--open .select2-dropdown--above {
            border-bottom: none;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0
        }

        .select2-container--open .select2-dropdown--below {
            border-top: none;
            border-top-left-radius: 0;
            border-top-right-radius: 0
        }

        .select2-search--dropdown {
            display: block;
            padding: 4px
        }

        .select2-search--dropdown .select2-search__field {
            padding: 4px;
            width: 100%;
            box-sizing: border-box
        }

        .select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
            -webkit-appearance: none
        }

        .select2-search--dropdown.select2-search--hide {
            display: none
        }

        .select2-close-mask {
            border: 0;
            margin: 0;
            padding: 0;
            display: block;
            position: fixed;
            left: 0;
            top: 0;
            min-height: 100%;
            min-width: 100%;
            height: auto;
            width: auto;
            opacity: 0;
            z-index: 99;
            background-color: #fff;
            filter: alpha(opacity=0)
        }

        .select2-hidden-accessible {
            border: 0 !important;
            clip: rect(0 0 0 0) !important;
            -webkit-clip-path: inset(50%) !important;
            clip-path: inset(50%) !important;
            height: 1px !important;
            overflow: hidden !important;
            padding: 0 !important;
            position: absolute !important;
            width: 1px !important;
            white-space: nowrap !important
        }

        .select2-container--default .select2-selection--single {
            background-color: #fff;
            border: 1px solid #aaa;
            border-radius: 4px
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 28px
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            cursor: pointer;
            float: right;
            font-weight: bold
        }

        .select2-container--default .select2-selection--single .select2-selection__placeholder {
            color: #999
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 26px;
            position: absolute;
            top: 1px;
            right: 1px;
            width: 20px
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow b {
            border-color: #888 transparent transparent transparent;
            border-style: solid;
            border-width: 5px 4px 0 4px;
            height: 0;
            left: 50%;
            margin-left: -4px;
            margin-top: -2px;
            position: absolute;
            top: 50%;
            width: 0
        }

        .select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
            float: left
        }

        .select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
            left: 1px;
            right: auto
        }

        .select2-container--default.select2-container--disabled .select2-selection--single {
            background-color: #eee;
            cursor: default
        }

        .select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
            display: none
        }

        .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent #888 transparent;
            border-width: 0 4px 5px 4px
        }

        .select2-container--default .select2-selection--multiple {
            background-color: white;
            border: 1px solid #aaa;
            border-radius: 4px;
            cursor: text
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered {
            box-sizing: border-box;
            list-style: none;
            margin: 0;
            padding: 0 5px;
            width: 100%
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            list-style: none
        }

        .select2-container--default .select2-selection--multiple .select2-selection__clear {
            cursor: pointer;
            float: right;
            font-weight: bold;
            margin-top: 5px;
            margin-right: 10px;
            padding: 1px
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #e4e4e4;
            border: 1px solid #aaa;
            border-radius: 4px;
            cursor: default;
            float: left;
            margin-right: 5px;
            margin-top: 5px;
            padding: 0 5px
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: #999;
            cursor: pointer;
            display: inline-block;
            font-weight: bold;
            margin-right: 2px
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #333
        }

        .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice,
        .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
            float: right
        }

        .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
            margin-left: 5px;
            margin-right: auto
        }

        .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
            margin-left: 2px;
            margin-right: auto
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border: solid black 1px;
            outline: 0
        }

        .select2-container--default.select2-container--disabled .select2-selection--multiple {
            background-color: #eee;
            cursor: default
        }

        .select2-container--default.select2-container--disabled .select2-selection__choice__remove {
            display: none
        }

        .select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
        .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
            border-top-left-radius: 0;
            border-top-right-radius: 0
        }

        .select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
        .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #aaa
        }

        .select2-container--default .select2-search--inline .select2-search__field {
            background: transparent;
            border: none;
            outline: 0;
            box-shadow: none;
            -webkit-appearance: textfield
        }

        .select2-container--default .select2-results>.select2-results__options {
            max-height: 200px;
            overflow-y: auto
        }

        .select2-container--default .select2-results__option[role=group] {
            padding: 0
        }

        .select2-container--default .select2-results__option[aria-disabled=true] {
            color: #999
        }

        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: #ddd
        }

        .select2-container--default .select2-results__option .select2-results__option {
            padding-left: 1em
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__group {
            padding-left: 0
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__option {
            margin-left: -1em;
            padding-left: 2em
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
            margin-left: -2em;
            padding-left: 3em
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
            margin-left: -3em;
            padding-left: 4em
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
            margin-left: -4em;
            padding-left: 5em
        }

        .select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
            margin-left: -5em;
            padding-left: 6em
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #5897fb;
            color: white
        }

        .select2-container--default .select2-results__group {
            cursor: default;
            display: block;
            padding: 6px
        }

        .select2-container--classic .select2-selection--single {
            background-color: #f7f7f7;
            border: 1px solid #aaa;
            border-radius: 4px;
            outline: 0;
            background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
            background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
            background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0)
        }

        .select2-container--classic .select2-selection--single:focus {
            border: 1px solid #5897fb
        }

        .select2-container--classic .select2-selection--single .select2-selection__rendered {
            line-height: 28px
        }

        .select2-container--classic .select2-selection--single .select2-selection__clear {
            cursor: pointer;
            float: right;
            font-weight: bold;
            margin-right: 10px
        }

        .select2-container--classic .select2-selection--single .select2-selection__placeholder {
            color: #999
        }

        .select2-container--classic .select2-selection--single .select2-selection__arrow {
            background-color: #ddd;
            border: none;
            border-left: 1px solid #aaa;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            height: 26px;
            position: absolute;
            top: 1px;
            right: 1px;
            width: 20px;
            background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
            background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
            background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0)
        }

        .select2-container--classic .select2-selection--single .select2-selection__arrow b {
            border-color: #888 transparent transparent transparent;
            border-style: solid;
            border-width: 5px 4px 0 4px;
            height: 0;
            left: 50%;
            margin-left: -4px;
            margin-top: -2px;
            position: absolute;
            top: 50%;
            width: 0
        }

        .select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
            float: left
        }

        .select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
            border: none;
            border-right: 1px solid #aaa;
            border-radius: 0;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            left: 1px;
            right: auto
        }

        .select2-container--classic.select2-container--open .select2-selection--single {
            border: 1px solid #5897fb
        }

        .select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
            background: transparent;
            border: none
        }

        .select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent #888 transparent;
            border-width: 0 4px 5px 4px
        }

        .select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
            border-top: none;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            background-image: -webkit-linear-gradient(top, #fff 0%, #eee 50%);
            background-image: -o-linear-gradient(top, #fff 0%, #eee 50%);
            background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0)
        }

        .select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
            border-bottom: none;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
            background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
            background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0)
        }

        .select2-container--classic .select2-selection--multiple {
            background-color: white;
            border: 1px solid #aaa;
            border-radius: 4px;
            cursor: text;
            outline: 0
        }

        .select2-container--classic .select2-selection--multiple:focus {
            border: 1px solid #5897fb
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__rendered {
            list-style: none;
            margin: 0;
            padding: 0 5px
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__clear {
            display: none
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice {
            background-color: #e4e4e4;
            border: 1px solid #aaa;
            border-radius: 4px;
            cursor: default;
            float: left;
            margin-right: 5px;
            margin-top: 5px;
            padding: 0 5px
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
            color: #888;
            cursor: pointer;
            display: inline-block;
            font-weight: bold;
            margin-right: 2px
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #555
        }

        .select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
            float: right;
            margin-left: 5px;
            margin-right: auto
        }

        .select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
            margin-left: 2px;
            margin-right: auto
        }

        .select2-container--classic.select2-container--open .select2-selection--multiple {
            border: 1px solid #5897fb
        }

        .select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
            border-top: none;
            border-top-left-radius: 0;
            border-top-right-radius: 0
        }

        .select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
            border-bottom: none;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0
        }

        .select2-container--classic .select2-search--dropdown .select2-search__field {
            border: 1px solid #aaa;
            outline: 0
        }

        .select2-container--classic .select2-search--inline .select2-search__field {
            outline: 0;
            box-shadow: none
        }

        .select2-container--classic .select2-dropdown {
            background-color: #fff;
            border: 1px solid transparent
        }

        .select2-container--classic .select2-dropdown--above {
            border-bottom: none
        }

        .select2-container--classic .select2-dropdown--below {
            border-top: none
        }

        .select2-container--classic .select2-results>.select2-results__options {
            max-height: 200px;
            overflow-y: auto
        }

        .select2-container--classic .select2-results__option[role=group] {
            padding: 0
        }

        .select2-container--classic .select2-results__option[aria-disabled=true] {
            color: grey
        }

        .select2-container--classic .select2-results__option--highlighted[aria-selected] {
            background-color: #3875d7;
            color: #fff
        }

        .select2-container--classic .select2-results__group {
            cursor: default;
            display: block;
            padding: 6px
        }

        .select2-container--classic.select2-container--open .select2-dropdown {
            border-color: #5897fb
        }

        .select2-search__field {
            position: absolute !important;
            margin: -7px;
            margin-top: -45px;
        }
    </style>
@endsection

@section('after_scripts')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
    <script src="http://blog.ikhuerta.com/jsDownload/dollar_get.js" type="text/javascript"></script>

    <script>

        function showVariables() {

            var body = '';

            $.ajax({
                url: '/api/template/body/' + $("#template").val(),
                type: 'GET',
                dataType: "json",
                async: false,
                success: function(data) {
                    body = data;
                },
            });


            $('#div_variables_aviso_averia').hide();
            $('#div_variables_solucion_averia').hide();
            $('#div_variables2').hide();
            $('#div_variables3').hide();
            $('#div_variables_activar_desactivar_tag').hide();
            $('#div_variables_respuesta_corte').hide();
            $('#div_variables_respuesta_averia').hide();


            switch($("#template").val()) {
                case 'aviso_averia_v1':
                    $('#div_variables_aviso_averia').show();
                    break;

                case 'solucion_averia_v2':
                    $('#div_variables_solucion_averia').show();
                    break;

                case 'inspeccion_de_seguridad_noche':
                case 'relevamiento_tecnico_pre_entrega_v1':
                    $('#div_variables2').show();
                    break;
                case 'tag_acceso_solicitud_v6':
                    $('#div_variables3').show();
                    break;
                case 'tag_activado':
                case 'tag_desactivado':
                    $('#div_variables_activar_desactivar_tag').show();
                    break;

                case 'respuesta_corte':
                    $('#div_variables_respuesta_corte').show();
                    break;

                case 'respuesta_averia':
                    $('#div_variables_respuesta_averia').show();
                    break;
            }

            $('#screen_template').html(nl2br(body.data));
        }

        function getTemplateVariableName(template_name) {
            var variable = '';

            if (template_name == 'aviso_averia_v1') {
                variable = 'variableAvisoAveria';
            } else if (template_name == 'solucion_averia_v2') {
                variable = 'variableSolucionAveria';
            } else if (template_name == 'inspeccion_de_seguridad_noche' || template_name ==
                'relevamiento_tecnico_pre_entrega_v1') {
                variable = 'variable2';
            } else if (template_name == 'tag_acceso_solicitud_v6') {
                variable = 'variable3';
            } else if (template_name == 'tag_activado' || template_name == 'tag_desactivado') {
                variable = 'variable_tags';
            } else if (template_name == 'respuesta_corte') {
                variable = 'variableRespuestaCorte';
            } else if (template_name == 'respuesta_averia') {
                variable = 'variableRespuestaAveria';
            }

            return variable;
        }

        function nl2br(str, is_xhtml) {
            var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
            return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
        }

        $(document).ready(function() {
            $('.select2').select2();
        });

        function changeAccents(string) {
            const accents = {
                'á': 'a',
                'é': 'e',
                'í': 'i',
                'ó': 'o',
                'ú': 'u',
                'Á': 'A',
                'É': 'E',
                'Í': 'I',
                'Ó': 'O',
                'Ú': 'U'
            };
            return string.split('').map(leter => accents[leter] || leter).join('').toString();
        }

        function changeSelect2() {
            building_with_towers = '{{ implode(',', $buildings_with_towers) }}';
            building_with_towers = building_with_towers.split(',');
            if (building_with_towers.includes($('#buildings').val())) {
                $('#select_towers_contacts').css('display', 'inline');
                $('#select_towers_options').css('display', 'inline');
            } else {
                $('#select_towers_contacts').css('display', 'none');
                $('#select_towers_options').css('display', 'none');
            }

            $('#contacts').select2({
                multiple: true,
                placeholder: 'Seleccione contacto/s',
                cache: true,
                minimumInputLength: 3,
                ajax: {
                    url: '/api/contacts/building/' + document.getElementById('buildings').value,
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term, // search term
                        };
                    },
                    processResults: function(data, params) {
                        return {
                            results: $.map(data.data, function(item) {
                                if (item.phone_mobile) {
                                    if (changeAccents(item.complete_name).toUpperCase().includes(params
                                            .term.toUpperCase()) || item.phone_mobile.includes(params
                                            .term)) {
                                        return {
                                            text: item.complete_name + ', ' + item.phone_mobile,
                                            id: item.contact_id
                                        }
                                    }
                                }
                            }),
                        };
                    },
                },
            });

            $('#tower').select2({
                multiple: false,
                placeholder: 'Seleccione torre/s',
                cache: true,
                minimumInputLength: 0,
                ajax: {
                    url: '/api/tower/building/' + document.getElementById('buildings').value,
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term, // search term
                        };
                    },
                    processResults: function(data, params) {
                        return {
                            results: $.map(data.data, function(item) {
                                if (item) {
                                    return {
                                        text: item.tower_denomination,
                                        id: item.id
                                    }
                                }
                            }),
                        };
                    },
                },
            });
        }

        document.getElementById("send-building").addEventListener("click", function(e) {
            var building_id = $("#buildings").val();
            var template = $("#template").val();

            if (!template || template.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un template de Witty para enviar el mensaje.");
                return;
            }

            if (!building_id || building_id.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un edificio para enviar el mensaje.");
                return;
            }

            if (!confirm(
                    "¿Está seguro que quiere mandar mensaje a todos los usuarios del edificio seleccionado?")) {
                e.preventDefault();
            } else {
                $('.cont-loader').css('opacity', '0.1');
                $('.loader, .loader-text').css('display', 'block');

                var variableName = getTemplateVariableName(template);
                var variable = null;
                if (variableName != '') {
                    variable = $('#' + variableName).val();
                }

                var settings = {
                    "url": "/api/send-mesage-to-contacts-in-building/" + "{{ backpack_user()->id }}",
                    "type": "POST",
                    "data": {
                        building_id,
                        variable,
                        template
                    },
                    "success": function(response) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Mensaje enviado correctamente.");

                    },
                    "error": function(xhr, status, error) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Ocurrió un error: " + error);
                    }
                };
                $.ajax(settings);
            }
        });


        document.getElementById("send-tower").addEventListener("click", function(e) {
            var building_id = $("#buildings").val();
            var template = $("#template").val();

            if (!template || template.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un template de Witty para enviar el mensaje.");
                return;
            }

            if (!building_id || building_id.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un edificio para enviar el mensaje.");
                return;
            }

            if (!confirm("¿Está seguro que quiere mandar mensaje a todos los usuarios de la torre seleccionada?")) {
                e.preventDefault();
            } else {
                $('.cont-loader').css('opacity', '0.1');
                $('.loader, .loader-text').css('display', 'block');


                var variableName = getTemplateVariableName(template);
                var variable = null;
                if (variableName != '') {
                    variable = $('#' + variableName).val();
                }

                var tower_id = $("#tower").val();

                var settings = {
                    "url": "/api/send-mesage-to-contacts-in-tower/" + "{{ backpack_user()->id }}",
                    "type": "POST",
                    "data": {
                        building_id,
                        variable,
                        template,
                        tower_id
                    }, // Asumiendo que debería ser 'template'
                    "success": function(response) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Mensaje enviado correctamente.");

                    },
                    "error": function(xhr, status, error) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Ocurrió un error: " + error);
                    }
                };
                $.ajax(settings);
            }
        });


        document.getElementById("send-comision").addEventListener("click", function(e) {
            var building_id = $("#buildings").val();
            var template = $("#template").val();

            if (!template || template.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un template de Witty para enviar el mensaje.");
                return;
            }

            if (!building_id || building_id.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un edificio para enviar el mensaje.");
                return;
            }

            if (!confirm(
                    "¿Está seguro que quiere mandar mensaje a todos los usuarios comisión del edificio seleccionado?"
                )) {
                e.preventDefault();
            } else {
                $('.cont-loader').css('opacity', '0.1');
                $('.loader, .loader-text').css('display', 'block');

                var variableName = getTemplateVariableName(template);
                var variable = null;
                if (variableName != '') {
                    variable = $('#' + variableName).val();
                }

                var settings = {
                    "url": "/api/send-mesage-to-comision-contacts-in-building/" + "{{ backpack_user()->id }}",
                    "type": "POST",
                    "data": {
                        building_id,
                        variable,
                        template
                    },
                    "success": function(response) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Mensaje enviado correctamente.");
                    },
                    "error": function(xhr, status, error) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        console.log("Status: ", status);
                        console.log("XHR Response: ", xhr.responseText);
                        console.log("Error Thrown: ", error);
                        alert("Ocurrió un error: " + (error || "No se pudo determinar el error"));
                    }
                };
                $.ajax(settings);
            }
        });


        document.getElementById("send-contacts").addEventListener("click", function(e) {
            var contacts_ids = $("#contacts").val();
            var building_id = $("#buildings").val();
            var template = $("#template").val();

            if (!template || template.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un template de Witty para enviar el mensaje.");
                return;
            }

            if (!building_id || building_id.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione un edificio para enviar el mensaje.");
                return;
            }

            if (!contacts_ids || contacts_ids.length === 0) {
                e.preventDefault();
                alert("Por favor, seleccione al menos un contacto antes de enviar el mensaje.");
                return;
            }

            if (!confirm("¿Está seguro que quiere mandar mensaje a los usuarios seleccionados anteriormente?")) {
                e.preventDefault();
            } else {
                $('.loader, .loader-text').css('display', 'block');
                $('.cont-loader').css('opacity', '0.1');

                var variableName = getTemplateVariableName(template);
                var variable = null;
                if (variableName != '') {
                    variable = $('#' + variableName).val();
                }

                var settings = {
                    "url": "/api/send-mesage-to-specific-contacts/" + "{{ backpack_user()->id }}",
                    "type": "POST",
                    "data": {
                        contacts_ids,
                        variable,
                        template
                    },
                    "success": function(response) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Mensaje enviado correctamente.");
                    },
                    "error": function(xhr, status, error) {
                        $('.loader, .loader-text').css('display', 'none');
                        $('.cont-loader').css('opacity', '1');
                        alert("Ocurrió un error: " + error);
                    }
                };
                $.ajax(settings).done(function(response) {});

                $('#div_variables').hide();
                $('#div_variables2').hide();
            }
        });


        function changeVisibilityOfTowers(visiible) {
            if (visiible == 0) {
                $('#select_towers_contacts').css('display', 'none')
                $('#select_towers_options').css('display', 'none')
            } else {
                $('#select_towers_contacts').css('display', 'inline')
                $('#select_towers_options').css('display', 'inline')
            }
        }

        setTimeout(function() {
            @if (array_key_exists('building_id', $_GET))
                $('#buildings').val('{{ $_GET['building_id'] }}').change();
                var $licencia_seleccionadas = $("<option selected></option>").val('{{ $_GET['contact_id'] }}')
                    .text('{{ \App\Models\User\Contact::find($_GET['contact_id'])->complete_name }}');
                $('#contacts').append($licencia_seleccionadas).trigger('change');
            @endif
        }, 1000)
    </script>
@endsection
