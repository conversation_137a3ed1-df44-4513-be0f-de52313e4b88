
@php
    $schedule = isset($contact) && $contact['schedule'];
    $contactId = isset($contact) && $contact['building_contact_id'] ? $contact['contact_id'] : null;
    $flatId = isset($contact) && $contact['flat_id'] ? $contact['flat_id'] : null;
    $scheduleText = '';
    if($schedule) {
        $result = \App\Models\User\Contact::getFakeSchedule($contactId, $flatId);
        if ($result && method_exists($result, 'content')) {
            $decoded = json_decode($result->content());
            $data = $decoded?->data ?? "";
        } else {
            $data = "";
        }
        $scheduleText = $data;
        $ultima_posicion = strrpos($scheduleText, '<br');
        // Verificar si se encontró <br>
        if ($ultima_posicion !== false) {
            // Eliminar la última aparición de <br>
            $texto_sin_ultimo_br = substr_replace($scheduleText, '', $ultima_posicion, strlen('<br'));
        } else {
            // Si no se encuentra <br>, dejar el texto intacto
            $texto_sin_ultimo_br = $scheduleText;
        }
        $scheduleText = $texto_sin_ultimo_br;
    }
@endphp
<div id="modal-container-popup-contact">
    <div class="modal-content">
        <div id="calling_notificcate" style="background-color: #41BA77;
    box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.2);
    width: 101%;
    padding: 5px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding-left: 15px;
    color: white;
    transition: all 5s;
    display: none">
        </div>
        <div class="row" style="width: 114%;">

            <div class="col-md-12" style="">
                <div id="header-blue-with-build-info" class="text-center row"
                     style="box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02); width: 93%; padding: 15px; border-top-right-radius: 5px; border-top-left-radius: 5px; background: linear-gradient(90deg, rgba(32, 49, 111, 1) 0%, rgba(32, 49, 111, 1) 0%, rgba(82, 106, 192, 1) 100%) !important; padding-left: 15px; color: white; margin-left: 0px;">
                    <div class="font-ibm"
                         style="min-width: 10%; background-color: white; color: #3B3B70FF; width: auto; height: 45px; border-radius: 5px; padding: 7px; font-weight: bold; font-size: 18px;">{{isset($building) ? $building['building_number'] : ''}}</div>
                    <div class="text-left font-ibm" id="show-contact-flat"
                         style="width: 72%; height: 45px; border-radius: 5px; padding: 12px; font-weight: 100; font-size: 20px; padding-top: 10px;">
                        {{isset($building) ? $building['name'] : ''}} / {{isset($contact) ? $contact['flat_number'] : ''}}
                    </div>
                    <div class="text-right font-ibm" style="width: 11%">
                        <img class="mx-auto" id="show-contact-image"
                             src="{{isset($contact['image'])  ?  $contact['image'] : asset('img/default-user.jpg')}}"
                             style="height: 55px; border-radius: 50px; width: 55px; border: 3px white solid; object-fit: cover">

                    </div>
                </div>
                <div class="text-center background-color-mode" style="width: 93%; border-radius: 0px;">
                    <div class="row" style="color: #4B4B4B;">
                        <div class="row" style="padding: 20px 10px; padding-bottom: 20px;">
                            <div class="col-md-2" style="padding-right: 0px;">
                                <i id="show-contact-icons" class="black_letter_in_light_white_dark">
                                </i>
                                <span id="icon-foxsys">
                                <ion-icon size="large"
                                          title="{{ \Illuminate\Support\Arr::get(\App\Models\Building::$iconsContactTypes, $contact['contact_type'] ?? '', '') }}"
                                          name="{{ (\Illuminate\Support\Arr::get(\App\Models\Building::$iconsContactTypes, $contact['contact_type'] ?? '', '') . '-outline') ?: 'person-outline' }}"
                                          wire:ignore>
                                </ion-icon>
                            </span>
                            </div>
                            <div class="hidden" id="show-contact-id"></div>

                            <div class="col-md-8 text-left font-ibm black_letter_in_light_white_dark"
                                 id="show-contact-complete-name"
                                 style="font-size: 24px; padding-left: 0px; margin-top: -2px;">
                                {{isset($contact) ? $contact['complete_name'] : ''}}
                            </div>
                            <a href="/admin/contact/{{isset($contact) ? $contact['id'] : 0}}/edit"
                               id="show-contact-edit-button" class="col-md-2 btn btn-link font-ibm"
                               style="padding-top: 10px; margin-bottom: 0px!important; cursor: pointer;">Editar</a>
                            @if(isset($contact['ci']) && strlen(trim($contact['ci'])) > 0)
                                <div id="show-contact-ci" class="col-md-12 font-ibm black_letter_in_light_white_dark"
                                     style="font-weight: bold">
                                    <div class="d-flex gap-1 w-auto" style="margin-left: 6.2rem">
                                        <div id="contact_ci_popup" class="contact-ci-security_word">
                                            {{$contact['ci']}}
                                        </div>
                                        @if(isset($contact['security_word']) )
                                            <div id="contact_security_word_popup"
                                                 class="contact-ci-security_word">
                                                {{$contact['security_word']}}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="row" style="padding-left: 7%;">
                            <div class="col-md-5 font-ibm text-left black_letter_in_light_white_dark"
                                 id="show-contact-type"
                                 style="padding-right: 0px;"><i
                                        id="show-contact-type-icon" title=""
                                        style="font-size: 22px; padding-right: 7px;"
                                        class=" la-2x">
                                </i>
                            </div>
                            <div class="col-md-5 font-ibm text-left" id="show-contact-type2"
                                 style="padding-right: 0px;"><i
                                        id="show-contact-type-icon2" title=""
                                        style="font-size: 22px; padding-right: 7px;"
                                        class=" la-2x"></i></div>
                        </div>
                        <div style="padding-left: 6%; padding-top: 4%; padding-right: 4%;"
                             class="text-left font-ibm black_letter_in_light_white_dark" id="show-contact-description">

                        </div>

                        @if(isset($contact) && $contact['start_date'] && $contact['end_date'])
                            <div class="" id="show-authorized-time">
                            <span class="contact-temporal-icon-show">
                            <ion-icon name="time-outline" style="zoom:2"></ion-icon>
                            Temporal
                            </span>
                                <div class="mt-1 contact-schedule">
                                    <div>
                                        <ion-icon name="alert-circle-outline"></ion-icon>
                                    </div>
                                    <div class="contact-schedule-text">
                                <span class="contact-schedule-date">
                                    <span>Desde:
                                        <b id="b-start-date">{{$contact['start_date']}}</b>
                                    </span>
                                    <span>Hasta:
                                        <b id="b-end-date">{{$contact['end_date']}}</b>
                                    </span>
                                </span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($scheduleText)
                            <div id="contact-schedule" class="mb-2">
                                <div class="contact-schedule mt-2 ">
                                    <div class="d-flex align-items-center">
                                        <ion-icon name="alert-circle-outline" style="min-width: 20px;"></ion-icon>
                                        <div class="contact-schedule-text">
                                            <span style="font-weight: 400; color: #82868B">Horario autorizado</span>
                                            <div class="d-flex text-left">
                                                @php echo $scheduleText; @endphp
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @if(isset($contact['description']))
                            <div class="text-left" style="padding: 0px 7%">
                                <span>{{$contact['description']}}</span>
                            </div>
                        @endif
                        @if(isset($contact['notes_2']))
                            <div class="text-left mt-1" style="padding: 0px 7%">
                                <span>{{$contact['notes_2']}}</span>
                            </div>
                        @endif
                        @if(isset($contact['notes_3']))
                            <div class="text-left mt-1" style="padding: 0px 7%">
                                <span>{{$contact['notes_3']}}</span>
                            </div>
                        @endif
                        <div id="globant_call_div" class="row text-left font-ibm" style="padding: 3%;">
                            <div class="col-md-12" style="color: #B9B9C3; padding-left: 8%">Llamar</div>
                            <div class="row" style="padding-top: 3%;">
                                <div class="d-flex" style="flex-direction: column;align-items: start;">
                                    @if(isset($contact) ? $contact['phone_mobile'] : false)
                                        <a id="show-contact-primary-phone"
                                           class="btn btn-outline-primary-show waves-effect"
                                           onclick="makeInteractionCall({{isset($contact) ? $contact['id'] : ''}} , '{{isset($contact) ? $contact['phone_mobile'] : ''}}')"
                                        >
                                            <i style="font-size: 18px;" class="la la-phone-volume">
                                            </i>
                                            {{isset($contact) ? $contact['phone_mobile'] : ''}}
                                        </a>
                                    @endif

                                    @if(isset($contact) ? $contact['phone_home'] : false)
                                        <a id="show-contact-phone_home"
                                           class="btn btn-outline-primary-show waves-effect"
                                           onclick="makeInteractionCall({{isset($contact) ? $contact['id'] : ''}}, '{{$contact['phone_home'] }}')"
                                        >
                                            <i style="font-size: 18px;" class="la la-phone-volume">
                                            </i>
                                            {{ $contact['phone_home'] }}
                                        </a>
                                    @endif
                                    @if(isset($contact) ? $contact['others_mobil_phones'] : false)
                                        @foreach($contact['others_mobil_phones'] as $phone)
                                            <a id="show-contact-others-mobil-phones-{{$phone}}"
                                               class="btn btn-outline-primary-show waves-effect"
                                               onclick="makeInteractionCall({{isset($contact) ? $contact['id'] : ''}} , '{{$phone}}')"
                                               style=" border-radius: 25px;">
                                                <i style="font-size: 18px; padding-right: 10px;"
                                                   class="la la-phone-volume">
                                                </i>
                                                {{$phone}}
                                            </a>
                                        @endforeach
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <a id="show-contact-secondary-phone"
                                       class="btn btn-outline-secondary-show waves-effect"
                                       style="border-radius: 25px; width: 100%; padding: 7px"></a>
                                </div>
                            </div>
                        </div>
                        <div class="row text-left font-ibm" style="padding: 1%;">
                            <div class="col-md-12" style="color: #B9B9C3; padding-left: 8%">Contactar</div>
                            <div class="row" style="padding-top: 3%;">
                                <div id="show-contact-email" class="btn btn-link col-md-8">
                                    <ion-icon
                                            style="pointer-events:none; padding-right: 3px; font-size: 18px!important;"
                                            name="mail-outline" class="md hydrated" role="img"
                                            aria-label="mail outline">
                                    </ion-icon>
                                    <a class="default_pointer_cs"
                                       href="mailto:{{isset($contact) ? $contact['email'] : ''}}"
                                       style="padding-left: 2px; font-size: 16px;">{{isset($contact) ? $contact['email'] : ''}}</a>
                                    </a>
                                </div>
                                <div class="btn btn-link col-md-4 text-right">
                                    <ion-icon
                                            style="pointer-events:none; padding-right: 3px; font-size: 18px!important;"
                                            name="chatbubble-ellipses-outline"></ion-icon>
                                    <a target="_blank" id="send_witty_button" style="font-size: 16px"
                                       href="/admin/send-wittybot-sms?building_id={{isset($building) ? $building['id'] : ""}}&flat_id={{isset($contact) ? $contact['flat_id'] : ""}}&contact_id={{ isset($contact) ? $contact['id']:""}}">Witty
                                        Bots
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="row text-left font-ibm" style="padding: 1%;">
                            <div class="col-md-12" style="color: #B9B9C3; padding-left: 8%">Actualizar ultima
                                interacción
                            </div>
                            <div class="row" style="padding-top: 3%;">
                                <button
                                        wire:click="updateComment('Autoriza')"
                                        class="btn btn-interactions btn-outline-primary-show {{ isset($selectedComment) && $selectedComment === 'Autoriza' ? 'selected-frase-in-comment-interaction' : '' }}"
                                        style="width: 17%; border-radius: 5px; padding: 7px; margin-left: 7%;">Autoriza
                                </button>
                                <button
                                        wire:click="updateComment('No autoriza')"
                                        class="btn btn-interactions btn-outline-primary-show  {{ isset($selectedComment) && $selectedComment === 'No autoriza' ? 'selected-frase-in-comment-interaction' : '' }}"
                                        style="width: 20%; border-radius: 5px; padding: 7px; margin-left: 2%;">No
                                    autoriza
                                </button>
                                <button
                                        wire:click="updateComment('Baja residente')"
                                        class="btn btn-interactions btn-outline-primary-show  {{ isset($selectedComment) && $selectedComment === 'Baja residente' ? 'selected-frase-in-comment-interaction' : '' }}"
                                        style="width: 25%; border-radius: 5px; padding: 7px; margin-left: 2%;">Baja
                                    residente
                                </button>
                                <button
                                        wire:click="updateComment('No atiende')"
                                        class="btn btn-interactions btn-outline-primary-show {{ isset($selectedComment) && $selectedComment === 'No atiende' ? 'selected-frase-in-comment-interaction' : '' }}"
                                        style="width: 20%; border-radius: 5px; padding: 7px; margin-left: 2%;">No
                                    atiende
                                </button>
                            </div>
                            <div style="padding-top: 2%;">
                                <input wire:model="interactionComment"
                                       class="form-control" type="text"
                                       style="width: 97%; height: 45px; padding: 3%; border-radius: 5px; margin-left: 4%;">
                            </div>


                            <div style="padding-top: 3%" class="row">

                                <a id="buton_to_edit_last_interaction_in_contact_popup_on_building_show"
                                   class="btn create_edit_back_color waves-effect"
                                   wire:click="updateLastInteraction()"
                                   style="width: 17%; border-radius: 5px; border-radius: 25px; padding: 7px; margin-left: 7%;">Guardar</a>
                                <button id="closeModalShowContact" type="button" data-dismiss="modal"
                                        style="visibility: hidden"></button>
                            </div>
                        </div>

                        <br>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

@push('after_scripts')
    <script>
        window.addEventListener('createInteractionCall', event => {
            let {id, phone_mobile, comment} = event.detail;

            if (!comment) comment = 'No hay descripción'

            phone_mobile = phone_mobile.toString();

            $.ajax({
                url: '/createiteractioncall/' + id + '/' + phone_mobile + '/' + comment,
                type: 'GET',
                dataType: "text",
                async: false,
                success: function () {
                    if (phone_mobile.substring(0, 1) == 9) {
                        window.location = "Tel:0" + phone_mobile;
                    } else {
                        window.location = "Tel:" + phone_mobile;
                    }
                },
            });
        });

        window.addEventListener('updateScheule', (data) => {
            addSchedule(data)
        })

        function addSchedule(data) {
            document.getElementById('contact-schedule').innerHTML = ""
            const div_container = document.createElement('div')
            div_container.classList.add('contact-schedule', 'mt-2')

            const div_ion = document.createElement('div')
            const ion = document.createElement('ion-icon')
            ion.setAttribute('name', 'alert-circle-outline')

            div_ion.appendChild(ion)

            div_container.appendChild(div_ion)

            const div_schedule_text = document.createElement('div')
            div_schedule_text.classList.add('contact-schedule-text')

            const span_horario_text = document.createElement('span')
            span_horario_text.style.fontWeight = "400"
            span_horario_text.style.color = "#82868B"
            span_horario_text.innerText = "Horario autorizado"

            const div_container_schedule = document.createElement('div')
            div_container_schedule.classList.add('d-flex', 'text-left')
            const span_fake_schedule = document.createElement('span')
            span_fake_schedule.innerHTML = `${data}`

            div_schedule_text.appendChild(span_horario_text)
            div_container_schedule.appendChild(span_fake_schedule)
            const span_button = document.createElement('span')
            span_button.setAttribute('type', 'button')
            span_button.setAttribute('data-toggle', 'modal')
            span_button.setAttribute('data-target', '#exampleModal')
            span_button.setAttribute('id', 'target-modal-sechedule')
            span_button.innerText = "Ver horarios"
            div_schedule_text.appendChild(div_container_schedule)
            div_schedule_text.appendChild(span_button)
            div_container.appendChild(div_schedule_text)

            document.getElementById('contact-schedule').appendChild(div_container)

            const modalSchedule = document.createElement('span')
            modalSchedule.innerHTML = `${data}`

            document.getElementById('modal-schedule').appendChild(modalSchedule)

        }

    </script>
@endpush
@push('after_styles')
    <style>
        .selected-frase-in-comment-interaction {
            background-color: #223A8F !important;
            color: white !important;
        }

        .create_edit_back_color {
            color: white !important;
            background-color: #223A8F !important;
        }

        .create_edit_back_color_dark {
            color: white !important;
            background-color: #666c7d !important;
        }

        .modal-backdrop.fade.show {
            background-color: #000000;
            z-index: 999;
        }

        #modal-container-popup-contact {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            width: 80%;
            max-width: 600px;
            padding: 0;
        }

        .dark-layout .btn-interactions {
            color: var(--main-text-color-dark);
        }

    </style>
@endpush
