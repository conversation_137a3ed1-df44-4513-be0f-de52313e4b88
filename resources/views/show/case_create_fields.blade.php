<style>
    .select2-container:nth-of-type(2) {
        display: none !important;
    }

    .select2-container--open .select2-dropdown {
        z-index: 9999999 !important;
    }

    .select2-container--default .select2-results {
        max-height: 200px;
        overflow-y: auto;
    }
</style>

<div class="modal-body row">
    <div class="special_color_of_select2_border form-group col-md-12 m-auto" style="padding-inline: 15px;">
        <div class="card">
            <div class="p-1 first_column_of_create_and_edit row col-md-12" style="margin: 0px; margin-left: 0px;">
                <div style="display: flex;padding: 0">
                    <ion-icon style="zoom: 2" class="titleTables form-title col-md-1 md hydrated" name="layers-outline"
                              role="img" aria-label="layers outline"></ion-icon>
                    <label class="titleTables"
                           style="color: #223A8F; font-size: 18px; font-weight: bold;">Categorización</label>
                </div>


                <div class="dropdown col-md-2" style="margin-left: -35%;">
                    <button class="btn btn-danger dropdown-toggle" type="button" id="state-dropdown"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span id="state-text_child">Pendiente</span>
                    </button>
                    <input type="text" id="state-value_child" name="state" value="pendiente" style="display:none;">
                    <div class="dropdown-menu" aria-labelledby="state-dropdown">
                        <a class="dropdown-item  btn-danger w-auto" href="#" data-value="pendiente"
                           onclick="changeButtonColor(this)">Pendiente</a>
                        <a class="dropdown-item btn-primary w-auto" href="#" data-value="en_curso"
                           onclick="changeButtonColor(this)">En curso</a>
                        <a class="dropdown-item btn-warning w-auto" href="#" data-value="solicitar_info"
                           onclick="changeButtonColor(this)">Solicitar info</a>
                        <a class="dropdown-item  btn-success w-auto" href="#" data-value="aviso_al_cliente"
                           onclick="changeButtonColor(this)">Aviso al cliente</a>
                        <a class="dropdown-item btn-secondary w-auto" href="#" data-value="finalizado"
                           onclick="changeButtonColor(this)">Finalizado</a>
                    </div>
                </div>


                <div class="col-md-12 my-1">
                    <div class="mr-sm-2" style="margin-bottom: 8px;">Categoria <span style='color:red!important'> *
                        </span></div>

                    <select name="select1_child" class="select2 mr-sm-2 white-field" id="select1_child"
                            onchange="getValueCategoryRelated(this)">
                        <?php
                        $array = \App\Models\Category::allCategoryFlatList();
                        ?>
                        @foreach ($array as $c)
                            @if ($c['name'] != 'Otros' and $c['name'] != 'Categoria Eliminada')
                                <option
                                    data="{{ $c['area_id'] }}|{{ $c['priority'] }}|{{ $c['description'] }}|{{ $c['case_title'] }}|{{ $c['case_description'] }}"
                                    value="{{ $c['id'] }}"
                                >
                                    {{ Str::limit($c['name'], 55) }}
                                </option>
                            @endif
                        @endforeach
                    </select>
                    <span style="display:none;color: red;align-items: center;" id="errorCategory"
                          class="mt-1 error-message-category-child">
                        <ion-icon name="information-circle-outline" style="color:red!important;"></ion-icon>Elige una
                        categoría para este caso
                    </span>

                    <?php
                    $array = \App\Models\Category::where('access_check', 1)->get();
                    $categoryWhitAccess = \App\Models\Category::where('access_check', 1)->pluck('id');
                    ?>

                </div>
                <div class="col-md-12 my-1" id="contAccessChild" style="display: none">
                    <div class="mr-sm-2" style="margin-bottom: 8px;">Accesos</div>
                    <select class="select2-custom mr-sm-2 white-field" id="select-case-access-child">
                        @foreach ($array as $c)
                            <option value="{{ $c->id }}">{{ $c->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <div class="case-msg-category" id="case-msg-category-cont-related" style="display: none">
                        <div class="sub-cont-msg">
                            <ion-icon name="information-circle-outline" style="zoom: 1.5; margin-left:2px"></ion-icon>
                            <span id="case-msg-category-related"></span>
                        </div>
                    </div>
                </div>
                <div class="row" style="padding: 10px;">
                    <div class="col-md-3">
                        <label>Prioridad</label>
                    </div>
                    <div class="form-check col-md-3">

                        <input class="form-check-input bool_fields_back" value="ALTA" type="radio"
                               name="flexRadioDefault" id="radio_alta" priority_radio_related="priority_radio_related">
                        <label class="form-check-label" for="radio_alta">
                            Alta
                        </label>
                    </div>
                    <div class="form-check col-md-3">
                        <input class="form-check-input bool_fields_back" value="MEDIA" type="radio"
                               name="flexRadioDefault" id="radio_media" checked
                               priority_radio_related="priority_radio_related">
                        <label class="form-check-label" for="radio_media">
                            Media
                        </label>
                    </div>
                    <div class="form-check col-md-3">
                        <input class="form-check-input bool_fields_back" value="BAJA" type="radio"
                               name="flexRadioDefault" id="radio_baja" priority_radio_related="priority_radio_related">
                        <label class="form-check-label" for="radio_baja">
                            Baja
                        </label>
                    </div>
                </div>


                <div class="form-group col-md-6" element="div"><label>Fecha limite</label>
                    <input type="datetime-local" name="limited_date_child" id="limited_date_child" column-order="1"
                           class="form-control">
                </div>

                <div class="form-group col-md-6" element="div">
                    <div>
                        <label>Recordar con anticipación</label>
                        <select id="warnings_days_related" style="width: 211px;height: 32px;border-radius: 4px;"
                                class="white-field">
                            <option value='null'>Sin recordatorio</option>
                            <option value='3d'>3 días antes</option>
                            <option value='2d'>2 días antes</option>
                            <option value='1d'>1 días antes</option>
                            <option value='4h'>4 horas antes</option>
                            <option value='2h'>2 horas antes</option>
                            <option value='30m'>30 min antes</option>
                            <option value='15m'>15 min antes</option>
                        </select>
                    </div>

                </div>

                <hr style="margin-top: 5%; margin-bottom: 5%; margin-left: -3%;">

                <div class="col-md-10">
                    <i style="padding: 10px;padding-left: 0px; color: rgb(34, 58, 143);"
                       class="titleTables la la-2x la-users"></i>
                    <label class="titleTables"
                           style="color: #223A8F; font-size: 18px; font-weight: bold;">Relacionado</label>
                </div>

                <div class="form-group col-md-12 white-field">
                    <div class="mr-sm-2" style="margin-bottom: 8px;">Asignar a</div>
                    <select name="area_view_child" class="select2 mr-sm-2 white-field" id="area_view_child"
                            onchange="changeResponsibles(this.value)">
                        @foreach (\App\Models\Area::all() as $area)
                            <option value="{{ $area->id }}">{{ $area->name }}</option>
                        @endforeach
                    </select>

                </div>

                <script></script>

                <div class="form-group col-md-12 white-field">
                    <div class="mr-sm-2" style="margin-bottom: 8px;">Responsable</div>
                    <select name="responsablec_child" class="custom-select mr-sm-2 white-field"
                            id="responsablec_child">
                    </select>
                </div>

                <div class="col-md-10">
                    <i style="padding: 10px;padding-left: 0px; color: rgb(34, 58, 143);"
                       class="titleTables la la-2x la-paste"></i>
                    <label class="titleTables"
                           style="color: #223A8F; font-size: 18px; font-weight: bold;">Descripción</label>
                </div>
                <div class="col-md-12 my-1">
                    <div style="margin-bottom: 8px;" class="mr-sm-2">
                        Titulo<span style='color:red!important'> *</span>
                    </div>
                    <input id="title_child" column-order="2" class="form-control" maxlength="254">
                    <span style="display:none; color: red;align-items: center;" id="errorTitulo"
                          class="mt-1 error-message-title-child">
                        <ion-icon name="information-circle-outline" style="color:red!important;"></ion-icon>Escribe un
                        título que resuma la solicitud
                    </span>

                </div>
                <div class="form-group col-md-12" element="div"><label></label>
                    <textarea name="description_child" id="description_child" column-order="2"
                              class="form-control"></textarea>
                </div>
                <div class="col-md-10">
                    <i style="padding: 10px;padding-left: 0px; color: rgb(34, 58, 143);font-size:25px"
                       class="titleTables la la-file-text-o"></i>
                    <label class="titleTables"
                           style="color: #223A8F; font-size: 18px; font-weight: bold; margin-bottom: 15px;">Reporte</label>
                </div>

                <div class="col-md-12 row pr-0">
                    <div class="col-md-7">
                        <b>Edificio <span style='color:red!important'> *</span></b>
                        <select id="case-relation-building" onchange="putUsersRelation(this.value)"></select>
                        <span style="display:none; color: red;align-items: center;" id="errorBuilding"
                              class="mt-1 error-message-title-child">
                        <ion-icon name="information-circle-outline" style="color:red!important;"></ion-icon>
                            Eliga un edificio
                    </span>
                    </div>

                    <div class="col-md-5 pr-0">
                        <b>Apartamento<span style='color:red!important'> *</span></b>
                        <select id="case-relation-flat" onchange="putUsersFlatsRelation()"></select>
                        <span style="display:none; color: red;align-items: center;" id="errorFlat"
                              class="mt-1 error-message-title-child">
                        <ion-icon name="information-circle-outline" style="color:red!important;"></ion-icon>
                             Eliga un apto
                    </span>
                    </div>

                    <div class="col-md-12 pr-0 mt-1">
                        <b>Contacto</b>
                        <select id="case-relation-contact" onchange="putUsersContactInfoRelation(this.value)"></select>
                    </div>
                    <div class="col-md-6 mt-1">
                        <b>Teléfono</b>
                        <input id="text_phone_number_relation" class="form-control">
                    </div>
                    <div class="col-md-6 pr-0 mt-1">
                        <b>Mail</b>
                        <input id="text_email_relation" class="form-control">
                    </div>

                </div>
                <div class="col-md-10">
                    <i style="padding: 10px;padding-left: 0px; color: rgb(34, 58, 143);"
                       class="titleTables la la-2x la-camera"></i>
                    <label class="titleTables"
                           style="color: #223A8F; font-size: 18px; font-weight: bold; margin-bottom: 15px;">Adjuntos</label>
                </div>
                <div class="form-group col-md-12 row" style="padding-left: 7%; padding-right: 0px;"
                     data-init-function="bpFieldInitUploadMultipleElement" data-field-name="images" element="div"
                     data-initialized="true">

                    <div style="margin-left:15px">
                        <input type="button" id="click-input" style="margin-left: -30px;" value="Adjuntar archivo"
                               onclick="document.getElementById('file-related').click();"/>
                        <label for="click-input" id="file-name-related" style="margin-left: -30px;">Sin
                            archivos</label>
                        <input type="file" style="display:none;" id="file-related" multiple name="images[]">
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@push('after_scripts')
<script>
    let currentUsersRelation = [];
    $case_info = @js($currentCase);
    let lastCategorySelected = null

    if ($case_info) {
        $('#case-relation-building').val($case_info?.building_id).trigger('change')
        $('#case-relation-flat').val($case_info?.flat_id).trigger('change')
    }

    async function loadFlatsRelation() {
        let building_id = $("#case-relation-building").val();

        if (building_id) {
            const res = await fetch('{{url('admin/building/')}}' + '/' + building_id + '/flats')
            if (res.ok) {
                const data = await res.json()
                $("#case-relation-flat").html(formatFlatsRelation(data));

                $('#case-relation-flat').select2({
                    allowClear: true,
                    placeholder: "Seleccionar un apartamento",
                    dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
                    width: '100%'
                });

                $('#case-relation-flat').val($case_info?.flat_id ?? '').change();
            }
        }
    }

    function loadContactCaseRelation() {
        $('#case-relation-contact').select2({
            allowClear: true,
            placeholder: "Seleccionar un contacto",
            dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
            width: '100%'
        });
        $('#case-relation-contact').val($case_info?.user_id ?? '').change();
    }

    function formatFlatsRelation(data) {
        let html;
        for (let i = 0; data.length > i; i++) {
            html += '<option value="' + data[i].id + '">' + data[i].number_with_tower + '</option>';
        }
        return html;
    }

    function putUsersRelation(value) {
        let email = document.getElementById('text_email_relation');
        let phone_number = document.getElementById('text_phone_number_relation');
        phone_number.innerHTML = '';
        phone_number.value = '';
        email.innerHTML = '';
        email.value = '';
        $('#case-relation-contact').empty()
        $('#case-relation-flat').empty()
        loadFlatsRelation(value)
        deleteSelectErrorRelated(value, 'errorBuilding');
    }

    function loadSelect2CaseAllBuildingRelated(buildings) {
        let select = $('#case-relation-building');
        select.empty();
        if (buildings) {
            let options = [];
            for (let i = 0; i < buildings.length; i++) {
                let option = new Option(buildings[i].building_number + '-' + buildings[i].name, buildings[i].id, true, true);
                options.push(option);
            }
            select.append(options).trigger('change');
        }

        $('#case-relation-building').select2({
            allowClear: true,
            placeholder: "Selecciona un edificio",
            dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
            width: '100%'
        });

        $('#case-relation-building').val($case_info?.building_id ?? '').change();
    }

    function putUsersFlatsRelation() {
        let building_id = $('#case-relation-building').val();
        let flat_id = $('#case-relation-flat').val();

        fetch('/admin/building/flats/' + flat_id + '/users')
            .then(async res => await res.json())
            .then(data => {
                $('#text_email_relation_flat').text('');
                $('#text_phone_number_relation_flat').text('');
                $('#case-relation-contact').empty();
                let value = ''
                const users = data.data
                for (const user of users) {
                    let opt = document.createElement('option');
                    opt.value = user.id;
                    value = user.id
                    opt.innerHTML = user.complete_name;
                    $('#case-relation-contact').append(opt);
                    currentUsersRelation.push(user)
                }

                loadContactCaseRelation();
                deleteSelectErrorRelated(value, 'errorFlat');
            })
    }

    async function getUsersByFlatsOrBuildingsRelation(building_id, flat_id) {
        const res = await fetch('/admin/building/' + building_id + '/flat/' + flat_id + '/users')
        if (res && res.ok) {
            const data = await res.json()
        } else {
            throw new Error('Error al obtener los usuarios')
        }
    }


    function putUsersContactInfoRelation(value) {
        let selectedUserId = value;
        if (selectedUserId) {
            let selectedUser = findUserById(currentUsersRelation, selectedUserId);
            if (selectedUser) {
                if (selectedUser.email) {
                    $('#text_email_relation').html(selectedUser.email);
                    $('#text_email_relation').text(selectedUser.email);
                    $('#text_email_relation').val(selectedUser.email);
                }
                if (selectedUser.phone_mobile) {
                    $('#text_phone_number_relation').html(selectedUser.phone_mobile);
                    $('#text_phone_number_relation').text(selectedUser.phone_mobile);
                    $('#text_phone_number_relation').val(selectedUser.phone_mobile);
                }
            }
        } else {
            clearPhoneAndEmailRelation();
        }
    }

    function clearPhoneAndEmailRelation() {
        $('#text_phone_number_relation').text('');
        $('#text_phone_number_relation').val('');
        $('#text_email_relation').text('');
        $('#text_email_relation').val('');
    }

    function findUserById(usersArray, targetId) {
        for (let i = 0; i < usersArray.length; i++) {
            if (usersArray[i].id == targetId) {
                return usersArray[i];
            }
        }
        return null;
    }

    function changeButtonColor(element) {
        const value = element.getAttribute('data-value');
        const button = document.getElementById('state-dropdown');
        button.className = 'btn ' + element.className;
        document.getElementById('state-text_child').innerText = element.innerText;
        document.getElementById('state-value_child').value = value;
    }

    function changeResponsibles(value) {
        getResponsables(value);
    }

    function putTextCategoryRelated(text) {
        let label = document.getElementById('case-msg-category-related');
        let cont = document.getElementById('case-msg-category-cont-related');
        if (text == undefined) {
            cont.style.display = 'none';
        } else if (text != '') {
            cont.style.display = 'block';
            text = text.replace(/\n/g, '<br>');
            label.innerHTML = text;
        } else {
            cont.style.display = 'none';
        }

    }

    let titleFieldChild = document.getElementById('title_child');

    titleFieldChild.addEventListener('keyup', (event) => {
        window.localStorage.setItem('caseChildTitle', event.target.value)
        let titleError = document.querySelector('.error-message-title-child');
        if (titleFieldChild.value) {
            $(titleError).css("display", "none");
        }
    });

    $('#description_child').on('keyup', (e) => {
        window.localStorage.setItem('caseChildDescription', e.target.value)
    })

    function getCaseTitleAndDescriptionChildFromLocalStorage() {
        const caseChildTitle = window.localStorage.getItem('caseChildTitle') ?? ''
        const caseChildDescription = window.localStorage.getItem('caseChildDescription') ?? ''

        return {caseChildTitle, caseChildDescription}
    }

    function putTextDefaultTitleAndDescriptionCaseRelated(title, description) {
        let titleCase = document.getElementById('title_child');
        let descriptionCase = document.getElementById('description_child');

        const {
            caseChildTitle,
            caseChildDescription
        } = getCaseTitleAndDescriptionChildFromLocalStorage()

        if (caseChildTitle == '' && title !== undefined) {
            titleCase.value = title ?? ''
        }

        if (caseChildDescription == '' && description !== undefined) {
            descriptionCase.value = description ?? ''
        }
    }

    function getValueCategoryRelated(currentValueSelectCategory) {
        if (!currentValueSelectCategory) return;
        let idCategory = currentValueSelectCategory.value;
        let selectedOption = currentValueSelectCategory.options[currentValueSelectCategory.selectedIndex];
        if (!selectedOption) return;
        let dataAttributes = selectedOption.getAttribute('data').split('|');
        let areaID = dataAttributes[0];
        let priority = dataAttributes[1];
        let description = dataAttributes[2];
        let titleCase = dataAttributes[3];
        let descriptionCase = dataAttributes[4];
        putTextCategoryRelated(description);
        putTextDefaultTitleAndDescriptionCaseRelated(titleCase, descriptionCase)

        $("#area_view_child").val(areaID).change();
        changePriorityRelated(priority);
        deleteSelectErrorRelated(currentValueSelectCategory, 'category-child');
        let category = <?php echo $categoryWhitAccess; ?>;

        for (let i = 0; i < category.length; i++) {
            if (category[i] == idCategory) {
                $('#contAccessChild').show();
            } else {
                $('#contAccessChild').hide();
            }
        }

    }

    function deleteSelectErrorRelated(value, fieldClass) {
        let titleError = document.querySelector('.error-message-' + fieldClass);
        if (value != '') {
            $(titleError).css("display", "none");
        }
    }

    function changePriorityRelated(priority) {
        let minInput = document.querySelectorAll('input[priority_radio_related="priority_radio_related"]');
        for (let i = 0; i < minInput.length; i++) {
            if (minInput[i].value == priority) {
                for (let j = 0; j < minInput.length; j++) {
                    if (minInput[j].checked) {
                        minInput[j].checked = false;
                    }
                }
                minInput[i].checked = true;
            }
        }
    }


    function loadSelect2CaseResponsableRelated(responsible) {
        let select = $('#responsablec_child');
        select.empty();
        if (responsible) {
            responsible.forEach(user => {
                let option = new Option(user.complete_name, user.id, true, true);
                select.append(option)
            })
        }
        $('#responsablec_child').val('').change();
    }

    function getResponsables(cat) {
        if (!cat) return;
        if (cat != lastCategorySelected) {
            lastCategorySelected = cat
            $('#responsablec_child').html('')
            fetch('/admin/area/' + cat + '/responsables')
                .then(async data => {
                    const responsables = await data.json()
                    loadSelect2CaseResponsableRelated(responsables)
                })
        }

    }

    function getAllBuildings() {
        let buidlings;
        $.ajax({
            url: '/api/building/all',
            type: 'GET',
            dataType: "json",
            async: false,
            success: function (data) {
                buidlings = data;
            },
        });
        return buidlings;
    }


    $(document).ready(function () {


        $('#exampleModalChildCaseCreate').on('shown.bs.modal', function () {
            if ($('#case-relation-building').data('select2')) {
                $('#case-relation-building').select2('destroy');
            }
            if ($('#case-relation-flat').data('select2')) {
                $('#case-relation-flat').select2('destroy');
            }
            if ($('#case-relation-contact').data('select2')) {
                $('#case-relation-contact').select2('destroy');
            }


            $('#case-relation-building').select2({
                allowClear: true,
                placeholder: "Selecciona un edificio",
                dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
                dropdownAutoWidth: true,
                minimumResultsForSearch: 5
            });

            $('#case-relation-flat').select2({
                allowClear: true,
                placeholder: "Seleccionar un apartamento",
                dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
                dropdownAutoWidth: true,
                minimumResultsForSearch: 5
            });

            $('#case-relation-contact').select2({
                allowClear: true,
                placeholder: "Seleccionar un contacto",
                dropdownParent: $('#exampleModalChildCaseCreate .modal-content'),
                dropdownAutoWidth: true,
                minimumResultsForSearch: 5
            });
        });
    });

    labelElementRelated = document.getElementById('file-name-related')

    inputElementRelated = document.getElementById('file-related')
    inputElementRelated.onchange = function (event) {
        let countFiles = inputElementRelated.files
        if (inputElementRelated.multiple) {
            if (countFiles.length == 1)
                labelElementRelated.innerHTML = countFiles.length + ' archivo'
            if (countFiles.length > 1)
                labelElementRelated.innerHTML = countFiles.length + ' archivos'
        }
    }

    function deleteSelectErrorRelated(value, errorId) {
        let titleError = document.getElementById(errorId);
        if (value != null) {
            $(titleError).css("display", "none");
        }
    }

</script>
@endpush
