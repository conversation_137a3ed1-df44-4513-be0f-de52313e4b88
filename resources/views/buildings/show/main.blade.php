@extends(backpack_view('layouts.top_left'))
@php
    $breadcrumbs = [
      trans('backpack::crud.admin') => backpack_url('dashboard'),
      trans('backpack::backup.backup') => false,
    ];
    $buildingIDName = $building->building_number . " - " . $building->name;
@endphp
@include('current-site-title', ['title' => $buildingIDName])

@section('content')
    <livewire:building-edit-button :building="$building"/>
    <div class="building-container">
        <div class="building-card-info scroll-content">
            @include('buildings.show.components.card-info.card_info')
        </div>
        <div class="building-contact-table scroll-content">
            @include('buildings.show.components.tables.contacts_table')
        </div>
        <div class="building-cases-table scroll-content card-minimal-style">
            <livewire:building-case :building="$building" wire:model.lazy/>
        </div>
        <div class="building-services-table scroll-content card-minimal-style">
            <livewire:building-service :building="$building" wire:model.lazy/>
        </div>
    </div>

@endsection

@push('after_styles')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet"/>

    <style>

        html .content.app-content {
            padding: 1.5rem !important;
        }

        .card-minimal-style {
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }

        .loader-cell {
            height: 100%;
            vertical-align: middle;
            text-align: center;
            position: relative;
        }

        .loader {
            display: inline-block;
            width: 48px;
            height: 48px;
            border: 4px solid transparent;
            border-radius: 50%;
            border-top-color: blue;
            animation: spin 1s linear infinite;
        }

        .building-container {
            display: grid;
            grid-template-rows: 1fr 0.5fr 1fr;
            grid-template-columns: 1fr 1.2fr 1fr;
            height: 96vh;
            gap: 1rem;
            background: #f0f4f9;
            padding: 8rem 1rem 0.5rem 1rem;
            grid-template-areas:
        "building-card-info building-contact-table building-contact-table"
        "building-card-info building-contact-table building-contact-table"
        "building-card-info building-cases-table building-services-table"
        "building-card-info building-cases-table building-services-table";

        }

        .dark-layout .building-container {
            background-color: #0d0d18;
        }


        .building-card-info {
            grid-area: building-card-info;
            height: 100%;
        }

        .building-contact-table {
            grid-area: building-contact-table;
            background: white;
            border-radius: 6px;
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
        }

        .building-cases-table {
            grid-area: building-cases-table;
            overflow-y: auto;
            background-color: white;
        }

        .building-services-table {
            grid-area: building-services-table;
            overflow-y: auto;
            background-color: white;
        }

    </style>
@endpush
@push('after_scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

    <script>
        // todo:ver wire click change tab
        document.addEventListener('DOMContentLoaded', function () {
            const loaderContactContainer = document.getElementById('loader-tbody');
            const contentContactContainer = document.getElementById('data-tbody');
            const contentCasesContainer = document.getElementById('data-tbody-cases');
            const loaderCasesContainer = document.getElementById('loader-tbody-cases');
            const contentServicesContainer = document.getElementById('data-tbody-services');
            const loaderServicesContainer = document.getElementById('loader-tbody-services');


            Livewire.on('loadingStarted', () => {
                loaderCasesContainer.classList.remove('d-none');
                loaderContactContainer.classList.remove('d-none');
                loaderServicesContainer.classList.remove('d-none');
                contentContactContainer.classList.add('d-none');
                contentCasesContainer.classList.add('d-none');
                contentServicesContainer.classList.add('d-none');

            });

            Livewire.on('loadingFinished', () => {
                loaderContactContainer.classList.add('d-none');
                contentContactContainer.classList.remove('d-none');

                loaderCasesContainer.classList.add('d-none');
                contentCasesContainer.classList.remove('d-none');

                loaderServicesContainer.classList.add('d-none');
                contentServicesContainer.classList.remove('d-none');

            });

        });


        window.addEventListener('popstate', () => {
            const url = window.location.pathname.split('/')
            if (url.filter(item => item == 'building')) {
                const buildingId = url[url.length - 2]
                $('#searchBuilding').val(buildingId).trigger('change')
            }
        })


        $('.footer').css('display', 'none');

    </script>
@endpush
