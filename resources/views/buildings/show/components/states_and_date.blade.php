@php
    $statesItems = \App\Models\Building::BUILDING_STATE;
    $currentStateBuilding = $statesItems[$building->building_state] ?? '';
@endphp
@if(($currentStateBuilding != '' && $currentStateBuilding['text'] != 'Activo') && !empty($currentStateBuilding) && isset($currentStateBuilding['text']))
    <div style="display: flex;flex-direction: row;justify-content: space-between;padding-bottom: 10px;">
        @if($currentStateBuilding != '' && $currentStateBuilding['text'] != 'Activo')
            <div class="building-state-show">
                <button type="button" class="building-state-button open-sans"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                        style=";background: var({{$currentStateBuilding['color']}})">
                    {{$currentStateBuilding['text']}}
                </button>
            </div>
        @endif
        @if(!empty($currentStateBuilding) && isset($currentStateBuilding['text']))
            @include('vendor.backpack.crud.fields.estimated_start_date_building',['data' => $currentStateBuilding])
        @endif
    </div>
@endif
