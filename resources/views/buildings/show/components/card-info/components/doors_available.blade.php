<div class="door-available-container">
    @include('buildings.show.components.modals.building-info-card-service-type-comments', ['$building' => $building])
    @include('buildings.show.components.modals.building-info-card-products-comments', ["$building" => $building, "videogarage" => $videogarage])
    <div class="doors-list  @if(count($towers) > 0 ) doors-list-border @endif"
         style="padding: @if(count($towers) > 0) 8px @endif; border-radius: @if(count($multipleAddressNoTower) > 0) 0px; border-top-left-radius: 8px;  border-bottom-left-radius: 8px; @endif;">
        <div class="scroll-content "
             style=" height: @if(count($towers) > 0 || count($multipleAddressNoTower) > 2) 118px @endif; overflow-y: auto">
            @if(count($towers) == 0 || !isset($building->doors_quantity))
                    @if($building->doors_quantity && count($address) < 2)
                        <div class="doors-list-item" style="padding: @if(count($towers) == 0) 1.25rem; @endif; height: 90px;">
                            <div class="doors-list-item-info">
                                <div class="doors-list-item-address">{{$street == ' ' ? '.....' : $street}}</div>
                                <div class="doors-list-item-details">{{$between == '' ? '.....' : $between}}</div>
                            </div>
                            <div class="doors-list-item-action">
                                @if($building->doors_quantity)
                                    <span>{{$building->doors_quantity}} @if($building->doors_quantity > 1 || $building->doors_quantity == 0)
                                            Puertas
                                        @else
                                            Puerta
                                        @endif</span>
                                    <ion-icon name="enter-outline" style="zoom: 1.2"></ion-icon>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="doors-list-item-multiple-address-container">
                            @for($i = 0; $i < count($multipleAddressBetweenNoTower); $i++)
                                <div class="doors-list-item-multiple-address">
                                    <div class="doors-list-item-info">
                                        <div
                                            class="doors-list-item-address ml-2">{{$multipleAddressNoTower[$i] ?? '----'}}
                                        </div>
                                        <div class="doors-list-item-details ml-2">
                                            esq {{$multipleAddressBetweenNoTower[$i] ?? '----'}}
                                        </div>
                                    </div>
                                    <div class="doors-list-item-action">
                                        @if($building->doors_quantity)
                                            <span>{{$building->doors_quantity}} @if($building->doors_quantity > 1 || $building->doors_quantity == 0)
                                                    Puertas
                                                @else
                                                    Puerta
                                                @endif</span>
                                            <ion-icon name="enter-outline" style="zoom: 1.2"></ion-icon>
                                        @endif
                                    </div>
                                </div>
                                <div class="hover-separator" ></div>
                            @endfor
                        </div>
                    @endif
            @else
                <div class="doors-list-item-container">
                    @foreach($towers as $tower)
                        <div class="doors-list-item">
                            <div class="doors-list-item-tower">{{strtoupper($tower->tower_denomination)}}</div>
                            <div class="doors-list-item-info">
                                <div
                                    class="doors-list-item-address doors-list-item-address-text">{{isset($tower->tower_address) && trim($tower->tower_address) != "" ? $tower->tower_address : $street ?? '----'}}</div>
                                <div
                                    class="doors-list-item-details doors-list-item-details-text">{{isset($tower->tower_corner) && trim($tower->tower_corner) != "" ? $tower->tower_corner : $between ?? '----'}}</div>
                            </div>
                            <div class="doors-list-item-action">
                                <span>{{!$tower->doors_quantity ? 0 : $tower->doors_quantity}} @if($tower->doors_quantity > 1 || $building->doors_quantity == 0)
                                        Puertas
                                    @else
                                        Puerta
                                    @endif</span>
                                <ion-icon name="enter-outline" style="zoom: 1.2"></ion-icon>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>

    <div class="doors-buttons" >

        <button class="doors-button-item icon-container-service-types" data-schedule="{{$building->getFakeSchedule()}}"
                style="padding: @if(count($towers) > 0) 8px 1rem @else 7px 24px; @endif; height: 50%">
            @if($building->service_type == 'Portería')
                <span class="porter-svg">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M4 7.11579C4 3 6.44017 2.12329 9.24669 2.12329L12.893 2.12329C15.6995 2.12329 18 3 18 7.11579V18.2102C18 22 15.6995 23.2027 12.893 23.2027H9.24669C6.44017 23.2027 4 22 4 18.2102V7.11579Z"
                              fill="#223A8F"/>
                        <circle cx="16.5" cy="4.5" r="3.5" fill="#41BA77"/>
                    </svg>
                </span>
            @elseif($building->service_type == 'Híbrido')
                <img id="service-type-icon-hibrido" src="" alt="icon"/>
            @elseif($building->service_type == 'Vigilancia')
                <ion-icon wire:ignore size="small" style="pointer-events:none; margin-top: 3px;"
                          name="videocam-outline"
                          role="img"
                          aria-label="videocam outline">
                </ion-icon>
            @else
                <ion-icon wire:ignore size="small" style="pointer-events:none; margin-top: 3px" name="key-outline"
                          role="img"
                          aria-label="key outline">
                </ion-icon>
            @endif
            {{$building->service_type}}
        </button>

        <button class="doors-button-item icon-container-products"
                style="padding: @if(count($towers) > 0) 12px @else 7px 24px @endif; height: 50%">
            <ion-icon name="information-circle" style="zoom: 1.2; "></ion-icon>
            Productos
        </button>

    </div>
</div>

<script>
    const productsData = document.querySelector('.icon-container-products');
    const serviceTypeData = document.querySelector('.icon-container-service-types');

    productsData?.addEventListener('mouseover', function (event) {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardProductsModal');
        const rect = productsData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
    });

    productsData?.addEventListener('mouseout', function (event) {
        document.getElementById('buildingCardProductsModal').style.display = 'none';
    });

    serviceTypeData?.addEventListener('mouseover', function (event) {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardServiceTypeModal');
        const rect = serviceTypeData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        const announcements = event.target.closest('.icon-container-service-types').dataset.schedule;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
    });

    serviceTypeData?.addEventListener('mouseout', function (event) {
        document.getElementById('buildingCardServiceTypeModal').style.display = 'none';
    });
</script>
