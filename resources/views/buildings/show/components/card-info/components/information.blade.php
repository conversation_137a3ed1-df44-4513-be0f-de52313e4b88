<div class="people-rules-container">
    @include('buildings.show.components.modals.building-info-card-info-comments')
    @include('buildings.show.components.modals.building-information-commercial-locals-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-information-bathrooms-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-information-medidores-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-information-azotea-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-information-others-alarms-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-information-fire-alarms-comments', ['building' => $building])
    <div class="accordion" id="accordionExample">
        <div class="accordion-item" style="background: transparent !important;">
            <h2 class="accordion-header accordion-transparent" id="headingOne">
                <button class="accordion-button font-weight-bold" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseBuildingInformation" aria-expanded="true"
                        aria-controls="collapseBuildingInformation">
                    <ion-icon class="mr-3" size="small" name="information-circle" wire:ignore></ion-icon>
                    Información del Edificio
                </button>
            </h2>
            <div id="collapseBuildingInformation" class="accordion-collapse collapse" aria-labelledby="headingOne">
                <div class="accordion-body accordion-transparent">
                    <div class="scroll-content"
                         style="background: transparent !important;  overflow-y: auto; overflow-x: visible;">
                        <div style="overflow-y: auto; overflow-x: visible; gap: 16px;"
                             class="d-flex flex-column scroll-content">
                            <div class="overflow-visible">
                                <div class="d-flex flex-column" style="gap: 16px;  overflow-x: visible">

                                    <div class="d-flex " style="gap: 16px; overflow: visible">
                                        <div
                                            class="icon-container-commercial-locals accordion-information-equipments-buttons"
                                            data-comerciallocals="{{$building->commercial_locals_comments ?? "[]"}}">
                                            <ion-icon name="storefront-outline" size="small" wire:ignore></ion-icon>
                                            Locales comerciales
                                        </div>
                                        <div
                                            class="icon-container-bathrooms accordion-information-equipments-buttons"
                                            data-bathrooms="{{$building->bathrooms_comments ?? "[]"}}">
                                            <ion-icon name="water-outline" size="small" wire:ignore></ion-icon>
                                            Baño
                                        </div>
                                        <div
                                            class="icon-container-medidores accordion-information-equipments-buttons"
                                            data-medidores="{{$building->medidores_comments ?? "[]"}}">
                                            <ion-icon name="speedometer-outline" size="small" wire:ignore></ion-icon>
                                            Medidores
                                        </div>
                                    </div>
                                    <div class="d-flex " style="gap: 16px; overflow: visible">
                                        <div
                                            class="icon-container-rooftop accordion-information-equipments-buttons"
                                            data-rooftop="{{$building->rooftop_comments ?? "[]"}}">
                                            <ion-icon name="push-outline" size="small" wire:ignore></ion-icon>
                                            Azotea
                                        </div>
                                        <div
                                            class="icon-container-fire-alarms accordion-information-equipments-buttons"
                                            data-firealarms="{{$building->fire_alarm_comments ?? "[]"}}">
                                            <ion-icon name="flame-outline" size="small" wire:ignore></ion-icon>
                                            Alarma incendio
                                        </div>
                                        <div
                                            class="icon-container-others-alarms accordion-information-equipments-buttons"
                                            data-othersalarms="{{$building->others_alarms_comments ?? "[]"}}">
                                            <ion-icon name="megaphone-outline" size="small" wire:ignore></ion-icon>
                                            Otras alarmas
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex flex-column" style="color:black !important;">
                                <ul>
                                    @foreach($aditionsInstructions as $instruction)
                                        <li class="total-apartments-and-contacts">{{$instruction['comment']}}</li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="d-flex align-items-center" style="gap: 16px;">
                                <div>
                                    <ion-icon class="building-information-flats" name="business" size="small" wire:ignore></ion-icon>
                                </div>
                                @if($totalFlats > 0)
                                    <div class="total-apartments-and-contacts">Edificio con <span
                                            class="building-flats-apartments">{{$totalFlats}}</span> apartamentos y
                                        <span
                                            class="building-flats-apartments">{{$totalContacts}}</span> contactos
                                    </div>
                                @else
                                    <span>----</span>
                                @endif
                            </div>
                            <div class="d-flex align-items-center" style="gap: 8px; margin-right: 10px;">
                                <div class="align-self-start">
                                    <div style="margin-top: 3px;">
                                        <ion-icon class="building-information-flats" name="information-circle" size="small" wire:ignore></ion-icon>
                                    </div>
                                </div>
                                <div style="padding-left: 8px; padding-top: 4px;"
                                     class="col-md-11 text-left " title="Informacion de apartamentos">
                                    <span class="total-apartments-and-contacts">
                                        @if($totalFlats > 0)
                                            {{ implode(', ', explode(',', $building_flats_list)) }}
                                        @else
                                            <span>----</span>
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('after_scripts')
    <script>
        const comercialLocalsData = document.querySelector('.icon-container-commercial-locals');
        const bathroomsData = document.querySelector('.icon-container-bathrooms');
        const medidoresData = document.querySelector('.icon-container-medidores');
        const rooftopData = document.querySelector('.icon-container-rooftop');
        const fireAlarmsData = document.querySelector('.icon-container-fire-alarms');
        const othersAlarmsData = document.querySelector('.icon-container-others-alarms');

        comercialLocalsData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationCommercialLocalsModal');
            const rect = comercialLocalsData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        comercialLocalsData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationCommercialLocalsModal').style.display = 'none';
        });

        medidoresData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationMedidoresModal');
            const rect = medidoresData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        medidoresData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationMedidoresModal').style.display = 'none';
        });

        rooftopData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationAzoteaModal');
            const rect = rooftopData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        rooftopData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationAzoteaModal').style.display = 'none';
        });

        othersAlarmsData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationOthersAlarmsModal');
            const rect = othersAlarmsData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        othersAlarmsData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationOthersAlarmsModal').style.display = 'none';
        });

        bathroomsData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationBathroomsModal');
            const rect = bathroomsData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        bathroomsData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationBathroomsModal').style.display = 'none';
        });

        fireAlarmsData?.addEventListener('mouseenter', function () {
            const menuCollapsed = document.body.classList.contains('menu-collapsed')
            const gestionesModal = document.getElementById('buildingCardInformationFireAlarmsModal');
            const rect = fireAlarmsData.getBoundingClientRect()
            const offsetTop = rect.top;
            const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
            gestionesModal.style.left = `${offsetLeft}px`;
            gestionesModal.style.top = `${offsetTop}px`;
            gestionesModal.style.display = 'block';
            const viewportHeight = window.innerHeight;
            const rectGestionesModal = gestionesModal.getBoundingClientRect()
            if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
            }
        });

        fireAlarmsData?.addEventListener('mouseout', function () {
            document.getElementById('buildingCardInformationFireAlarmsModal').style.display = 'none';
        });
    </script>
@endpush

