<div class="people-rules-container">
    @include('buildings.show.components.modals.building-announcements-comments')
    <div class="accordion" id="accordionExample">
        <div class="accordion-item" style="background: transparent !important; overflow: visible">
            <h2 class="accordion-header accordion-transparent" id="headingOne">
                <button class="accordion-button font-weight-bold" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseBuildingAnnouncements" aria-expanded="true"
                        aria-controls="collapseBuildingAnnouncements">
                    <ion-icon size="small" class="mr-3" name="megaphone" wire:ignore></ion-icon>
                    Comunicados
                </button>
            </h2>
            <div id="collapseBuildingAnnouncements" class="accordion-collapse collapse show"
                 aria-labelledby="headingOne">
                <div class="accordion-body accordion-transparent scroll-content"
                     style="overflow-y:auto; max-height: 300px;">
                    <div style="overflow: visible; max-height: 150px;">
                        <div class="d-flex gap-1" style="background: transparent !important; ">
                            <div class="flex-grow-1">

                                @forelse($announcements as $case)
                                    <div class="d-flex announcement-container cursor-pointer"
                                         data-case_id="{{$case->id}}" style="margin-bottom: 0.5rem;"
                                         onclick="window.open(`/admin/case/{{$case->id}}/info`, '_blank')">

                                        <div
                                            class="d-flex justify-content-center align-items-center building-announcements-icon">
                                            <i class="las la-bullhorn" style="font-size: 20px;"></i>
                                        </div>
                                        <div class="announcement-item">
                                            <div
                                                class="announcement-title icon-container-announcement pl-3 total-apartments-and-contacts truncate flex-grow-1"
                                                data-announcements="{{$case->description}}"
                                                data-announcement_title="{{$case->title ?? 'Sin título'}}">{{$case->title ?? "Sin título"}}</div>
                                            <a href="/admin/case/{{$case->id}}/info" target="_blank"
                                               class="justify-end mr-2">
                                                <i class="las la-external-link-alt" style="font-size: 20px;"></i>
                                            </a>
                                        </div>
                                    </div>
                                @empty
                                    <div
                                        class="d-flex align-items-center container-no-particularidades total-apartments-and-contacts">
                                        <ion-icon name="information-circle-outline" wire:ignore size="small"></ion-icon>
                                        No hay comunicados
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('after_scripts')
    @once
        <script>
            const announcementsAccordion = document.querySelectorAll(`[id*=collapseBuildingAnnouncements]`)
            announcementsAccordion.forEach((announcementCard) => {
                announcementCard.addEventListener('mouseenter', () => {
                    const tab = window.localStorage.getItem('currentTab') ?? 'people'
                    const announcementsData = document.querySelectorAll(`[id=tab-${tab}] [id=collapseBuildingAnnouncements] .icon-container-announcement`)
                    announcementsData?.forEach(item => {
                        item.addEventListener('mouseenter', function (event) {
                            const menuCollapsed = document.body.classList.contains('menu-collapsed')
                            const tab = window.localStorage.getItem('currentTab') ?? 'people'
                            const currentTab = `tab-${tab}`
                            const rect = item.getBoundingClientRect()
                            const gestionesModal = document.querySelector(`[id=${currentTab}] [id*=buildingAnnouncementsModal]`);
                            const offsetTop = rect.top + 40;
                            const offsetLeft = menuCollapsed ? event.clientX : event.clientX - 200

                            const announcements = event.target.closest('.icon-container-announcement').dataset.announcements.replace(/\n/g, '<br>');
                            const gestionesDetail = document.querySelector(`[id=${currentTab}] [id*=buildingAnnouncementsDetails]`)
                            gestionesDetail.innerHTML = ''
                            gestionesModal.style.left = `${offsetLeft}px`;
                            gestionesModal.style.top = `${offsetTop}px`;
                            const title = document.querySelector(`[id=${currentTab}] [id*=building-announcements-title]`)
                            title.innerHTML = ''
                            const titleSpan = document.createElement('span')
                            titleSpan.innerHTML = item.dataset.announcement_title
                            title.appendChild(titleSpan)
                            if (announcements.trim().length > 0) {
                                const infoSpan = document.createElement('span')
                                infoSpan.classList.add('hovers-text')
                                infoSpan.innerHTML = announcements
                                gestionesDetail.appendChild(infoSpan)
                            } else {
                                const infoSpan = document.createElement('span')
                                infoSpan.classList.add('hovers-text')
                                infoSpan.innerHTML = '----'
                                gestionesDetail.appendChild(infoSpan)
                            }
                            gestionesModal.style.display = 'block';
                            const viewportHeight = window.innerHeight;
                            const rectGestionesModal = gestionesModal.getBoundingClientRect()
                            if (rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
                                gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight - 50}px`;
                            }
                        });

                        item.addEventListener('mouseout', function () {
                            const tab = window.localStorage.getItem('currentTab') ?? 'people'
                            document.querySelector(`[id=tab-${tab}] [id*=buildingAnnouncementsModal]`).style.display = 'none';
                        })
                    });
                })
            })
        </script>
    @endonce
@endpush
