<div class="equipment-container">
    @include('buildings.show.components.modals.building-equipment-intercoms-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-equipment-speakers-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-equipment-integrated_alarms-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-equipment-phones-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-equipment-internet-contract-comments', ['building' => $building])
    @include('buildings.show.components.modals.building-equipment-sirens-comments', ['building' => $building])
    <div class="accordion" id="accordionExample">
        <div class="accordion-item" style="background: transparent !important; overflow: visible;">
            <h2 class="accordion-header accordion-transparent" id="headingOne">
                <button class="accordion-button font-weight-bold" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseBuildingEquipment" aria-expanded="true"
                        aria-controls="collapseBuildingEquipment">
                    <ion-icon class="mr-3" size="small" name="camera" wire:ignore></ion-icon>
                    Equipamiento y Datos de Foxsys
                </button>
            </h2>
            <div id="collapseBuildingEquipment" class="accordion-collapse collapse" aria-labelledby="headingOne">
                <div class="accordion-body accordion-transparent">
                    <div class="scroll-content"
                         style="background: transparent !important; overflow-y: auto; overflow-x: visible;">
                        <div style="overflow-y: auto; overflow-x: visible; gap: 16px;"
                             class="d-flex flex-column scroll-content">
                            <div class="d-flex flex-column" style="gap: 16px; overflow: visible; ">

                                <div class="d-flex" style="gap: 16px;">
                                    <div
                                        class="icon-container-intercoms accordion-information-equipments-buttons"
                                        data-intercoms="{{$intercoms ?? "[]"}}">
                                        <ion-icon name="id-card-outline" size="small" wire:ignore></ion-icon>
                                        Intercomunicadores
                                    </div>
                                    <div
                                        class="icon-container-phones accordion-information-equipments-buttons"
                                        data-phones="{{$building->phones_comments ?? "[]"}}">
                                        <ion-icon name="call-outline" size="small" wire:ignore></ion-icon>
                                        Teléfonos
                                    </div>
                                    <div
                                        class="icon-container-internet-contract accordion-information-equipments-buttons"
                                        data-internetcontract="{{$building->number_of_internet_contrat ?? "[]"}}">
                                        <ion-icon name="wifi-outline" size="small" wire:ignore></ion-icon>
                                        Contrato Internet
                                    </div>
                                </div>
                                <div class="d-flex" style="gap: 16px;">
                                    <div
                                        class="icon-container-speakers accordion-information-equipments-buttons"
                                        data-speakers="{{$building->speakers_comments ?? "[]"}}">
                                        <ion-icon name="volume-high-outline" size="small" wire:ignore></ion-icon>
                                        Altoparlantes
                                    </div>
                                    <div
                                        class="icon-container-sirens accordion-information-equipments-buttons"
                                        data-sirens="{{$building->sirens_comments ?? "[]"}}">
                                        <ion-icon name="barcode-outline" size="small" wire:ignore></ion-icon>
                                        Sirenas y Barreras
                                    </div>
                                    <div
                                        class="icon-container-integrated-alarms accordion-information-equipments-buttons"
                                        data-integratedalarms="{{$building->integrated_alarms_comments ?? "[]"}}">
                                        <ion-icon name="shield-checkmark-outline" size="small" wire:ignore></ion-icon>
                                        Alarmas integradas
                                    </div>
                                </div>
                            </div>
                            @if($building->warranty_date->equipament)
                                <div class="d-flex align-items-center total-apartments-and-contacts" style="gap: 16px;">
                                    <div style="color: #4B4B4B;">
                                        <ion-icon name="construct-outline" size="small" wire:ignore></ion-icon>
                                    </div>
                                    <span class="d-block total-apartments-and-contacts">Audio: <span
                                            style="font-weight: bold;">{{$building->warranty_date->equipament}}</span>
                                    @if($dateToShow)
                                            Garantía hasta {{$dateToShow}}
                                        @endif
                                    </span>
                                    @if($dateToShow)
                                        <span
                                            class="ml-auto pr-4 @if($isValid) audio-valid @else audio-invalid @endif">{{$isValid ? 'Vigente' : 'Vencido'}}</span>
                                    @endif
                                </div>
                            @endif
                            @if($building->warranty_date->other)
                                <div class="d-flex align-items-center total-apartments-and-contacts"
                                     style="gap: 16px; ">
                                    <div style="color: #4B4B4B;">
                                        <ion-icon name="construct-outline" size="small" wire:ignore></ion-icon>
                                    </div>
                                    <span class="d-block total-apartments-and-contacts">Otro: <span
                                            style="font-weight: bold;">{{$building->warranty_date->other}}</span>
                                    @if($dateToShow)
                                            Garantía hasta {{$dateToShow}}
                                        @endif
                                    </span>
                                    @if($dateToShow)
                                        <span
                                            class="ml-auto pr-4 @if($isValid) audio-valid @else audio-invalid @endif">{{$isValid ? 'Vigente' : 'Vencido'}}</span>
                                    @endif
                                </div>
                            @endif
                            <div class="d-flex align-items-center total-apartments-and-contacts" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="checkmark-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">Inicio de servicio: <span
                                        style="font-weight: bold;">{{$building->service_start_date ?  \Carbon\Carbon::parse($building->service_start_date)->format('d/m/y') : '----'}}</span>
                                </span>
                            </div>
                            <div class="d-flex align-items-center total-apartments-and-contacts" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="briefcase-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">Ejecutivo Comercial: <span
                                        style="font-weight: bold;">{{$building->operator_id ?  $operator : '----'}}</span>
                                </span>
                            </div>
                            <div class="d-flex align-items-center" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="document-text-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->order_job)) > 0)
                                        <a target="_blank"
                                           href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->order_job)}}">Orden de trabajo</a>
                                    @else
                                        <span>Orden de trabajo</span>
                                    @endif</span>
                            </div>
                            <div class="d-flex align-items-center" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="checkmark-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->order_job_checked)) > 0)
                                        <a target="_blank"
                                           href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->order_job_checked)}}">Orden de trabajo revisada</a>
                                    @else
                                        <span>Orden de trabajo revisada</span>
                                    @endif</span>
                            </div>
                            <div class="d-flex align-items-center" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="document-attach-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->contract)) > 0)
                                        <a target="_blank"
                                           href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->contract)}}">Contrato</a>
                                    @else
                                        <span>Contrato</span>
                                    @endif</span>
                            </div>
                            <div class="d-flex align-items-center" style="gap: 16px;">
                                <div style="color: #4B4B4B;">
                                    <ion-icon name="shield-outline" size="small" wire:ignore></ion-icon>
                                </div>
                                <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->rev_security))> 0)
                                        <a target="_blank"
                                           href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->rev_security)}}">Relevamiento de seguridad</a>
                                    @else
                                        <span>Relevamiento de seguridad</span>
                                    @endif</span>
                            </div>
                            @if(isset($building->rev_security2))
                                <div class="d-flex align-items-center" style="gap: 16px;">
                                    <div style="color: #4B4B4B;">
                                        <ion-icon name="shield-outline" size="small" wire:ignore></ion-icon>
                                    </div>
                                    <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->rev_security2))> 0)
                                            <a target="_blank"
                                               href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->rev_security2)}}">Relevamiento de seguridad 2</a>
                                        @else
                                            <span>Relevamiento de seguridad 2</span>
                                        @endif</span>
                                </div>
                            @endif
                            @if(isset($building->rev_security3))
                                <div class="d-flex align-items-center" style="gap: 16px;">
                                    <div style="color: #4B4B4B;">
                                        <ion-icon name="shield-outline" size="small" wire:ignore></ion-icon>
                                    </div>
                                    <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->rev_security3))> 0)
                                            <a target="_blank"
                                               href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->rev_security3)}}">Relevamiento de seguridad 3</a>
                                        @else
                                            <span>Relevamiento de seguridad 3</span>
                                        @endif</span>
                                </div>
                            @endif
                            @if(isset($building->rev_security3))
                                <div class="d-flex align-items-center" style="gap: 16px;">
                                    <div style="color: #4B4B4B;">
                                        <ion-icon name="shield-outline" size="small" wire:ignore></ion-icon>
                                    </div>
                                    <span class="d-block total-apartments-and-contacts">@if(strlen(trim($building->rev_security4))> 0)
                                            <a target="_blank"
                                               href="{{\Illuminate\Support\Facades\Storage::disk('uploads')->url($building->rev_security4)}}">Relevamiento de seguridad 4</a>
                                        @else
                                            <span>Relevamiento de seguridad 4</span>
                                        @endif
                                    </span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const intercomsData = document.querySelector('.icon-container-intercoms');
    const phonesData = document.querySelector('.icon-container-phones');
    const internetContractData = document.querySelector('.icon-container-internet-contract');
    const speakersData = document.querySelector('.icon-container-speakers');
    const sirensData = document.querySelector('.icon-container-sirens');
    const integratedAlarmsData = document.querySelector('.icon-container-integrated-alarms');

    const menuCollapsed = window.localStorage.getItem('menuCollapsed') ?? false

    intercomsData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentIntercomsModal');
        const rect = intercomsData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    intercomsData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentIntercomsModal').style.display = 'none';
    });

    internetContractData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentInternetContractModal');
        const rect = internetContractData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    internetContractData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentInternetContractModal').style.display = 'none';
    });

    speakersData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentSpeakersModal');
        const rect = speakersData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    speakersData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentSpeakersModal').style.display = 'none';
    });

    integratedAlarmsData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentIntegratedAlarmsModal');
        const rect = integratedAlarmsData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    integratedAlarmsData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentIntegratedAlarmsModal').style.display = 'none';
    });

    phonesData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentPhonesModal');
        const rect = phonesData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    phonesData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentPhonesModal').style.display = 'none';
    });

    sirensData.addEventListener('mouseenter', function () {
        const menuCollapsed = document.body.classList.contains('menu-collapsed')
        const gestionesModal = document.getElementById('buildingCardEquipmentSirensModal');
        const rect = sirensData.getBoundingClientRect()
        const offsetTop = rect.top;
        const offsetLeft = menuCollapsed ? rect.left + 70 : rect.left - 110;
        gestionesModal.style.left = `${offsetLeft}px`;
        gestionesModal.style.top = `${offsetTop}px`;
        gestionesModal.style.display = 'block';
        const viewportHeight = window.innerHeight;
        const rectGestionesModal = gestionesModal.getBoundingClientRect()
        if(rectGestionesModal.top + gestionesModal.offsetHeight > viewportHeight) {
            gestionesModal.style.top = `${rectGestionesModal.top - gestionesModal.offsetHeight}px`;
        }
    });

    sirensData.addEventListener('mouseout', function () {
        document.getElementById('buildingCardEquipmentSirensModal').style.display = 'none';
    });
</script>

