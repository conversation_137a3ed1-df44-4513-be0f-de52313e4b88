<livewire:building-card-info-livewire :building="$building"/>
@push('after_styles')
    <style>
        .building-card-info-header {
            box-sizing: border-box;
            box-shadow: 0px 0px 4px 5px rgba(0, 0, 0, 0.02);
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            background: var(--ColorHeaderBuilding);
            color: white;
            padding: 16px;
            width: 100%;
        }

        .building-card-info-header-number {
            background-color: white;
            color: var(--ColorHeaderBuildingNumber);
            width: 70px;
            height: 50px;
            border-radius: 5px;
            padding: 7px;
            font-weight: bold;
            font-size: 18px;

        }

        .building-card-info-header-select {
            background-color: white;
            border-radius: 5px;
            padding: 7px;
            width: -webkit-fill-available;
            height: 50px;
            font-weight: bold;
            font-size: 18px;
            color: #1F1F1F;
        }

        .building-card-info-header-img {
            width: 50px;
            height: 50px;
            background-color: white;
            border-radius: 5px;
        }

        .building-card-info-header-img > img{
            width: 50px;
            height: 50px;
            border-radius: 5px;
        }

        .building-card-info-header-tools {
            background: var(--ColorHeaderBuilding);
            color: white;
            padding: 8px 16px 0 0;
            /*cursor: pointer;*/

            & span {
                display: inline-block;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 16px 40px;
                cursor: pointer;
                margin: 0 auto;
            }

            & span.active {
                background-color: white;
                color: var(--ColorHeaderBuilding);
                font-weight: bold;
            }
        }

        .building-card-info-container {
            width: 100%;
            height: 100%;
            padding: 16px 24px;
            background: white;
        }

    </style>
@endpush
