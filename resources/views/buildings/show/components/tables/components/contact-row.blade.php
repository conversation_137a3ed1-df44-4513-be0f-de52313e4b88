<tr wire:key="contact-row-{{$contact['building_contact_id']}}"
    class="{{$contact['temporary_contact'] == true ? "contact-temporal" : ""}}">
    <td class="col-apto overflow-visible  {{$this->changeColorRedContactNotAllowed($contact['contact_type']) }}">
        <span class="d-flex align-items-center position-relative">
            <span wire:click="showModalInteractions({{json_encode($contact)}})"
                  class="font-weight-bold total-apartments-and-contacts {{$this->changeColorRedContactNotAllowed($contact['contact_type']) }}">
                {{ $contact['flat_number'] ?? 'S/A' }}
            </span>
            <span class="position-absolute" style="right: -25px;">
                @if($this->contactHasAnnouncements($contact))
                    <div class="icon-container cursor-pointer"
                         data-announcements="{{ $contact['announcements'] }}"
                    >
                        <ion-icon name="megaphone-outline" class="advertisements" wire:ignore>
                        </ion-icon>
                    </div>
                @endif
            </span>
        </span>
    </td>
    <td class="col-nombre">
        <div class="d-flex align-items-center gap-1">
            <img
                src="{{$contact['image'] }}"
                class="avatar-sm rounded-circle"
                style="width: 24px; height: 24px;">
            <div class="d-flex flex-column">
                <a href="/admin/contact/{{ $contact['contact_id'] ?? $contact['id'] }}/info"
                   target="_blank">
                    <span title="{{$contact['complete_name']}}"
                          class="{{$this->changeColorRedContactNotAllowed($contact['contact_type'])}} contacts-text total-apartments-and-contacts"
                    >
                        {{ $contact['complete_name'] }}
                    </span>
                </a>
                <span style="font-size: 12px;">{{$contact['contact_email_atc']}}</span>
            </div>
        </div>
    </td>
    <td class="col-doc {{$this->changeColorRedContactNotAllowed($contact['contact_type']) }}"
        wire:click="showModalInteractions({{json_encode($contact)}})">
        <span
            class="{{$this->changeColorRedContactNotAllowed($contact['contact_type']) }} contacts-text doors-list-item-details-text">
            {{ $contact['ci'] }}
            <br>
            {{ $contact['security_word'] }}
        </span>
    </td>
    <td
        @if(isset($contactsWithPackages[$contact['contact_id']]) && $contactsWithPackages[$contact['contact_id']]['isAuthorized'])
            wire:click="showModalPackage({{ json_encode($contact) }})"
        @endif
        wire:key="contact-{{ $contact['contact_id'] }}"
        class="col-paquetes"
    >
        <span style="position: relative; display: inline-block;">
            @if(isset($contactsWithPackages[$contact['contact_id']]) && $contactsWithPackages[$contact['contact_id']]['isAuthorized'])
                @if($contactsWithPackages[$contact['contact_id']]['hasPackages'])
                    <ion-icon name="cube-sharp"
                              style="color:#C0C8D2; font-size: 1.7rem!important;"></ion-icon>
                    <span class="icon-package package-count-label">
                        {{ $contactsWithPackages[$contact['contact_id']]['packageCount'] }}
                    </span>
                @else
                    <ion-icon name="add-circle-outline" class="icon-add-package"></ion-icon>
                @endif
            @endif
        </span>
    </td>
    <td wire:click="showModalInteractions({{json_encode($contact)}})" class="col-descripcion">
        <span title="{{$contact['description']}} &#13;&#13;{{$contact['notes_2']}} &#13;&#13;{{$contact['notes_3']}}"
              class="{{$this->changeColorRedContactNotAllowed($contact['contact_type']) }} contacts-text doors-list-item-details-text">
            {{ $contact['description'] }}
        </span>
    </td>
    <td wire:click="showModalInteractions({{json_encode($contact)}})" class="col-tipo">
        <span title="{{$contact['temporary_contact'] ? 'Autorizado Temporal' : $contact['contact_type']}}"
              class="d-flex align-items-center contact-type-icon-color contact-text" style="gap: 2px;">
            @foreach(\App\Models\Building::$iconsContactTypesBuildingShow[$contact['contact_type']] ?? ['person'] as $icon)
                @if($contact['temporary_contact'])
                    <ion-icon
                        name="time-outline"
                        class="{{$this->changeColorRedContactNotAllowed($contact['contact_type']) }} contact-type-icon-color">
                    </ion-icon>
                @else
                    <ion-icon
                        name="{{$icon}}-outline"
                        class="{{$this->changeColorRedContactNotAllowed($contact['contact_type']) }} contact-type-icon-color">
                    </ion-icon>
                @endif
            @endforeach
            <span title="Referente">
            @if($contact['referrer'])
                    <ion-icon
                        name="compass-outline"
                        class="{{$this->changeColorRedContactNotAllowed($contact['contact_type']) }} contact-type-icon-color">
                </ion-icon>
                @endif
        </span>
        </span>

    </td>
    <td class="col-telefono">

        <div class="d-flex">
            <span class="d-flex align-items-center gap-1 doors-list-item-details-text">
                <a class="m-0 contacts-text doors-list-item-details-text"
                   onclick="interactionCall3CX({{$contact['id']}}, '{{ $this->formatPhoneNumber($contact['real_primary_phone'] ?? $contact['principal_phone']) }}', {{json_encode($contact)}})"
                >
                      <span class="doors-list-item-details-text">
                          {{ $this->formatPhoneNumber($contact['real_primary_phone'] ?? $contact['principal_phone']) }}
                      </span>
                </a>
                @if($contact['dont_call'])
                    <ion-icon
                        name="notifications-off-circle-outline"
                        class="color-red-contacts md hydrated"
                        style="font-size: 20px!important;"
                        title="NO LLAMAR"
                        role="img"
                        aria-label="notifications off circle outline"
                    >
                    </ion-icon>
                @endif
                @if($this->contactHasMorePhone($contact))
                    <div class="icon-container cursor-pointer" data-contact="{{json_encode($contact)}}"
                         data-phones="{{ $this->phoneTitle }}"
                         data-id="{{ $contact['id'] }}">
                        <span class="counter">+1</span>
                    </div>
                @endif
            </span>
        </div>
    </td>
    <td class="col-acciones">
        @if(
            (!empty($contact['phone_home']) && Str::startsWith($contact['phone_home'], '0')) ||
            (!empty($contact['phone_mobile']) && Str::startsWith($contact['phone_mobile'], '0'))
        )
            <div class="actions"
                 onclick="openWittyBotPage({{ $contact['building_id'] }}, {{ $contact['flat_id'] }},{{ $contact['id'] }})"
            >
                <span title="Enviar mensaje">
                    <ion-icon name="logo-whatsapp" wire:ignore class="icon-wsp"></ion-icon>
                </span>
            </div>
        @endif
    </td>
</tr>
