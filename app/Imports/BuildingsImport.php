<?php

namespace App\Imports;

use App\Models\Building;
use App\Models\Company\Administration;
use App\Models\User\Operator;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;

class BuildingsImport implements ToModel
{
    /**
     * @param array $row
     *
     * @return Building|null
     */
    public function model(array $row)
    {

        if ($row[0] && strtolower($row[0]) != 'name' && strtolower($row[0]) != 'nombre') {

            if (!$this->buildingExists($row[0])) {
                return $this->createNewBuilding($row);
            } else {
                return $this->updateExistingBuilding($row);
            }
        }
    }

    public function createNewBuilding($row)
    {
        return new Building([
                'name' => $row[0],
                'address' => $row[1],
                'between_streets' => $row[2],
                'city' => $row[3],
                'postal_code' => $row[4],
                'phone' => $row[5],
                'mail' => $row[6],
                'comments' => $row[7],
                'security_comments' => $row[8],
                'building_type' => $row[9],
                'service_level' => $row[10],
                'service_type' => $row[11],
                'operator_id' => $this->getOperatorIdByName($row[12]),
                'gamma_code' => $row[13],
                'doorman_format' => $row[14],
                'measures_locations' => $row[15],
                'administration_id' => $this->getAdministratorIdByName($row[16]),
                'doorman_service_hours' => $this->checkIndexExists(17, $row),
                'building_doorman_hours' => $this->checkIndexExists(18, $row),
                'opens_from_flat' => $this->checkIndexExists(19, $row),
                'doors_quantity' => $this->checkIndexExists(20, $row),
                'gates_ramps_controllers' => $this->checkIndexExists(21, $row)
            ]
        );
    }

    public function updateExistingBuilding($row)
    {
        $building = Building::where('name', $row[0])->first();
        $building->address = $row[1];
        $building->between_streets = $row[2];
        $building->city = $row[3];
        $building->postal_code = $row[4];
        $building->phone = $row[5];
        $building->mail = $row[6];
        $building->comments = $row[7];
        $building->security_comments = $row[8];
        $building->building_type = $row[9];
        $building->service_level = $row[10];
        $building->service_type = $row[11];
        $building->operator_id = $this->getOperatorIdByName($row[12]);
        $building->gamma_code = $row[13];
        $building->doorman_format = $row[14];
        $building->measures_locations = $row[15];
        $building->administration_id = $this->getAdministratorIdByName($row[16]);
        $building->doorman_service_hours = $this->checkIndexExists(17, $row);
        $building->building_doorman_hours = $this->checkIndexExists(18, $row);
        $building->opens_from_flat = $this->checkIndexExists(19, $row);
        $building->doors_quantity = $this->checkIndexExists(20, $row);
        $building->gates_ramps_controllers = $this->checkIndexExists(21, $row);
        $building->save();
    }

    public function buildingExists($name)
    {
        return Building::where('name', $name)->exists();
    }

    public function checkIndexExists($index, $row)
    {
        return array_key_exists($index, $row) ? $row[$index] : '';
    }

    public function getOperatorIdByName($name)
    {
        $operator = Operator::whereRaw("UPPER(name) LIKE '%" . strtoupper($name) . "%'")->first();
        return $operator ? $operator->id : null;
    }

    public function getAdministratorIdByName($name)
    {
        $administrator = Administration::whereRaw("UPPER(name) LIKE '%" . strtoupper($name) . "%'")->first();
        return $administrator ? $administrator->id : null;
    }
}
