<?php

namespace App\Imports;

use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Company\Administration;
use App\Models\Flat;
use App\Models\TemporaryContact;
use App\Models\Tower;
use App\Models\User\Contact;
use App\Models\User\Operator;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;

use function PHPUnit\Framework\isEmpty;

class ContactsImport implements ToModel
{
    /**
     * @param array $row
     *
     * @return Contact|null
     */
    public function model(array $row)
    {
        try {
            if (!is_null($row[0]) && strtolower($row[0]) != 'name' && strtolower($row[0]) != 'nombre') {
                $name = $row[0];
                $surname = $row[1];
                $building_number = $row[2];

                $building = Building::where('building_number', $building_number)->first();

                if (is_null($building)) {
                    throw new ModelNotFoundException("Edificio {$row[2]} no existe para el contacto {$name} {$surname}");
                }

                $flat = $row[3];
                $tower_denomination = $row[4];
                if ($tower_denomination == '' || $tower_denomination == null) {
                    $tower_denomination = null;
                    $row[4] = null;
                }
                if ($row[0] && strtolower($row[0]) != 'nombre' && strtolower($row[0]) != 'name' && $row[3]) {
                    if (!Contact::contactExists($name, $surname, $building_number, $flat, $tower_denomination)) {
                        return $this->createNewContact($row);
                    } else {
                        return $this->updateExistingContact($row);
                    }
                }
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function createNewContact($row)
    {
        $building = Building::query()->where('building_number', $row[2])->first();
        $building_id = $building->id;
        $email = str_replace(' ', '', $row[13]);
        $flat_number = $row[3];
        $tower_denomination = $row[4];
        $contactType = ucwords($row[5]);
        $contact = Contact::query()->create([
            'name' => $row[0],
            'surname' => $row[1],
            'complete_name' => $row[0] . ' ' . $row[1],
            'phone_mobile' => $row[7] ? str_replace(' ', '', $row[7]) : null,
            'ci' => $row[9],
            'email' => $email == '' ? null : $email,
            'password' => Hash::make($row[9]), //ci
            'notified' => true,
            'needs_verification' => null
        ]);

        $flatId = Contact::findOrCreateFlat($building_id, $flat_number, $tower_denomination);

        $officeContactTypes = Contact::$officeContactTypes[$contactType] ?? null;

        $typeUser = $officeContactTypes ? 'Oficina' : 'Residencial';

        BuildingContact::query()->create([
            'building_id' => $building_id,
            'flat_id' => $flatId,
            'contact_id' => $contact->id,
            'contact_type' => $contactType,
            'owner_or_tenant' => $row[6],
            'phone_mobile' => $row[7] ? str_replace(' ', '', self::getMobilePhone($row[7])) : null,
            'phone_home' => $row[8] ? str_replace(' ', '', $row[8]) : null,
            'ci' => $row[9],
            'type_of_contact_in_building' => $typeUser,
            'referrer' => false,
            'empty_flat' => $row[10] ?? false,
            'main_building' => 1,
            'relation' => $row[11],
            'dont_call' => $row[14] ?? false,
            'description' => $row[12]
        ]);

        return $contact;
    }


    public function updateExistingContact($row)
    {

        $building_id = Building::query()->where('building_number', $row[2])->value('id');
        $email = str_replace(' ', '', $row[13]);
        $flat_number = $row[3];
        $name = $row[0];
        $surname = $row[1];
        $building = $row[2];
        $tower_denomination = $row[4];

        $contact = $this->getExistingContact($name, $surname, $building, $flat_number, $tower_denomination);

        if ($contact) {
            $contactType = ucwords($row[5]);
            $officeContactTypes = Contact::$officeContactTypes[$contactType] ?? null;

            $typeUser = $officeContactTypes ? 'Oficina' : 'Residencial';

            $contact->update([
                'complete_name' => $name . ' ' . $surname,
                'contact_type' => $contactType,
                'owner_or_tenant' => $row[6],
                'phone_mobile' => $row[7] ? str_replace(' ', '', self::getMobilePhone($row[7])) : null,
                'phone_home' => $row[8] ? str_replace(' ', '', $row[8]) : null,
                'ci' => $row[9],
                'type_of_contact_in_building' => $typeUser,
                'referrer' => false,
                'empty_flat' => $row[10] ?? false,
                'main_building' => 1,
                'relation' => $row[11],
                'dont_call' => $row[14] ?? false,
                'email' => trim($email),
                'password' => bcrypt($row[9]),
                'deleted_at' => null,
                'notified' => true,
                'needs_verification' => null,
            ]);

            $flatId = Contact::findOrCreateFlat($building_id, $flat_number, $tower_denomination);
            $mainBuilding = BuildingContact::query()->where('contact_id', $contact->id)->count() == 1;

            BuildingContact::query()->where('contact_id', $contact->id)
                ->first()
                ->update([
                    'building_id' => $building_id,
                    'flat_id' => $flatId,
                    'contact_type' => $row[5],
                    'owner_or_tenant' => $row[6],
                    'phone_mobile' => $row[7] ? str_replace(' ', '', self::getMobilePhone($row[7])) : null,
                    'phone_home' => $row[8] ? str_replace(' ', '', $row[8]) : null,
                    'empty_flat' => $row[10] ?? false,
                    'relation' => $row[11],
                    'description' => $row[12],
                    'dont_call' => $row[14] ?? false,
                    'main_building' => $mainBuilding,
                    'type_of_contact_in_building' => 'S/D',
                ]);

            return $contact;
        }

        return null;
    }

    public function getExistingContact($name, $surname, $building_number, $flat_number, $tower_denomination)
    {
        $building_id = Building::query()->where('building_number', $building_number)->value('id');
        $flatId = Contact::findOrCreateFlat($building_id, $flat_number, $tower_denomination);
        $complete_name = $name . ' ' . $surname;

        return Contact::query()->withTrashed()->where('complete_name', $complete_name)
            ->whereHas('contacts', function (Builder $query) use ($building_id, $flatId) {
                $query->where('building_id', $building_id)
                    ->where('flat_id', $flatId);
            })->first();

    }

    public static function updateContactsBuildings()
    {
        foreach (Contact::all() as $contact) {
            if ($contact->flat->count() > 0) {
                $contact->building_id = $contact->flat[0]->building->id;
                $contact->save();
            }
        }
        return 'OK';
    }

    public static function deleteAllContacts()
    {
        foreach (TemporaryContact::withTrashed()->get() as $contact) {
            $contact->forceDelete();
        }

        foreach (Contact::withTrashed()->get() as $contact) {
            $contact->forceDelete();
        }
        return 'OK';
    }

    public static function getMobilePhone($mobile_phone, bool $international = false)
    {
        if ($mobile_phone) {
            if (str_starts_with($mobile_phone, '0')) return $mobile_phone;
            if (str_starts_with($mobile_phone, '9')) return '0' . $mobile_phone;
            if (str_starts_with($mobile_phone, '598')) return '0' . substr($mobile_phone, 3);
            if (str_starts_with($mobile_phone, '+598')) return '0' . substr($mobile_phone, 4);
            if (str_starts_with($mobile_phone, '+') || $international) {
                $mobile_phone = str_replace('+', '', $mobile_phone);
                return '00' . $mobile_phone;
            }
        }
        return $mobile_phone;
    }
}
