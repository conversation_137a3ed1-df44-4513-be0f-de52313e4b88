<?php

namespace App\Notifications;

use App\Models\Area;
use App\Models\Caseq;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class NotificationCaseLimitExpired extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Caseq $case)
    {
        $this->case = $case;

    }


    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'case' => $this->case->id,
            'priority' => $this->case->priority,
            'notification_type' => 'Fecha límite ' . \Carbon\Carbon::parse($this->case->limited_date)->format('d/m/y H:i'),
            'description' => 'Se cumplió la fecha límite ' . $this->case->limited_date,
            'created_by' => $this->case->created_by,
            'created_by_name' => User::find($this->case->created_by) ? User::find($this->case->created_by)->complete_name : 'Foxsys',
            'area_name' => Area::find($this->case->area_id ?? 1)->name,
            'area_id' => $this->case->area_id,
            'text' => 'El caso ' . $this->case->id . ' está vencido.',
            'link' => '/admin/case/' . $this->case->id . '/show',
            'updated_by' => $this->case->created_by,
            'updated_by_name' => $this->case->created_by,
            'last_comment' => $this->case->getLastCommentFormatForNotifications(),

        ];
    }
}
