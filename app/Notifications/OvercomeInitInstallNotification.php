<?php

namespace App\Notifications;

use App\Models\Area;
use App\Models\Building;
use App\Models\Caseq;
use App\Models\TemporaryContact;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OvercomeInitInstallNotification extends Notification
{
    use Queueable;

    public $building;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Building $building)
    {
        $this->building = $building;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'temporary' => $this->building->id,
            'description' => $this->building->service_type,
            'created_by' => 'FOXSYS',
            'created_by_name' => 'FOXSYS',
            'area_name' => '',
            'area_id' => '',
            'text' => 'Fecha inicial de la instalación del edificio N° ' . $this->building->building_number .'.',
            'link' => '/admin/building/' . $this->building->id . '/edit',
        ];
    }
}
