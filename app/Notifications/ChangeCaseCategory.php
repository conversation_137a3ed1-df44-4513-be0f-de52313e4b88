<?php

namespace App\Notifications;

use App\Models\Area;
use App\Models\Caseq;
use App\Models\Category;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeCaseCategory extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Caseq $case)
    {
        $this->case = $case;
    }


    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'case' => $this->case->id,
            'description' => $this->case->description,
            'notification_type' => 'Categoría nueva ' . Category::getCategoryName($this->case->last_category_id),
            'created_by' => $this->case->created_by,
            'created_by_name' => User::find($this->case->created_by) ? User::find($this->case->created_by)->complete_name : 'Foxsys',
            'area_name' => Area::find($this->case->area_id ?? 1)->name,
            'area_id' => $this->case->area_id,
            'title' => $this->case->title,
            'text' => 'Se cambió la categoria de un caso.',
            'priority' => $this->case->priority,
            'link' => '/admin/case/' . $this->case->id . '/show',
            'updated_by' => backpack_user()->id,
            'updated_by_name' => backpack_user()->complete_name,
            'last_comment' => $this->case->getLastCommentFormatForNotifications(),

        ];
    }
}
