<?php

namespace App\Integrations\Intercoms;

use App\Actions\Intercoms\AccessCodes\GetAllUsersAKUVOX;
use App\Actions\Intercoms\AccessCodes\GetIntercomsIps;
use App\Models\AccessCode;
use App\Services\KazooService;
use App\Services\MethodsEnum;

class IntercomAkuvoxBase implements IIntercom, IIntercomKazoo
{
    protected $ip;

    public function __construct($ip)
    {
        $this->ip = $ip['ip'];
    }

    protected function executeCurlRequest($settings)
    {
        $url = config('intercoms.SERVER_IP') . ':' .
            config('intercoms.SERVER_PORT') . '/api/user/create/akuvox/' . $settings['ip'];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($settings),
            CURLOPT_HTTPHEADER => ["Content-Type: application/json"],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        $response = curl_exec($curl);

        \Log::info('--------------------- START executeCurlRequest ');
        \Log::info($response);
        \Log::info($settings['ip']);
        \Log::info($settings);
        \Log::info('--------------------- END executeCurlRequest ');


        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            die('Curl failed: ' . $error);
        }

        return json_decode($response, true);
    }

    public function getUserIdByPrivatePinAkuvox($pin): array
    {
        if (!$pin || $pin == '') return [];

        $response = json_decode($this->getAllUsersAKUVOX($this->ip), true);

        if (!isset($response[0]['status']) || $response[0]['status'] == '501') {
            return [];
        }

        $ids = [];
        $usersData = $response[0]['message'] ?? null;

        if (is_array($usersData)) {
            foreach ($usersData as $oneUser) {
                if (isset($oneUser['PrivatePIN']) && $oneUser['PrivatePIN'] == $pin) {
                    $ids[] = $oneUser['ID'];
                }
            }
        }

        return $ids;
    }


    public static function getAllUsersAKUVOX($intercomIp)
    {
        $results = [];
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');
        $url = "$serverIP:$serverPORT" . '/api/user/get/akuvox/' . $intercomIp;

        try {
            $curl = curl_init();

            curl_setopt_array(
                $curl,
                array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                )
            );

            $response = curl_exec($curl);

            if ($response === FALSE) {
                AccessCode::$accessCodeBoolean = false;
                die('Curl failed: ' . curl_error($curl));
            }

            curl_close($curl);

            $decodedResponse = json_decode($response, true);

            if (!is_array($decodedResponse)) {
                AccessCode::$accessCodeBoolean = false;
                $results[] = ['status' => '500', 'message' => 'Invalid JSON response from API'];
            } else {
                if (array_key_exists('error', $decodedResponse)) {
                    AccessCode::$accessCodeBoolean = false;
                    $results[] = ['status' => '501', 'message' => $decodedResponse['error']['description']];
                } else if ($decodedResponse['status'] === '501') {
                    $results[] = ['status' => '501', 'message' => $decodedResponse['message']];
                } else {
                    $results[] = ['status' => '200', 'message' => $decodedResponse['message']['data']['item']];
                }
            }

        } catch (\Exception $exception) {
            AccessCode::$accessCodeBoolean = false;
            $results[] = ['status' => '501', 'message' => $exception->getMessage()];
        }

        return json_encode($results);
    }

    public function createAccessCode($contactName, $accessCode)
    {
        $settings = [
            "data" => [
                "item" => [
                    [
                        "Name" => $contactName,
                        "PrivatePIN" => (string)$accessCode,
                        "ScheduleRelay" => '1001-1',
                    ]
                ]
            ],
            "target" => "user",
            "action" => "add",
            "ip" => $this->ip,
        ];
        return $this->executeCurlRequest($settings);
    }

    public function deleteAccessCode($accessCode)
    {

        $userIDs = $this->getUserIdByPrivatePinAkuvox($accessCode);
        $results = [];
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');


        \Log::info('--------------------- START deleteAccessCode ');
        \Log::info('accessCode');
        \Log::info($accessCode);
        \Log::info('users IDs to delete');
        \Log::info($userIDs);
        \Log::info('--------------------- END deleteAccessCode ');

        foreach ($userIDs as $userID) {
            $url = "$serverIP:$serverPORT" . '/api/user/delete/akuvox/' . $this->ip;
            $header = array("Content-Type: application/json");
            $settings = [];
            $data = null;
            $user = [];
            $user['ID'] = $userID;
            $data['item'] = [$user];
            $settings['data'] = $data;
            $settings['target'] = 'user';
            $settings['action'] = 'del';
            try {
                $curl = curl_init();
                curl_setopt_array(
                    $curl,
                    array(
                        CURLOPT_URL => $url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'DELETE',
                        CURLOPT_POSTFIELDS => json_encode($settings),
                        CURLOPT_HTTPHEADER => $header,
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => false,

                    )
                );
                $response = curl_exec($curl);
                if ($response === FALSE) {
                    AccessCode::$accessCodeBoolean = false;
                    die('Curl failed: ' . curl_error($curl));
                }
                curl_close($curl);
                $results[] = ['status' => '204', 'message' => json_decode($response, true)];
            } catch (\Http\Client\Exception $exception) {
                AccessCode::$accessCodeBoolean = false;
                $results[] = ['status' => '501', 'message' => $exception->getMessage()];
            }
        }
        return json_encode($results);
    }

    public function addContact(string $name, string $kazoo_number)
    {
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');

        $url = "$serverIP:$serverPORT" . '/api/contact/create/akuvox/' . $this->ip;

        $attributes = [
            "target" => "contact",
            "action" => "add",
            "data" => [
                "item" => [
                    [
                        "DialType" => "2",
                        "Name" => $name,
                        "Phone" => $kazoo_number,
                    ]
                ]
            ]
        ];

        KazooService::executeHttpRequest($url, $attributes, MethodsEnum::POST->value, false);
    }

    public function deleteContact(string $name)
    {
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');

        $url = "$serverIP:$serverPORT" . '/api/contact/delete/akuvox/' . $this->ip;

        $attributes = [
            "target" => "contact",
            "action" => "del",
            "data" => [
                "item" => [
                    [
                        "Name" => $name,
                    ]
                ]
            ]
        ];


        KazooService::executeHttpRequest($url, $attributes, MethodsEnum::POST->value, false);
    }
}
