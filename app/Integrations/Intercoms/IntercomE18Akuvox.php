<?php

namespace App\Integrations\Intercoms;

use App\Services\KazooService;
use App\Services\MethodsEnum;

class IntercomE18Akuvox extends IntercomAkuvoxBase
{
    public function createAccessCode($contactName, $accessCode)
    {
        $settings = [
            "data" => [
                "item" => [
                    [
                        "UserID" => $this->generateRandomString(),
                        "Name" => $contactName,
                        "PrivatePIN" => (string)$accessCode,
                        "ScheduleRelay" => '1001-1',
                    ]
                ]
            ],
            "target" => "user",
            "action" => "add",
            "ip" => $this->ip,
        ];
        return $this->executeCurlRequest($settings);
    }

    private function generateRandomString($length = 6)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
    }

    public function addContact(string $name, string $kazoo_number)
    {
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');

        $url = "$serverIP:$serverPORT" . '/api/contact/create/akuvox/' . $this->ip;

        $isUserCreated = $this->checkifExitUserInIntercom($name, $kazoo_number);

        if ($isUserCreated) {
            return;
        }

        $attributes = [
            "ip" => $this->ip,
            "target" => "user",
            "action" => "add",
            "data" => [
                "item" => [
                    [
                        "CallPriority" => "0",
                        "DialAccount" => "2",
                        "Group" => "Kazoo",
                        "Name" => $name,
                        "Phone" => $kazoo_number,
                        "ScheduleRelay" => "1001-1",
                        "UserID" => $this->generateRandomString(),
                    ]
                ]
            ]
        ];

        KazooService::executeHttpRequest($url, $attributes, MethodsEnum::POST->value, false);
    }

    public function deleteContact(string $name)
    {
        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');

        $url = "$serverIP:$serverPORT" . '/api/contact/delete/akuvox/' . $this->ip;

        $idUserToDelete = $this->getIdAkuvox($name);

        $attributes = [
            "ip" => $this->ip,
            "target" => "user",
            "action" => "del",
            "data" => [
                "item" => [
                    [
                        "ID" => $idUserToDelete
                    ],
                ],
            ],
        ];

        KazooService::executeHttpRequest($url, $attributes, MethodsEnum::POST->value, false);
    }

    public function getIdAkuvox($name)
    {
        $allUsersRaw = $this->getAllUsersAKUVOX($this->ip);
        $allUsers = json_decode($allUsersRaw, true)[0]['message'] ?? [];

        foreach ($allUsers as $user) {
            if (strcasecmp(trim($user['Name'] ?? ''), trim($name)) === 0) {
                return $user['ID'] ?? null;
            }
        }
        return null;
    }
    public function checkifExitUserInIntercom($name, $kazoo_number)
    {
        $allUsersRaw = $this->getAllUsersAKUVOX($this->ip);
        $allUsers = json_decode($allUsersRaw, true)[0]['message'] ?? [];

        foreach ($allUsers as $user) {

            if (
                strcasecmp(trim($user['Name'] ?? ''), trim($name)) === 0 &&
                trim($user['Phone'] ?? '') === trim($kazoo_number)
            ) {
                return true;
            }
        }
        return false;
    }

}
