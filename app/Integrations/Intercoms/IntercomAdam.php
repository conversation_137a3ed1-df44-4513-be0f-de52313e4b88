<?php

namespace App\Integrations\Intercoms;

use App\Actions\Intercoms\AccessCodes\GetAllUsersAKUVOX;
use App\Actions\Intercoms\AccessCodes\GetIntercomsIps;
use App\Models\AccessCode;

class IntercomAdam implements IIntercom
{

    public function createAccessCode($contactName, $accessCode)
    {
        return false;

    }

    public function deleteAccessCode($accessCode)
    {
        return false;

    }
}
