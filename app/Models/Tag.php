<?php

namespace App\Models;

use App\Http\Controllers\Api\TagController;
use App\Models\User\Contact;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Carbon\Carbon;
use DB;
use PHPUnit\Exception;
use Illuminate\Database\Eloquent\SoftDeletes;
use Venturecraft\Revisionable\RevisionableTrait;
use Spatie\Permission\Traits\HasRoles;

class Tag extends Model
{
    use CrudTrait, SoftDeletes, RevisionableTrait, HasRoles;

    public function identifiableName()
    {
        return $this->code;
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'tags';
    // protected $primaryKey = 'id';
    // public $timestamps = false;
    // protected $guarded = ['id'];
    protected $fillable = ['code', 'code_hex', 'status', 'description', 'flat_id', 'building_id', 'contact_id', 'case_id'];
    // protected $hidden = [];
    // protected $dates = [];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */


    function import($file)
    {


       //Determine Building
        $building = null;
        $buildingRaw = explode("-", $file->getClientOriginalName());

        $buildingId = str_replace(" ", "",  $buildingRaw[0]);

        if ($buildingId && is_numeric($buildingId)) {
            $building = Building::where('building_number', $buildingId)->first();
        }

        //Load content
        $contents = file_get_contents($file);
        $lines = explode("\n", $contents);


        //Output
        $tags = [];

        if (count($lines) > 1) {

            for ($i = 0; $i < count($lines); $i++) {
                $flat = null;
                $flatTags = [];
                $parts = explode("   ", $lines[$i]);

                if (self::isFlatFormat($parts, $lines[$i])) {
                    $flat = $parts[0];
                    $i++;

                    while ($i < count($lines)) {
                        $parts = explode("   ", $lines[$i]);
                        if ($parts[0] == "   "  || $parts[0] == "\r"  || $parts[0] == "\n"  || $lines[$i] == "\r") {
                            //Sigue obteniendo los tags dentro de este bloque / dos lineas que son flats
                            $i++;

                            continue;
                        }

                        $parts0 = str_replace("\r", '',  $parts[0]);

                        if (count($parts) == 1 && strlen($parts0) != 10) {
                            //Rompe el ciclo y trata al siguiente como un flat ya que esta linea no se corresponde al formato de un tag code
                            --$i;
                            break;
                        }

                        //Linea válida para enviarla a la siguiente etapa
                        $flatTags[] = self::addTag($parts, $flatTags);

                        $i++;
                    }

                    //Si no hay más nada que hacer en este bloque, guardamos la dupla de flat => flatTags
                    $tags[] = ['flat' => str_replace("\r", '',  $flat), 'flatTags' => $flatTags];

                } else {
                    $parts = explode("   ", $lines[$i]);

                    $flatTags[] = self::addTag($parts, $flatTags);
                    $tags[] = ['flat' => str_replace("\r", '',  ''), 'flatTags' => $flatTags];


                }
            }
        }


        return self::storeTags($tags, $building->id ?? null);
    }

    public static function isFlatFormat($parts, $line)
    {

        $line = str_replace("\r", '',  $line);


        if (count($parts) === 1 && $line != "\r") {
            if(is_numeric($line)){
                if(strlen($line)<9){
                return true;
                }
            }else{
             return true;
            }
        }

        return false;
    }

    public static function addTag($parts)
    {
        //Preparamos el payload de un tag
        $tag_code = null;
        $tag_code_hex = null;
        $tag_code_description = null;
        $tag_code_case = null;
        $tag_status = 'ACTIVE';

        if (isset($parts[0])) {
            $tag_code = $parts[0];
        }

        //Esta segunda parte puede ser un hexadecimal o una descripcion
        if (isset($parts[1])) {
            $description1 = str_replace("\r", '',  $parts[1]);
            $description = str_replace(" ", '', $description1);

            if (ctype_xdigit($description)) {
                $tag_code_hex = $description;
            } else {
                //Si en la segunda parte, no hay un hexadecimal entonces, esta linea en esta posicion tiene una description
                $comment = self::buildDescription($description);
                $tag_code_description =  $comment['tag_code_description'];
                $tag_status = $comment['tag_status'];
                $tag_code_case = $comment['tag_code_case'];
                $tag_code_hex = null;
            }
        }

        //Si viene con una tercera parte, si o si es una descripcion
        if (isset($parts[2])) {
            $comment = self::buildDescription($parts[2]);

            $tag_code_description =  $comment['tag_code_description'];
            $tag_status = $comment['tag_status'];
            $tag_code_case = $comment['tag_code_case'];
        }

        //En cualquier variante, si no tenemos codigo hexadecimal, creamos uno del tag code
        if (!$tag_code_hex) {
            $tag_code_hex =  strtoupper(dechex($tag_code));
        }

        //Payload final
        $tag = [
            'tag_code' => $tag_code,
            'tag_code_hex' => $tag_code_hex,
            'tag_code_description' => $tag_code_description,
            'tag_status' =>  $tag_status,
            'tag_case' => $tag_code_case
        ];


        return  $tag;
    }

    public static function buildDescription($commnetRaw)
    {
        $tag_status = 'ACTIVE';
        $tag_code_case = Null;


        $tag_code_description =   $commnetRaw;


        if (str_contains($tag_code_description, 'ELIMINADO') || str_contains($tag_code_description, 'ELIMINADA')) {
            $tag_status = 'INACTIVE';
        }

        //Case by ('Case' #) format
        $partsDescription = explode("(Caso", $tag_code_description);


        if (count($partsDescription) > 1) {
            $partsWithNumbers = explode(")", $partsDescription[1]);
            $tag_code_case = str_replace(" ", "", $partsWithNumbers[0]);
            $tag_code_description = null;
        }

        //Case by (Case#) format
        $partsDescription = explode("(", $tag_code_description);

        if (count($partsDescription) > 1) {
            $partsWithNumbers = explode(")", $partsDescription[1]);
            $result = str_replace(" ", "", $partsWithNumbers[0]);
            if (is_numeric($result)) {
                $tag_code_case = $result;
                $tag_code_description = null;
            } else {
                $tag_code_description = $partsWithNumbers[0];
            }
        }

        return ['tag_code_description' => $tag_code_description, 'tag_status' => $tag_status, 'tag_code_case' => $tag_code_case];
    }

    public static function storeTags($tags, $buildingId)
    {

        try {
            foreach ($tags as $tagOrigin) {
                foreach ($tagOrigin['flatTags'] as $ft) {

                    $tag = new Tag();

                    DB::beginTransaction();
                    $tag->code = $ft['tag_code'];
                    $tag->code_hex = $ft['tag_code_hex'];
                    $tag->description = $ft['tag_code_description'];
                    $tag->status = $ft['tag_status'];

                    if (Caseq::find($ft['tag_case'])) {

                        $tag->case_id = $ft['tag_case'];
                    }



                    if ($buildingId) {
                        $tag->building_id = $buildingId;

                        if (array_key_exists('flat', $tagOrigin)) {
                            $flat = null;
                        //    $towers = Tower::where('building_id', $buildingId)->get()->pluck('id')->toArray();

                            $flatNumbers = Flat::where('building_id', $buildingId)->pluck('number');


                            $flats = Flat::whereIn('number', $flatNumbers)->where('building_id', $buildingId)->get();

                            $flats = $flats->filter(function ($flat) use ($tagOrigin) {
                                return $flat->number_with_tower == $tagOrigin['flat'];
                            });



                            if ($flats->count() > 0) {
                                $flat = $flats->first();
                            }
                            if ($flat) {
                                $tag->flat_id = $flat->id;
                            } else {
                                $flatBuildingByDefault = Flat::where('building_id', $buildingId)->where('number', 'Edificio')->first();
                                if ($flatBuildingByDefault) {
                                    $tag->flat_id = $flatBuildingByDefault->id;
                                }
                                $tag->description = $tagOrigin['flat'] . '  ' . $tag->description;
                            }
                        }
                    }


                    if ($ft['tag_code'] &&  $ft['tag_code'] != " " &&  $ft['tag_code'] != "\r") {
                        $tag->save();
                    }

                    DB::commit();
                }
            }

            return ['code' => 'ok', 'message' => 'Data Imported successfully'];
        } catch (\Throwable $th) {
            return ['code' => 'error', 'message' => $th->getMessage()];
        }
    }




    public function buildingName()
    {

        if ($this->building) {
            $number = strval($this->building->building_number);
            $number = '00' . $number;
            return $this->building->name . ' - ' .  substr($number, -3);
        }
        return 'No se asignó edificio.';
    }

    public function flatName()
    {
        if ($this->flat) {
            return $this->flat->number;
        }
        return 'No se asignó apartamento.';
    }

    public function fullName()
    {
        return $this->flat->fullName;
    }



    public static function updateIntercoms($tag, $contact)
    {
        try {

            //code...
            // 1- Delete contact at intercoms
            self::deleteForAkuvoxOr2N($contact->id, $tag->code);

            // 2- Create new contact at intercoms with the new tag code
            self::createTagCodeAtIntercoms($contact->id, $tag->code);
        } catch (\Throwable $th) {
            //throw $th;
        }
    }



    public static function createTagCodeAtIntercoms($userId, $cardCode)
    {
        $contact =  Contact::find($userId);

        if ($contact && $contact->building->intercoms->count() != 0) {
            if ($contact->userBuildingHasAkuvoxIntercom()) {
                self::createTagCodeAkuvox($userId, $cardCode);
            } else {
                self::createTagCode2N($userId, $cardCode);
            }
        }
    }

    public static function updateTagWithContactData($tag, $contact, $type = "all")
    {
        if ($tag && $contact) {
            if ($type == 'all') {
                //Contact
                $tag->contact_id = $contact->id;
                //Flat
                $tag = self::updateTagFlat($tag, $contact);
                //Building
                $tag = self::updateTagBuilding($tag, $contact);
                //Update intercoms
                //  self::updateIntercoms($tag, $contact); //todo

                $tag->update();
            } elseif ($type == 'onlyBuildingAndFlat') {
                //Flat
                $tag = self::updateTagFlat($tag, $contact);
                //Building
                $tag = self::updateTagBuilding($tag, $contact);

                $tag->update();
            }
        }
    }

    public static function updateTagBuilding($tag, $contact)
    {
        if (!$tag->building_id) {
            if (Building::find($contact->flat_id)) {
                $tag->building_id = $contact->building_id;
            } else {
                $flat = Flat::find($contact->flat_id);
                if ($flat) {
                    $tag->building_id = $flat->building_id;
                }
            }
        }

        return $tag;
    }



    public static function updateTagFlat($tag, $contact)
    {
        if (!$tag->flat_id) {
            if (Flat::find($contact->flat_id)) {
                $tag->flat_id = $contact->flat_id;
            }
        }

        return $tag;
    }



    public static function deleteExpiredAccessCodeAkuvox($userId, $code)
    {

        $contact = Contact::find($userId);

        $userID = self::getUserIdByPrivatePinAkuvox($userId, $code);

        $ip = $contact->building->intercoms()->first()->ip;

        $url = (env('LOGS_HOST') ?? '*************') . ':' .
            (env('LOGS_HTTP_PORT') ?? '33667')
            . '/api/code/delete/akuvox/' . $ip;

        $header = array("Content-Type: application/json");
        $settings = [];
        $data = null;
        $user = [];
        $user['ID'] = $userID;
        $data['item'] = [$user];
        $settings['data'] = $data;
        $settings['target'] = 'user';
        $settings['action'] = 'del';

        try {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'DELETE',
                CURLOPT_POSTFIELDS => json_encode($settings),

                CURLOPT_HTTPHEADER => $header,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,

            ));
            $response = curl_exec($curl);
            if ($response === FALSE) {

                die('Curl failed: ' . curl_error($curl));
            }
            curl_close($curl);

            return json_encode(['status' => '201', 'message' => json_decode($response, true)]);
        } catch (\Http\Client\Exception $exception) {
            return json_encode(['status' => '501', 'message' => $exception->getMessage()]);
        }
    }

    public static function getUserIdByPrivatePinAkuvox($userId, $cardCode)
    {
        $users = json_decode(self::getAllUsersAkuvox($userId))->message;
        foreach ($users as $user) {
            if ($user->CardCode == $cardCode) {
                return $user->ID;
            }
        }
        return null;
    }


    public static function deleteAccessCode2N($userId, $code)
    {
        $contact = Contact::find($userId);
        $ip = $contact->building->intercoms()->first()->ip;

        $url = (env('LOGS_HOST') ?? '*************') . ':' .
            (env('LOGS_HTTP_PORT') ?? '33667')
            . '/api/code/delete/2n/' . $ip;

        $uuid = Tag::where('code', $code)->first()->uuid_contact_intercom;

        if ($uuid) {
            $header = array("Content-Type: application/json");
            $settings = [];
            $user = [];
            $user['uuid'] = $uuid;
            $settings['users'] = [$user];

            try {
                $curl = curl_init();

                curl_setopt_array($curl, array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'DELETE',
                    CURLOPT_POSTFIELDS => json_encode($settings),
                    CURLOPT_HTTPHEADER => $header,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_HTTPAUTH => CURLAUTH_DIGEST,
                    CURLOPT_USERPWD => 'foxsys:PRyj4AL*N^$xkdec'
                ));

                $response = curl_exec($curl);
                if ($response === FALSE) {
                    echo 'RESPONSE DELETE 2N ACCESSCODE: ' . $response . PHP_EOL;

                    die('Curl failed: ' . curl_error($curl));
                }
                echo 'RESPONSE DELETE 2N ACCESSCODE: ' . $response . PHP_EOL;

                curl_close($curl);

                if (array_key_exists('error', json_decode($response, true))) {
                    return json_encode(['status' => '501', 'message' => json_decode($response, true)['error']['description']]);
                }
                echo '2n accessCode deleted... ' . PHP_EOL;

                return json_encode(['status' => '201', 'message' => json_decode($response, true)]);
            } catch (\Http\Client\Exception $exception) {
                return json_encode(['status' => '501', 'message' => $exception->getMessage()]);
            }
        }

        return json_encode(['status' => '501', 'message' => 'Not found user']);
    }


    public static function createTagCodeAkuvox($userId, $cardCode)
    {
        $contact = Contact::find($userId);
        $contactName = $contact->complete_name;
        $ip = $contact->building->intercoms()->first()->ip;

        $url = (env('LOGS_HOST') ?? '*************') . ':' .
            (env('LOGS_HTTP_PORT') ?? '33667')
            . '/api/code/create/akuvox/' . $ip;

        $header = array("Content-Type: application/json");
        $settings = [];
        $data = null;
        $user = [];
        $user['Name'] = $contactName;
        $user['CardCode'] = (string)$cardCode;
        $user['ScheduleRelay'] = '1001-1';
        $data['item'] = [$user];
        $settings['data'] = $data;
        $settings['target'] = 'user';
        $settings['action'] = 'add';

        try {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($settings),

                CURLOPT_HTTPHEADER => $header,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,

            ));
            $response = curl_exec($curl);
            if ($response === FALSE) {
                die('Curl failed: ' . curl_error($curl));
            }


            curl_close($curl);

            if (array_key_exists('error', json_decode($response, true))) {
                return json_encode(['status' => '501', 'message' => json_decode($response, true)['error']['description']]);
            }

            return json_encode(['status' => '201', 'message' => json_decode($response, true)]);
        } catch (\Http\Client\Exception $exception) {
            return json_encode(['status' => '501', 'message' => $exception->getMessage()]);
        }
    }

    public static function createTagCode2N($userId, $cardCode)
    {
        $contact = Contact::find($userId);
        $contactName = $contact->complete_name;
        $contactEmail = $contact->email;

        $ip = $contact->building->intercoms()->first()->ip;

        $url = (env('LOGS_HOST') ?? '*************') . ':' .
            (env('LOGS_HTTP_PORT') ?? '33667')
            . '/api/code/create/2n/' . $ip;

        $header = array("Content-Type: application/json");
        $settings = [];
        $user = [];
        $user['name'] = $contactName;
        $user['email'] = $contactEmail;
        $user['access']['card'] = [$cardCode];
        $settings['users'] = [$user];

        try {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($settings),
                CURLOPT_HTTPHEADER => $header,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPAUTH => CURLAUTH_DIGEST,
                CURLOPT_USERPWD => 'foxsys:PRyj4AL*N^$xkdec'
            ));

            $response = curl_exec($curl);

            $uuid = json_decode($response, true)['message']['result']['users'][0]['uuid'];

            $tag = Tag::where('code', $cardCode)->first();
            $tag->uuid_contact_intercom = $uuid;
            $tag->save();

            if ($response === FALSE) {
                echo 'RESPONSE CREATE 2N TAGCODE: ' . $response . PHP_EOL;
                die('Curl failed: ' . curl_error($curl));
            }
            echo 'RESPONSE CREATE 2N TAGCODE: ' . $response . PHP_EOL;

            curl_close($curl);

            echo '2n tagCode generated... ' . PHP_EOL;
            return json_encode(['status' => '201', 'message' => json_decode($response, true)]);
        } catch (\Http\Client\Exception $exception) {
            return json_encode(['status' => '501', 'message' => $exception->getMessage()]);
        }
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function flat()
    {
        return $this->belongsTo(Flat::class);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function case()
    {
        return $this->belongsTo(Caseq::class, 'case_id');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getFullNameAttribute()
    {
        return $this->fullName();
    }

    public function getBuildingNameAttribute()
    {
        return $this->buildingName();
    }

    public function getFlatNameAttribute()
    {
        return $this->flatName();
    }

    public function getContactNameAttribute()
    {
        return $this->contact->complete_name ?? '';
    }

    public function getNumberWithTowerAttribute()
    {
        if($this->flat){

            $tower = $this->flat->tower;
            if ($tower) {
                return  $this->flat->tower->tower_denomination . '-' . $this->flat->number;
            }
            return $this->flat->number;
        }

        return null;
    }


    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
