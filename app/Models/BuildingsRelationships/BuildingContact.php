<?php

namespace App\Models\BuildingsRelationships;

use App\Imports\ContactsImport;
use App\Jobs\CreateOrUpdateContactDeviceJob;
use App\Models\AccessCode;
use App\Models\Building;
use App\Models\BuildingIntercom;
use App\Models\Caseq;
use App\Models\Flat;
use App\Models\KazooFlatsConsecutive;
use App\Models\Tag;
use App\Models\User\Contact;
use App\Models\User\User;
use App\Services\CaseService;
use App\Services\KazooService;
use App\Traits\BuildingContactTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Venturecraft\Revisionable\RevisionableTrait;
use function MongoDB\select_server_for_aggregate_write_stage;
use function Termwind\renderUsing;

/**
 * @property $building_id
 * @property $flat_id
 * @property $contact_id
 * @property $description
 * @property $contact_type
 * @property $owner_or_contact
 * @property $type_of_contact_in_buildgin
 * @property $referrer
 * @property $empty_flat
 * @property $noted_2
 * @property $noted_3
 * @property $main_building
 * @property $schedule
 * @property $start_date
 * @property $end_date
 * @property $announcements
 */
class BuildingContact extends Model
{
    use CrudTrait, RevisionableTrait, SoftDeletes, BuildingContactTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'building_contact';
    protected $dontKeepRevisionOf = ['created_by', 'updated_by', 'image'];

    protected $fillable = [
        'building_id',
        'flat_id',
        'contact_id',
        'description',
        'contact_type',
        'owner_or_tenant',
        'type_of_contact_in_building',
        'referrer',
        'phone_home',
        'empty_flat',
        'notes_2',
        'notes_3',
        'main_building',
        'schedule',
        'start_date',
        'end_date',
        'announcements',
        'cases_open',
        'created_from_app',
        'dont_call',
        'phone_mobile',
        'home_is_primary_phone',
        'complete_name',
        'email',
        'ci',
        'security_word',
        'building_name',
        'image',
        'address',
        'door_number',
        'address_flat_number',
        'deleted_at',
        'total_cases_announcements',
        'intercom_incoming_call',
        'intercom_name'
    ];


    protected $with = ['flat', 'casesAnnouncementFromFlat'];

    protected $appends = [
        'created_by', 'building_contact_id', 'code', 'access_code_temporal_2', 'building_number', 'flat_number',
        'dateRangeClicked', 'real_primary_phone', 'real_secondary_phone', 'name', 'surname', 'foreign_document',
        'security_word', 'ci', 'number_kazoo', 'others_mobil_phones', 'tower_id', 'contact_email_atc'
    ];


    protected $casts = [
        'referrer' => 'integer',
        'start_date' => 'datetime:Y-m-d H:i',
        'end_date' => 'datetime:Y-m-d H:i',
    ];
    public static $contactOriginalIntercomName;

    private static $orderContactType = [
        'Contacto Principal - Comisión' => 0,
        'Contacto Principal' => 1,
        'Comisión' => 2,
        'Referente' => 3,
        'Oficina' => 4,
        'Responsable' => 5,
        'Residente' => 6,
        'Prohibido Ingreso' => 7,
        'Empleado' => 8,
        'Proveedor' => 9,
        'Autorizado' => 10,
        'No Residente' => 11,
    ];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */
    public static function createAuthorizedContactFromWittyBots($cname, $cdescription, self $buildingContact)
    {
        $contact = Contact::query()->create([
            'name' => $cname,
            'password' => bcrypt(str_random(8)),
            'birthday' => null,
            'created_by' => $buildingContact->contact_id,
        ]);

        return self::query()->create([
            'contact_id' => $contact->id,
            'building_id' => $buildingContact->building->id,
            'flat_id' => $buildingContact->flat_id,
            'description' => $cdescription,
            'main_building' => true,
            'contact_type' => 'Autorizado',
            'owner_or_tenant' => 'S/D',
            'type_of_contact_in_building' => $buildingContact->building->building_type,
            'schedule' => json_encode([]),
            'start_date' => Carbon::now()->setTimezone('America/Montevideo'),
            'end_date' => Carbon::today()->setTimezone('America/Montevideo')->endOfDay(),
        ]);
    }

    public static function updateForDeleteContact($contactId, $flatId)
    {
        $contact = self::query()
            ->where('contact_id', $contactId)
            ->where('flat_id', $flatId)
            ->first();

        $contact->update([
            'deleter_user_id' => backpack_user()->id
        ]);
    }

    /**
     * @throws \Exception
     */
    public static function updateForDeleteContactFromShow($contactId)
    {
        try {
            self::query()
                ->where('contact_id', $contactId)
                ->each(function (BuildingContact $contact) {
                    if ($contact->callflow_id) {
                        KazooService::deleteCallFlow(md5($contact->building_number), $contact->callflow_id);
                    }
                    KazooFlatsConsecutive::query()
                        ->where('contact_id', $contact->contact_id)
                        ->where('flat_id', $contact->flat_id)
                        ->delete();

                    $contact->intercom_incoming_call = false;

                    $contact->deleter_user_id = backpack_user()->id;
                    $contact->update();

                    BuildingContact::createDeleteIntercomContact($contact);

                    $contact->delete();
                });
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function createBuildingContact($building_datum, $contact_id)
    {
        try {
            $dateRangeClicked = isset($building_datum->dateRangeClicked) && $building_datum->dateRangeClicked;
            $attributes = self::attributesToSaveBuildingContact($building_datum, $contact_id, $dateRangeClicked);
            $buildingContact = self::create($attributes);
            self::addCommissionIntegrantForContact($attributes, $buildingContact);
            self::createOrUpdateKazooDeviceForBuildingContact($attributes, $buildingContact, $building_datum);
            self::createDeleteIntercomContact($buildingContact);
            self::createAccessCode($building_datum, $buildingContact);
            self::checkAndSetIncomingCallForBuildingContact($buildingContact);
            self::setContactImage($attributes, $contact_id);
        } catch
        (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public static function setContactImage(array $attributes, $contactId)
    {
        if (($attributes['start_date'] || $attributes['end_date'] || $attributes['schedule'])) {
            self::removeContactImage($contactId);
            return;
        }

        if ($attributes['main_building'] && !in_array($attributes['contact_type'], Contact::$usersTypeCanHaveImage)) {
            self::removeContactImage($contactId);
        }
    }

    public static function removeContactImage($contactId)
    {
        User::query()
            ->find($contactId)
            ->update([
                'image' => null
            ]);
        BuildingContact::query()
            ->where('contact_id', $contactId)
            ->get()
            ->each(function (BuildingContact $contact) {
                $contact->update([
                    'image' => null
                ]);
            });
    }

    private static function checkAndSetIncomingCallForBuildingContact($buildingContact)
    {
        $buildingIncomingCall = $buildingContact?->building?->incoming_call;
        if (!$buildingIncomingCall) {
            $buildingContact->intercom_incoming_call = 0;
            $buildingContact->save();
        }
    }

    private static function attributesToSaveBuildingContact($building_datum, $contact_id, $dateRangeClicked)
    {
        $attributes = [
            'main_building' => $building_datum->main_building,
            'building_id' => $building_datum->building_id,
            'flat_id' => $building_datum->flat_id,
            'contact_id' => $contact_id,
            'type_of_contact_in_building' => isset($building_datum->type_of_contact_in_building) ? $building_datum->type_of_contact_in_building : "Residencial",
            'owner_or_tenant' => isset($building_datum->owner_or_tenant) ? $building_datum->owner_or_tenant : "S/D",
            'contact_type' => isset($building_datum->contact_type) ? $building_datum->contact_type : "Residente",
            'referrer' => isset($building_datum->referrer) ? $building_datum->referrer : false,
            'empty_flat' => isset($building_datum->empty_flat) ? $building_datum->empty_flat : false,
            'description' => isset($building_datum->description) ? $building_datum->description : "",
            'notes_2' => isset($building_datum->notes_2) ? $building_datum->notes_2 : "",
            'notes_3' => isset($building_datum->notes_3) ? $building_datum->notes_3 : "",
            'phone_home' => isset($building_datum->phone_home) ? $building_datum->phone_home : "",
            'dont_call' => isset($building_datum->dont_call) ? $building_datum->dont_call : false,
            'foreign_phone' => isset($building_datum->foreign_phone) ? $building_datum->foreign_phone : false,
            'home_is_primary_phone' => isset($building_datum->phone_home) && $building_datum->phone_home && $building_datum->home_is_primary_phone == 1,
            'contact_email' => isset($building_datum->contact_email) ? $building_datum->contact_email : "",
            'address' => isset($building_datum->address) ? $building_datum->address : "",
            'door_number' => isset($building_datum->door_number) ? $building_datum->door_number : "",
            'address_flat_number' => isset($building_datum->address_flat_number) ? $building_datum->address_flat_number : "",
            'schedule' => strtolower($building_datum->contact_type) === 'autorizado'
                ? $building_datum->schedule ?? null
                : null,
            'start_date' => $building_datum->contact_type == 'Autorizado' && $dateRangeClicked && $building_datum->start_date
                ? $building_datum->start_date
                : null,
            'end_date' => $building_datum->contact_type == 'Autorizado' && $dateRangeClicked && $building_datum->end_date
                ? $building_datum->end_date
                : null,
            'announcements' => [],
            'cases_open' => null,
            'phone_mobile' => ContactsImport::getMobilePhone($building_datum->phone_mobile),
            'intercom_name' => $building_datum->intercom_name,
            'intercom_incoming_call' => $building_datum->intercom_incoming_call
        ];

        return $attributes;
    }

    private static function addCommissionIntegrantForContact($attributes, $buildingContact)
    {
        if (in_array($attributes['contact_type'], ['Contacto Principal - Comisión', 'Comisión'])) {
            CommissionIntegrant::create([
                'building_id' => $buildingContact->building_id,
                'contact_id' => $buildingContact->contact_id
            ]);
        }
    }

    /**
     * @throws \Exception
     */
    public static function createOrUpdateKazooDeviceForBuildingContact($attributes, $buildingContact, $building_datum)
    {

        try {
            if ((backpack_user()->hasRole(['Admin']) || backpack_user()->hasRole(['Representante Atención al Cliente']))
                && !in_array($attributes['contact_type'], $buildingContact->getKazooDeviceRestrictionForBuildingContact($buildingContact))
                && $buildingContact->building?->incoming_call) {
                $buildingContact->intercom_name = $building_datum->intercom_name;
                $buildingContact->intercom_incoming_call = $building_datum->intercom_incoming_call ?? false;
                if ($buildingContact->building->incoming_call) {
                    \Artisan::call('queue:restart');
                    CreateOrUpdateContactDeviceJob::dispatch($buildingContact);
                }

                $consecutivesKazoo = KazooFlatsConsecutive::query()
                    ->where('flat_id', $buildingContact->flat_id)
                    ->where('contact_id', $buildingContact->contact_id)
                    ->first();

                if ($consecutivesKazoo) {
                    $buildingContact->user_sip_kazoo = $consecutivesKazoo->user_sip_kazoo;
                    $buildingContact->kazoo_device_id = $consecutivesKazoo->device_id;
                    $buildingContact->callflow_id = $consecutivesKazoo->callflow_id;
                    $buildingContact->password_kazoo = $consecutivesKazoo->password_kazoo;
                    $buildingContact->name_kazoo = $consecutivesKazoo->name_kazoo;
                }
                $buildingContact->save();
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function getKazooDeviceRestrictionForBuildingContact($buildingContact)
    {
        $buildingType = $buildingContact?->building()?->first()?->building_type;
        return $buildingType == 'Residencial' ? config('constants.kazoo_not_create_devices_building_residential') :
            config('constants.kazoo_not_create_devices_building_office');
    }

    public static function createDeleteIntercomContact(BuildingContact $buildingContact)
    {
        // If the environment is 'local', halt execution because a VPN connection is required to update or save the contact.
        if (env('APP_ENV') == 'local') return;

        $buildingIncomingCall = $buildingContact?->building?->incoming_call;
        $nameIntercom = $buildingContact?->intercom_name;
        $intercomIncomingCall = $buildingContact?->intercom_incoming_call;
        $intercomsForKazooData = $buildingContact?->buildingIntercoms()->whereIn('model', \App\Models\BuildingIntercom::$kazooModels)->get();

        if ($intercomIncomingCall && $buildingIncomingCall) {
            foreach ($intercomsForKazooData ?? [] as $intercom) {
                if (!is_null(self::$contactOriginalIntercomName)) {
                    $intercom->intercom()->deleteContact(self::$contactOriginalIntercomName);
                }
                sleep(1);
                $intercom->intercom()->addContact($nameIntercom, $buildingContact?->number_kazoo);
            }
        } elseif (!$intercomIncomingCall && $buildingIncomingCall) {
            self::deleteIntercomContact($buildingContact);
        }
    }

    public static function deleteIntercomContact(BuildingContact $buildingContact)
    {
        $intercomsForKazooData = $buildingContact?->buildingIntercoms()->whereIn('model', \App\Models\BuildingIntercom::$kazooModels)->get();
        $intercomIncomingCall = $buildingContact?->intercom_incoming_call;
        $nameIntercom = $buildingContact?->intercom_name;

        foreach ($intercomsForKazooData ?? [] as $intercom) {

            if ($intercomIncomingCall && !is_null($nameIntercom)) {
                $intercom->intercom()->deleteContact($nameIntercom);
            }
            if (!$intercomIncomingCall && !is_null($nameIntercom)) {
                $intercom->intercom()->deleteContact($nameIntercom);
            }
            if (!$intercomIncomingCall && !is_null(Contact::$intercomName)) {
                $intercom->intercom()->deleteContact(Contact::$intercomName);
            }
        }
    }

    private static function createAccessCode($building_datum, $buildingContact)
    {
        if ((bool)Building::find($building_datum->building_id)?->access_by_pin) {
            if ((isset($building_datum->access_code) && $building_datum->access_code != null) || (isset($building_datum->access_code_temporal) && $building_datum->access_code_temporal != null)) {
                $contact = Contact::find($buildingContact->contact_id);
                if ($contact?->userAccessAuthorized() && $contact?->phone_mobile) {
                    $codes[] = $building_datum->access_code;
                    $codes[] = isset($building_datum->access_code_temporal) && !is_array($building_datum->access_code_temporal) ? $building_datum->access_code_temporal : null;
                    if ($codes[0]) {
                        $contact->generateAccessCodes($building_datum->building_id, $building_datum->flat_id, $codes[0],
                            'code');
                    }
                    if ($codes[1]) {
                        $contact->generateAccessCodes($building_datum->building_id, $building_datum->flat_id, $codes[1],
                            'code-temporal');
                    }
                }
            }
        }
    }

    public static function updateMainBuilding($contactId, $lastRelation)
    {
        $contact = self::query()
            ->where('contact_id', $contactId)
            ->first();

        if (!$contact?->main_building && !$lastRelation) {
            $contact->update([
                'main_building' => true
            ]);
        }
    }

    public function isTemporalContact()
    {
        return $this->start_date && $this->end_date;
    }

    public static function getAuthorizedContacts($buildingId, $operator = '=', $needsVerification = false)
    {
        return self::query()
            ->whereHas('contact', function (Builder $query) use ($needsVerification) {
                if ($needsVerification) {
                    $query->where('needs_verification', 1);
                } else {
                    $query->whereNull('needs_verification');
                }
            })
            ->where('building_id', $buildingId)
            ->where('contact_type', $operator, 'Autorizado');
    }

    public static function getContactsForVerification($buildingId, $operator = '=')
    {
        return self::query()
            ->whereHas('contact', function (Builder $query) {
                $query->where('needs_verification', 1);
            })
            ->where('building_id', $buildingId)
            ->where('contact_type', $operator, 'Autorizado');
    }

    public static function saveSchedule($userId, $flatId, $schedule)
    {
        if (!is_null($flatId) && isset($flatId)) {
            $contact = BuildingContact::query()
                ->where('contact_id', $userId)
                ->where('flat_id', $flatId)
                ->first();
            if (!Building::isEmptySchedule($schedule)) {
                if ($contact) {
                    $contact->update([
                        'schedule' => $schedule
                    ]);
                } else {
                    $buildingId = Flat::query()->where('id', $flatId)->value('building_id');
                    if ($buildingId && $flatId && $userId && $schedule) {
                        self::create([
                            'building_id' => $buildingId,
                            'flat_id' => $flatId,
                            'contact_id' => $userId,
                            'schedule' => $schedule,
                            'contact_type' => 'Autorizado'
                        ]);
                    }
                }

                return response()->json([
                    'data' => $contact
                ]);
            }
        }

        return response()->json([
            'data' => null
        ]);
    }

    public static function trimPhones($user)
    {
        if (!$user) {
            return;
        }
        BuildingContact::where('contact_id', $user->id)->update([
            'phone_mobile' => str_replace(' ', '', $user->phone_mobile)
        ]);
    }

    public static function trimAndReturnPhone($phone)
    {
        return str_replace(' ', '', $phone);
    }

    public function getUserTower()
    {
        if ($this->flat->count() > 0) {
            if ($this->flat->tower) {
                return $this->flat->tower->tower_denomination;
            }
        }
        return null;
    }

    public function getApiImageAbsolutePath()
    {

        $image = $this->image;

        if (str_contains($image, config('constants.foxsys_aws'))) {
            return $image;
        }


        return env('APP_URL') . $image;
    }

    public function getImageApi()
    {

        $image = $this->image;

        if (str_contains($image, config('constants.foxsys_aws'))) {
            return config('constants.image_placeholder');
        }

        return $image;
    }


    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function building()
    {
        return $this->belongsTo(Building::class, 'building_id', 'id');
    }


    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function flat()
    {
        return $this->belongsTo(Flat::class);
    }

    public function buildingIntercoms()
    {
        return $this->building->intercoms($this->getUserTower());
    }


    public function casesAnnouncementFromFlat()
    {
        return $this->hasMany(Caseq::class, 'flat_id', 'flat_id')
            ->whereNotIn('state', ['finalizado', 'CERRADO', 'FINALIZADO', 'cerrado'])
            ->where('last_category_id', config('constants.category_announcement'))
            ->where('tracing', 1);
    }

    public function tags()
    {
        return $this->hasMany(Tag::class, 'flat_id', 'flat_id');
    }

    public function accessCodes()
    {
        return $this->hasMany(AccessCode::class, 'user_id', $this->contact_id)
            ->where('flat_id', $this->flat_id);
    }

    public function casePackages()
    {
        return $this->hasMany(Caseq::class, 'user_id', 'contact_id')
            ->whereColumn("cases.flat_id", "building_contact.flat_id")
            ->whereIn('last_category_id', config('constants.package_cases'))
            ->where('cases.state', config('constants.package_state_pending'));
    }


    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeSortContactsWithTower($query)
    {
        return $query->selectRaw('*, ' . $this->getContactOrderExpression())
            ->orderBy('contact_order', 'asc')
            ->orderBy('tower_denomination', 'asc')
            ->orderByRaw('CAST(flat_number AS UNSIGNED) asc');
    }

    public function scopeSortContactsWithoutTower($query)
    {
        return $query->selectRaw('*, ' . $this->getContactOrderExpression())
            ->orderBy('contact_order', 'asc');
    }

    private function getContactOrderExpression()
    {
        $orderMapping = self::$orderContactType;
        $caseStatements = $this->setupOrderExpression($orderMapping);

        return "CASE
            WHEN referrer = 1 AND (
                CASE contact_type
                    {$caseStatements}
                    ELSE 12
                END
            ) >= 3 THEN 3
            ELSE (
                CASE contact_type
                    {$caseStatements}
                    ELSE 12
                END
            )
        END as contact_order";  // Agregamos un alias
    }


    private function setupOrderExpression(array $orderMapping)
    {
        $statements = '';
        foreach ($orderMapping as $contactType => $orderValue) {
            $statements .= "WHEN '" . addslashes($contactType) . "' THEN " . $orderValue . " ";
        }
        return $statements;
    }


    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getContactEmailAtcAttribute()
    {
        return backpack_user()->hasRole('Representante Atención al Cliente') ? $this->email : '';
    }

    public function getTowerIdAttribute()
    {
        return $this->flat?->tower_id;
    }

    protected function startDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? Carbon::parse($value)->format('Y-m-d H:i') : null,
        );
    }

    protected function temporaryContact(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->attributes['start_date'] && $this->attributes['end_date']
        );
    }

    protected function endDate(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? Carbon::parse($value)->format('Y-m-d H:i') : null,
        );
    }

    public function getDateRangeClickedAttribute()
    {
        return $this->start_date && $this->end_date ? 1 : 0;
    }

    public function getBuildingContactIdAttribute()
    {
        return $this->id;
    }

    public function getPrimaryPhone()
    {
        return $this->home_is_primary_phone ? $this->phone_home : $this->contact?->phone_mobile;
    }

    public function getSecondaryPhone()
    {
        return $this->home_is_primary_phone ? $this->contact?->phone_mobile : $this->phone_home;
    }

    public function getCodeAttribute()
    {
        return $this->accessCode && $this->accessCode->isNotEmpty()
            ? $this->accessCode->whereNull('expires_at')->first()
            : null;
    }

    public function getAccessCodeTemporal2Attribute()
    {
        return $this->accessCode && $this->accessCode->isNotEmpty()
            ? $this->accessCode->whereNotNull('expires_at')
            : collect();
    }

    public function getBuildingNumberAttribute()
    {
        return $this->building?->building_number;
    }

    public function getSecurityWordAttribute()
    {
        return $this->contact?->security_word;
    }


    public function getFlatNumberAttribute()
    {
        return $this->flat?->number_with_tower;
    }

    public function getCreatedByAttribute()
    {
        return $this->contact?->created_by;
    }

    public function getCiAttribute()
    {
        return $this->contact?->ci;
    }

    //Todo: hacer relacion
    public function getNumberKazooAttribute()
    {
        $kazooFlatsConsecutives = KazooFlatsConsecutive::query()->where('contact_id', $this->contact_id)->where('flat_id',
            $this->flat_id)->value('kazoo_number');
        return $this->kazoo_device_id && $kazooFlatsConsecutives ? $kazooFlatsConsecutives : "";
    }

    public function getNameAttribute()
    {
        return $this->contact?->name;
    }

    public function getSurNameAttribute()
    {
        return $this->contact?->surname;
    }

    public function getForeignDocumentAttribute()
    {
        return $this->contact?->foreign_document;
    }

    public function getImageAttribute()
    {
        return $this->contact?->image;
    }

    public function getRealSecondaryPhoneAttribute()
    {
        $secondaryPhone = !$this->home_is_primary_phone ? $this->phone_home : $this->phone_mobile;
        return $secondaryPhone != $this->real_primary_phone ? $secondaryPhone : "";
    }

    public function getRealPrimaryPhoneAttribute()
    {
        return $this->home_is_primary_phone ? $this->phone_home : (empty($this->phone_mobile) ? $this->phone_home : $this->phone_mobile);
    }

    public function getEmailAttribute()
    {
        return $this->contact?->email;
    }

    public function getOthersMobilPhonesAttribute()
    {
        return $this->contact?->getOnlyExtraPhone();
    }


    public function getAnnouncementsAttribute(): string
    {
        return $this->casesAnnouncementFromFlat
            ->filter(fn($case) => is_null($case->user_id) || $case->user_id === $this->contact_id)
            ->map(fn($case) => [
                'title'       => $case->title,
                'description' => $case->description,
            ])
            ->values()
            ->toJson();
    }


    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    public function setNameAttribute($name)
    {
        $this->attributes['name'] = strtoupper($name);
    }

}
