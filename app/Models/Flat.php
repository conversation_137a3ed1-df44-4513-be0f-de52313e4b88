<?php

namespace App\Models;

use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\User\Contact;
use App\Models\User\User;
use App\Traits\FlatTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Venturecraft\Revisionable\RevisionableTrait;

/**
 * @property $number_with_tower
 */

class Flat extends Model
{
    use CrudTrait, SoftDeletes, RevisionableTrait, FlatTrait;

    public function identifiableName()
    {
        return $this->number;
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'flats';
    protected $dontKeepRevisionOf = ['created_by', 'updated_by'];

    // protected $primaryKey = 'id';
    // public $timestamps = false;
    // protected $guarded = ['id'];
    protected $fillable = ['number', 'building_id', 'tags', 'created_by', 'updated_by', 'asterisk_extension_number', 'asterisk_extension_password','tower_id', 'number_with_tower'];
//    protected $appends = ['number_with_tower'];
    // protected $hidden = [];
    // protected $dates = [];



    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    public static function updateFlatOnly($searchIdxArray, $tower_flats_original, $tower, $flat)
    {
        $replace_value = array($searchIdxArray => $flat->number);
        $final_value_flats = array_replace($tower_flats_original, $replace_value);
        $value_to_string = implode(',', $final_value_flats);
        $tower->flats = $value_to_string;
        $tower->save();

    }

    public static function removeFlatFromTower($tower_flats_original, $searchIdxArray, $tower)
    {
        unset($tower_flats_original[$searchIdxArray]);
        $value_to_string = implode(',', $tower_flats_original);
        $tower->flats = $value_to_string;
        $tower->save();

    }

    public static function insertFlatIntoNewTower($id, $flat)
    {
        $tower_old_id = Flat::select('tower_id')->where('id', $id)->first();
        $flat_new = Tower::findOrFail($tower_old_id->tower_id);

        $news_falts_to_append = $flat_new->flats . ',' . $flat->number;
        $flat_new->flats = $news_falts_to_append;
        $flat_new->save();
    }

    function buildingName()
    {
        if ($this->building) {
            return $this->building->name;
        }
        return 'No se asignó edificio.';
    }

    function buildingNumber()
    {
        if ($this->building) {
            return $this->building->building_number;
        }
        return 'No se asignó edificio.';
    }

    public static function rearrangeFlats()
    {
        $deletedIds = [];

        foreach (Flat::all() as $currentFlat) {

            if (!in_array($currentFlat->id, $deletedIds)) {

                foreach (Flat::all() as $flat) {

                    if (Flat::repeatedFlat($currentFlat, $flat) && $currentFlat != $flat) {

                        foreach ($flat->users as $user) {
                            $user->flat_id = $currentFlat->id;
                            $user->save();
                        }

                        array_push($deletedIds, $flat->id);
                        $flat->delete();

                    }
                }
            }
        }
        return 'OK';
    }

    static function repeatedFlat($flat1, $flat2)
    {
        if ($flat1->building_id == $flat2->building_id
            && $flat1->number === $flat2->number) {
            return true;
        }
        return false;
    }

    function addCars($cars)
    {
        try {
            foreach (json_decode($cars, true) as $carData) {
                $car = new Car();
                $car->fill($carData);
                $car->needs_verification = true;
                $car->save();
                $this->cars()->attach($car->id);
            }
            throw new \Exception('No se puede asociar un vehiculo a un apartamento.');
        } catch (\Exception $e) {
            return json_encode([
                'status' => '500',
                'message' => 'No se puede asociar un vehiculo a un apartamento. Error: ' . $e,
            ]);
        }
    }

    function temporaryContacts()
    {
        return $this->hasMany(TemporaryContact::class, 'flat_id', 'id')
            ->where('end_date', '>=', Carbon::now()->setTimezone('America/Montevideo')->toDateTimeString());
    }

    public static function isSameFlatNumber($flat, $flat_number)
    {
        return str_replace(' ', '', $flat->number) === str_replace(' ', '', $flat_number);
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    function building()
    {
        return $this->belongsTo(Building::class);
    }

    public
    function tower()
    {
        return $this->belongsTo(Tower::class);
    }

    public
    function tags()
    {
        return $this->hasMany(Tag::class);
    }

    public
    function contacts()
    {
        return $this->hasManyThrough(
            Contact::class,
            BuildingContact::class,
            'flat_id',
            'id',
            'id',
             'contact_id'
        );
    }

    public
    function intercomCards()
    {
        return $this->hasMany(IntercomCards::class, 'flat_id', 'id');
    }

    public function users()
    {
        return $this->hasManyThrough(
            User::class,
            BuildingContact::class,
            'contact_id',
            'id',
            'id',
            'id'
        );
    }

    function cases()
    {
        return $this->hasMany('App\Models\Caseq');
    }

    public function buildingIntercoms(): BelongsToMany
    {
        return $this->belongsToMany(BuildingIntercom::class, 'building_intercom_flat');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getFullNameAttribute()
    {
        return $this->number . ' - ' . $this->buildingName();
    }


    public function getAsteriskExtensionNumberAttribute()
    {
        if (array_key_exists('asterisk_extension_number', $this->attributes)) {
            return $this->attributes['asterisk_extension_number'];
        }
    }

    function getAsteriskExtensionPasswordAttribute()
    {
        if (array_key_exists('asterisk_extension_password', $this->attributes)) {
            return $this->attributes['asterisk_extension_password'];
        }
    }
    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

}
