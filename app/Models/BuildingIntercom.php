<?php

namespace App\Models;

use App\Integrations\Intercoms\IIntercom;
use App\Integrations\Intercoms\IntercomE18Akuvox;
use App\Integrations\Intercoms\IntercomR20KAkuvox;
use App\Integrations\Intercoms\IntercomR29Akuvox;
use App\Models\User\User;
use App\Models\BuildingsRelationships\BuildingContact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Jenssegers\Mongodb\Eloquent\SoftDeletes;
use Venturecraft\Revisionable\RevisionableTrait;

class BuildingIntercom extends Model
{
    use \Backpack\CRUD\app\Models\Traits\CrudTrait;
    use SoftDeletes;
    use RevisionableTrait;

    public function identifiableName()
    {
        return $this->ip;
    }

    protected $dontKeepRevisionOf = ['created_by', 'updated_by'];

    protected $table = 'building_intercoms';
    protected $guarded = ['id'];

    protected $fillable = [
        'building_id',
        'building_name',
        'intercom_service',
        'ip',
        'switch',
        'door_name',
        'asterisk_extension_number',
        'tower_prefix',
        'http_port',
        'num',
        'delay',
        'intercom_type',
        'model',
        'has_keyboard',
        'extension_3cx',
        'rtsp_url',
        'access_group',
        'order_group',
        'password',
        'user',
        'action'
    ];

    protected $appends = [
        'number_with_tower',
        'associated_flat'
    ];
    public static $kazooModels = [
        'R29',
        'E18',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            $post->{$post->getKeyName()} = (string)\Str::uuid();
        });
    }

    /*
   |--------------------------------------------------------------------------
   | FUNCTIONS
   |--------------------------------------------------------------------------
   */
    public function getIncrementing()
    {
        return false;
    }

    public function getKeyType()
    {
        return 'string';
    }

    public static function getBuildingIntercomsByBuilding($building_id)
    {
        return BuildingIntercom::where('building_id', Building::query()->where('id', $building_id)->value('building_number'))
            ->where('has_keyboard', true)
            ->pluck('id')->toArray();
    }

    public static function storeIntercoms()
    {
        $attributes = request()->all();
        if ($attributes['rtsp_url'] == "[{}]") {
            $attributes['rtsp_url'] = null;
        }
        if ($attributes['intercom_service'] == 'PUERTA')
            $attributes['flats'] = null;
        $buildingIntercom = self::create($attributes);
        $buildingIntercom->flats()->sync(request()->input('flats'));
        $buildingIntercom->model = $buildingIntercom->intercomModel->model;

        return $buildingIntercom;
    }

    public static function updateIntercoms()
    {
        $attributes = request()->all();
        $buildingIntercom = self::findOrFail($attributes['id']);
        $intercomService = array_key_exists('intercom_service', $attributes) ? $attributes['intercom_service'] : $buildingIntercom->intercom_service;

        if ($attributes['rtsp_url'] == "[{}]") {
            $attributes['rtsp_url'] = null;
        }

        if ($intercomService == 'PUERTA')
            $attributes['flats'] = null;

        $buildingIntercom->update($attributes);
        $buildingIntercom->flats()->sync(request()->input('flats'));


        $buildingIntercom->model = $buildingIntercom->intercomModel->model;
        $buildingIntercom->save();
        return self::find(request()->input('id'));
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function flat(): BelongsTo
    {
        return $this->belongsTo(Flat::class);
    }


    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function intercomModel()
    {
        return $this->belongsTo(IntercomModel::class, 'intercom_type');
    }


    public function intercom()
    {
        if ($this->intercomModel->model == Config('constants.akuvox_r29'))
            return new \App\Integrations\Intercoms\IntercomR29Akuvox($this);

        if ($this->intercomModel->model == Config('constants.akuvox_e18'))
            return new \App\Integrations\Intercoms\IntercomE18Akuvox($this);

        if ($this->intercomModel->model == Config('constants.akuvox_r20k')) {
            return new \App\Integrations\Intercoms\IntercomR20KAkuvox($this);
        }
        if ($this->intercomModel->model == Config('constants.2n')) {
            return new \App\Integrations\Intercoms\Intercom2N($this);
        }
        if ($this->intercomModel->model == Config('constants.adam')) {
            return new \App\Integrations\Intercoms\IntercomAdam($this);
        }
    }


    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getFakeModelAttribute()
    {
        return $this->intercomModel->model ?? '';
    }

    public function getNumberWithTowerAttribute()
    {
        return $this->flat?->number_with_tower ?? '';
    }

    public function getAssociatedFlatAttribute()
    {
        return $this->flats;
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    public function setBuildingIdAttribute($value)
    {
        $this->attributes['building_id'] = Building::find($value)->building_number;
        $this->attributes['building_name'] = Building::find($value)->name;
    }

    public function flats(): BelongsToMany
    {
        return $this->belongsToMany(Flat::class, 'building_intercom_flat');
    }

}
