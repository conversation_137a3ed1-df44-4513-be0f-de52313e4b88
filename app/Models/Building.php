<?php

namespace App\Models;

use App\Http\Helpers\DoesNotNeedVerificationScope;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\BuildingsRelationships\BuildingService;
use App\Models\BuildingsRelationships\CommissionIntegrant;
use App\Models\Company\Administration;
use App\Models\Company\Company;
use App\Models\User\Comercial;
use App\Models\User\Commission;
use App\Models\User\Contact;
use App\Models\User\Operator;
use App\Models\User\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Doctrine\DBAL\Exception\InvalidArgumentException;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Venturecraft\Revisionable\RevisionableTrait;
use Illuminate\Notifications\Notifiable;
use function PHPUnit\Framework\isNull;

class Building extends Model
{
    use CrudTrait, SoftDeletes, RevisionableTrait, Notifiable;

    public static function boot()
    {
        parent::boot();
    }

    public function scopeIsLead($query)
    {
        unset($query->getQuery()->wheres[0]);

        return $query->where('lead', '!=', null);
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    public function identifiableName()
    {
        return $this->name;
    }

    protected $table = 'buildings';
    // protected $primaryKey = 'id';
    // public $timestamps = false;
    // protected $guarded = ['id'];
    protected $dontKeepRevisionOf = ['created_by', 'updated_by'];

    protected $fillable = [
        'name',
        'building_type',
        'service_type',
        'address',
        'city',
        'postal_code',
        'country',
        'service_level',
        'phone',
        'mail',
        'between_streets',
        'comments',
        'security_comments',
        'gamma_code',
        'doorman_format',
        'measures_locations',
        'doorman_service_hours',
        'building_doorman_hours',
        'opens_from_flat',
        'doors_quantity',
        'operator_id',
        'gates_ramps_controllers',
        'installation_date',
        'administration_id',
        'schedule',
        'service_start_date',
        'tags_delivery_by',
        'tags_delivery_date',
        'mon_service',
        'thu_service',
        'wen_service',
        'tha_service',
        'fre_service',
        'sat_service',
        'sun_service',
        'allows_elevator_comunication',
        'admin_name',
        'admin_mail',
        'admin_number',
        'installation_has_special_hours',
        'installation_time',
        'extra_keys_porter',
        'extra_keys_admin',
        'extra_keys_clear',
        'extra_keys_other',
        'door_special_denomination',
        'tower_access',
        'flats',
        'lead',
        'comercial_email',
        'porter',
        'order_job_checked',
        'order_job',
        'contract',
        'rev_security',
        'lock',
        'image',
        'building_number',
        'asterisk_extension_number',
        'asterisk_extension_password',
        'open_door_disabled',
        'initial_date',
        'before_delivery',
        'delivery',
        'delivery_comment',
        'app_foxsys',
        'video_disabled',
        'number_of_internet_contrat',
        'gates_ramps_denomination',
        'gates_ramps_file',
        'cleaning_time',
        'security_comments_2',
        'security_comments_3',
        'access_by_pin',
        'access_by_pin_permanent',
        'access_by_pin_temporary',
        'building_state',
        'estimated_start_date',
        'agreed_start_date',
        'agreed_start_time',
        'account_kazoo',
        'domain_kazoo',
        'account_kazoo_id',
        'incoming_call',
        'cadete_hall',
        'cadete_hall_comments',
        'cadete_up',
        'cadete_up_comments',
        'services_comments',
        'records_comments',
        'tags_comments',
        'investigations_comments',
        'others_comments',
        'rules_comments',
        'arrangements_comments',
        'malfunctions_comments',
        'moving_comments',
        'commercial_locals_comments',
        'bathrooms_comments',
        'fire_alarm_comments',
        'rooftop_comments',
        'others_alarms_comments',
        'aditional_instructions_comments',
        'phones_comments',
        'speakers_comments',
        'sirens_comments',
        'integrated_alarms_comments',
        'amenities',
        'instructions_explanations',
        'instructions_explanations_comments',
        'medidores_comments',
        'face_recognition',
        'camera_anpr',
        'rev_security2',
        'rev_security3',
        'rev_security4',
    ];


    protected $week_days = [
        'monday' => 'Lun',
        'tuesday' => 'Mar',
        'wednesday' => 'Mie',
        'thursday' => 'Jue',
        'friday' => 'Vie',
        'saturday' => 'Sab',
        'sunday' => 'Dom'
    ];

    protected $work_week_days = [
        'monday' => 'Lun',
        'tuesday' => 'Mar',
        'wednesday' => 'Mie',
        'thursday' => 'Jue',
        'friday' => 'Vie',
    ];

    protected $week_end_days = [
        'saturday' => 'Sab',
        'sunday' => 'Dom'
    ];

    public static $iconsContactTypes = [
        'Contacto Principal - Comisión' => 'bag-check',
        'Contacto Principal' => 'bag-check',
        'Comisión' => 'ribbon',
        'Referente' => 'compass',
        'Oficina' => 'cafe',
        'Responsable' => 'flag',
        'Residente' => 'person',
        'Prohibido Ingreso' => 'ban',
        'Empleado' => 'hammer',
        'Proveedor' => 'cube',
        'Cadete' => 'cube',
        'Autorizado' => 'walk',
        'No Residente' => 'briefcase',
        'Foxsys' => 'body',
    ];

    public static $iconsContactTypesBuildingShow = [
        'Contacto Principal - Comisión' => ['person', 'bag-check', 'ribbon'],
        'Contacto Principal' => ['person', 'bag-check'],
        'Comisión' => ['person', 'ribbon'],
        'Referente' => ['compass'],
        'Oficina' => ['cafe'],
        'Responsable' => ['flag'],
        'Residente' => ['person'],
        'Prohibido Ingreso' => ['ban'],
        'Empleado' => ['hammer'],
        'Proveedor' => ['cube'],
        'Cadete' => ['cube'],
        'Autorizado' => ['walk'],
        'No Residente' => ['briefcase'],
        'Foxsys' => ['body'],
    ];

    const BUILDING_STATE = [
        'pending' => ['text' => 'En espera', 'color' => '--ColorOrangeSec'],
        'active' => ['text' => 'Activo', 'color' => '--ColorGreen'],
        'installation' => ['text' => 'Instalación', 'color' => '--ColorBlue'],
        'inactive' => ['text' => 'Inactivo', 'color' => '--ColorRed'],
    ];

    private static $orderContactType = [
        'Contacto Principal - Comisión' => 0,
        'Contacto Principal' => 1,
        'Comisión' => 2,
        'Referente' => 3,
        'Oficina' => 4,
        'Responsable' => 5,
        'Residente' => 6,
        'Prohibido Ingreso' => 7,
        'Empleado' => 8,
        'Proveedor' => 9,
        'Autorizado' => 10,
        'No Residente' => 11,
    ];

    // protected $hidden = [];
    // protected $dates = [];


    protected $with = ['operator', 'administration', 'towers'];

    protected $appends = ['search_data'];

    public static $buildingServiceType = [
        'Portería',
        'Híbrido',
        'Vigilancia',
        'Acceso'
    ];

    public static $buildingServiceIcons = [
        'Portería' => ['type' => 'svg', 'icon' => 'porteria'],
        'Híbrido' => ['type' => 'svg', 'icon' => 'hibrido'],
        'Vigilancia' => ['type' => 'ion', 'icon' => 'videocam-outline'],
        'Acceso' => ['type' => 'ion', 'icon' => 'key-outline'],
    ];


    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    public function updateFlats()
    {
        $this->update([
            'flats' => implode(',', Flat::query()->where('building_id', $this->id)->pluck('number_with_tower')->toArray())
        ]);
    }

    public function scopeOnlyDeleted($query)
    {
        return $query->withoutGlobalScopes()->where('deleted_at', '!=', null);
    }

    public function scopeNumberCases($query)
    {
        return $query->withoutGlobalScopes()->join('cases', 'buildings.id', '=', 'cases.building_id')->where('cases.state', '!=', 'finalizado')->groupBy('buildings.id')->select('buildings.*');
    }

    public function tagsCodes()
    {
        if ($this->flats) {
            return implode($this->flats->tags->name, [',']);
        }
        return 'No se asignó edificio.';
    }

    public function isActive()
    {
        $isActive = 0;
        $activeHours = $this->activeHours()->get();
        if ($activeHours) {
            $days = json_decode($activeHours->active_hours);
            if (Building::hourInSlider($days)) {
                $isActive = 1;
            }
            if (!$isActive && $activeHours->second_active_hours != '{}') {
                $days2 = json_decode($activeHours->second_active_hours);
                if (Building::hourInSlider($days2)) {
                    $isActive = 1;
                }
            }
        }
        return $isActive;
    }

    public static function hourInSlider($active_hours)
    {
        $today = strtolower(Carbon::now()->format('l'));
        $currentHour = Building::hourToAmount(Carbon::now()->setTimezone('America/Montevideo')->format('H:i'));
        $isInSlider = true;
        foreach ($active_hours as $day => $hours) {

            $startHour = Building::hourToAmount(explode('-', $hours)[0]);
            $endHour = Building::hourToAmount(explode('-', $hours)[1]);

            if ($day === $today) {
                if ($currentHour <= $startHour || $currentHour >= $endHour) {
                    $isInSlider = false;
                }
            }
        }
        return $isInSlider;
    }

    public static function hourToAmount($hour)
    {
        return explode(':', $hour)[0] * 60 + explode(':', $hour)[1];
    }

    public function buildingHasATower()
    {
        return $this->towers()->count() > 0;
    }

    public function setContactInfoToSort($contacts, $building_id)
    {
        foreach ($contacts as $c) {
            if ($c->building_id != $building_id) {
                if (isset($c->flats) && sizeof($c->flats) != 0) {
                    $building_contact = $this->contacts()->get();
                    $all_flats = $c->flats;
                    foreach ($all_flats as $f) {
                        if ($f->building_id == $building_id) {
                            $c->flat_id = $f->id;
                            $c->building_id = $f->building_id;
                            $c->flat_number = $f->number_with_tower;
                        }
                    }
                    foreach ($building_contact as $b_c) {
                        if ($b_c->building_id == $building_id) {
                            $c->contact_type = $b_c->contact_type;
                            $c->owner_or_tenant = $b_c->owner_or_tenant;
                            $c->referrer = $b_c->referrer;
                            $c->description = $b_c->description;
                            $c->notes_2 = $b_c->notes_2;
                            $c->notes_3 = $b_c->notes_3;
                            $c->announcements = $b_c->announcements;
                        }
                    }
                }
            }

        }
        return $contacts;
    }

    public static function initializeBuildingsActiveHours()
    {
        foreach (BuildingActiveHours::all() as $buildingHours) {
            $building = Building::findOrFail($buildingHours->building_id);
            $building->setHasActiveHours();
        }
        return 'OK';
    }

    public function setHasActiveHours()
    {
        $this->has_active_hours = true;
        $this->save();
    }

    function getImagePath()
    {
        if ($this->image) {
            return '/' . $this->image;
        }
        return '/uploads/buildings/no-image.png';
    }

    public function getFormattedActiveHours()
    {
        $active_hours = json_decode($this->activeHours[0]->active_hours, true);
        $second_active_hours = json_decode($this->activeHours[0]->second_active_hours, true);

        if ($this->isSameWeekHours($active_hours, $second_active_hours, $this->week_days)) {
            return $this->returnSameFullWeekHours($active_hours, $second_active_hours);
        } elseif ($this->isSameWeekHours($active_hours, $second_active_hours, $this->work_week_days)) {
            return $this->returnSameWorkWeekHours($active_hours, $second_active_hours, $this->week_days);
        }

        return $this->returnDifferentWeekHours($active_hours, $second_active_hours, $this->week_days);
    }


    public function isSameWeekHours($active_hours, $second_active_hours, $days)
    {
        $hour = isset($active_hours[array_key_first($days)]) ? $active_hours[array_key_first($days)] : null;
        $second_hour = isset($second_active_hours[array_key_first($days)]) ? $second_active_hours[array_key_first($days)] : null;

        foreach ($days as $day => $parsed) {

            if (isset($active_hours[$day])) {
                if ($active_hours[$day] != $hour) {
                    return false;
                }
            }

            if (isset($second_active_hours[$day])) {
                if ($second_active_hours[$day] != $second_hour) {
                    return false;
                }
            }
        }
        return true;
    }

    public function returnSameFullWeekHours($active_hours, $second_active_hours)
    {

        $hour = isset($active_hours['monday']) ? $active_hours['monday'] : null;
        $second_hour = isset($second_active_hours['monday']) ? $second_active_hours['monday'] : null;

        if ($second_hour) {
            $hour .= ' y ' . $second_hour;
        }

        return 'Todos los días: ' . $hour;
    }

    public function returnSameWorkWeekHours($active_hours, $second_active_hours, $work_week_days)
    {

        $hour = isset($active_hours['monday']) ? $active_hours['monday'] : null;
        $second_hour = isset($second_active_hours['monday']) ? $second_active_hours['monday'] : null;

        $parsed = 'Lunes a Viernes: ' . $hour;

        if ($second_hour) {
            $parsed .= ' y ' . $second_hour;
        }

        if ($this->isSameWeekHours($active_hours, $second_active_hours, $this->week_end_days)) {
            $parsed .= ', ' . $this->returnSameWeekEndHours($active_hours, $second_active_hours, $this->week_days);
        } else {
            $parsed .= ', ' . $this->parseDay($active_hours, $second_active_hours, 'saturday', 'Sábado');
            $parsed .= $this->parseDay($active_hours, $second_active_hours, 'sunday', 'Domingo');
        }
        return rtrim($parsed, ', ');
    }


    public function returnSameWeekEndHours($active_hours, $second_active_hours, $week_end_days)
    {
        $hour = isset($active_hours['saturday']) ? $active_hours['saturday'] : null;
        $second_hour = isset($second_active_hours['saturday']) ? $second_active_hours['saturday'] : null;

        if ($second_hour) {
            $hour .= ' y ' . $second_hour;
        }

        return rtrim('Sábados y domingos: ' . $hour, ', ');
    }

    public function returnDifferentWeekHours($active_hours, $second_active_hours, $full_week_days)
    {
        $parsed_string = '';
        foreach ($full_week_days as $day => $parsed) {
            $parsed_string .= $this->parseDay($active_hours, $second_active_hours, $day, $parsed);
        }
        return rtrim($parsed_string, ', ');
    }

    public function parseDay($active_hours, $second_active_hours, $day, $parsed)
    {
        $parsed_day = '';
        if (isset($active_hours[$day])) {
            $parsed_day .= $parsed . ' : ' . $active_hours[$day];
            if (isset($second_active_hours[$day])) {
                $parsed_day .= ' y ' . $second_active_hours[$day];
            }
        }
        return $parsed_day . ', ';
    }

    public static function getTowersCount($buildingId)
    {
        return self::where('id', $buildingId)->withCount('towers')->value('towers_count');
    }

    public static function sortedContacts($contacts)
    {
        if (sizeof($contacts) > 0 && self::getTowersCount($contacts->first()->building_id) == 0)
            return self::sortedContactTypeBuildingWithoutTower($contacts);

        return self::sortedContactTypeBuildingWithTower($contacts);
    }


    private static function sortedContactTypeBuildingWithTower($contacts)
    {
        return $orderedUsers = $contacts->sortBy(function ($user) {
            $order = self::$orderContactType;

            $typeOrder = $order[$user->contact_type] ?? 12;

            if ($user->referrer == 1 && ($order[$user->contact_type] ?? 0) >= 3) {
                $typeOrder = 3;
            }

            $apartmentNumber = $user->flat_number;
            $apartmentParts = explode('-', $apartmentNumber);
            $building = $apartmentParts[0] ?? '';
            $number = $apartmentParts[1] ?? '';

            $buildingOrder = str_split($building);
            $buildingOrderIndex = implode('', $buildingOrder);

            return [$typeOrder, $buildingOrderIndex, $number];
        });
    }

    private static function sortedContactTypeBuildingWithoutTower($contacts)
    {
        return $orderedUsers = $contacts->sortBy(function ($user) {
            $order = self::$orderContactType;

            if ($user->referrer == 1 && ($order[$user->contact_type] ?? 0) >= 3) {
                return [3];
            }

            $typeOrder = $order[$user->contact_type] ?? 12;
            return [$typeOrder];
        });

    }

    public function sortOfContactType($contacts)
    {
        $new_array_order = [];
        if (strtoupper($this->building_type) == 'RESIDENCIAL') {
            $types = Contact::$sortedToTableTypesOnResident;
        } else {
            $types = Contact::$sortedToTableTypesOnOffice;
        }


        foreach ($types as $type) {
            foreach ($contacts as $contact) {
                $contat_type = $contact->contact_type;
                if ($contact->secondaryInfo) {
                    foreach ($contact->secondaryInfo as $secondary) {
                        if ($secondary->building_id == $this->id) {
                            $contat_type = $secondary->contact_type;
                        }
                    }
                }
                if ($contat_type == $type) {
                    array_push($new_array_order, $contact);
                }
            }
        }
        return collect(array_reverse($new_array_order));
    }

    public function addTypeWithSecondSort($type, $contacts, $sorted_contacts)
    {


        $this->addToCollectionExceptSecondTypes(
            $sorted_contacts,
            $contacts,
            'contact_type',
            $type,
            'owner_or_tenant',
            ['Propietario', 'Inquilino']
        );

        $this->addToCollection(
            $sorted_contacts,
            $contacts,
            'contact_type',
            $type,
            'owner_or_tenant',
            'Propietario'
        );
        $this->addToCollection(
            $sorted_contacts,
            $contacts,
            'contact_type',
            $type,
            'owner_or_tenant',
            'Inquilino'
        );

        $this->addToCollection(
            $sorted_contacts,
            $contacts,
            'contact_type',
            $type,
            'referrer',
            1
        );
    }


    public function sortedServices()
    {
        $services = $this->services()->get();

        $sorted_services = Collect();
        $this->addToCollectionExceptTypes($sorted_services, $services, 'service', ['Portería', 'Residente']);
        $this->addToCollection($sorted_services, $services, 'service', 'Recepción');
        $this->addToCollection($sorted_services, $services, 'service', 'Portería');

        return $sorted_services;
    }


    public function addToCollection($collection, $objects, $column, $type, $second_column = null, $second_types = null)
    {
        foreach ($objects as $obj) {
            if ($obj->$column === $type) {
                if ($second_column) {
                    if ($obj->$second_column && $obj->$second_column === $second_types) {
                        $collection->add($obj);
                    }
                } else {
                    $collection->add($obj);
                }
            }
        }
        return $collection;
    }

    public function addToCollectionExceptTypes($collection, $objects, $column, $types)
    {
        foreach ($objects as $obj) {
            if (!in_array($obj->$column, $types)) {
                $collection->add($obj);
            }
        }
        return $collection;
    }

    public function addToCollectionExceptSecondTypes($collection, $objects, $column, $type, $second_column, $second_types)
    {
        foreach ($objects as $obj) {
            if ($obj->$column === $type) {

                // Lógica de negocio. residente referente no se puede agregar duplicado
                if ($obj->referrer === 1)
                    continue;

                else if (!in_array($obj->$second_column, $second_types)) {
                    $collection->add($obj);
                }
            }
        }
        return $collection;
    }

    private function getUserFlatNumbers($userId)
    {
        $userFlats = [];
        if ($userId) {
            $flatsIds = BuildingContact::where('contact_id', $userId)->get()->pluck('flat_id');
            foreach (Flat::whereIn('id', $flatsIds)->get() as $flat) {
                array_push($userFlats, $flat->number);
            }
        }
        return $userFlats;
    }

    public function intercomsData($userId = false)
    {
        $userFlats = $this->getUserFlatNumbers($userId);

        $response = [];

        foreach ($this->intercoms as $intercom) {
            $associatedFlats = $intercom->associated_flat->pluck('number')->toArray();
            foreach ($userFlats as $flat) {
                if (!in_array($flat, $associatedFlats) && $intercom->intercom_service != "PUERTA") {
                    continue;
                }
                $response[] =
                    [
                        'intercom_id' => $intercom->id,
                        'door_name' => $intercom->door_name,
                        'asterisk_extension_number' => (string)$intercom->asterisk_extension_number,
                        'tower_prefix' => $intercom->tower_prefix,
                        'ip' => $intercom->ip,
                        'has_keyboard' => $intercom->has_keyboard,
                        'rtsp_url' => $intercom->rtsp_url,
                        'intercom_service' => $intercom->intercom_service
                    ];
            }
        }
        return \GuzzleHttp\json_encode($response);
    }

    function extractInfoToCallCleaningModal($json)
    {
        $data = json_decode($json);
        $value = '';
        $mail = '<EMAIL>';
        foreach ($data as $dato) {
            $link = '';
            if (!empty($dato->number)) {
                $link = sprintf('<a href="#" onclick="createInteractionCallPorterAndCleaning(\'%s\',\'%s\')">%s</a>', $mail, $dato->number, $dato->number);
            }
            $ci = isset($dato->ci) ? Contact::formatDocumentByCi($dato->ci, false) : '';
            $value .= $dato->name . ' ' . $ci . ' ' . $dato->hour . ' ' . $link . '  ' . "<br>";
        }
        return $value;
    }

    function extractInfoToCallPorterModal($json)
    {
        $data = json_decode($json);
        $value = '';
        $mail = '<EMAIL>';
        foreach ($data as $dato) {
            $link = '';
            if (!empty($dato->number)) {
                $link = sprintf('<a href="#" onclick="createInteractionCallPorterAndCleaning(\'%s\',\'%s\')">%s</a>', $mail, $dato->number, $dato->number);
            }
            $ci = isset($dato->ci) ? Contact::formatDocumentByCi($dato->ci, false) : '';
            $value .= $dato->name . ' ' . $ci . ' ' . $dato->hour . ' ' . $link . '  ' . "<br>";
        }
        return $value;
    }

    function extractInfoToTitleModal($json)
    {
        $data = json_decode($json);
        $value = '';
        foreach ($data as $dato) {
            $ci = isset($dato->ci) ? Contact::formatDocumentByCi($dato->ci, false) : '';
            $value .= $dato->name . ' ' . $ci . ' ' . $dato->hour . ' ' . $dato->number . "\n";
        }
        return $value;
    }


    public function formatPorter($data)
    {
        $porter = '';
        foreach ($data as $p) {
            $porter = $porter . json_encode($p) . ',';
        }
        $porter = substr($porter, 0, -1);
        $this->attributes['porter'] = '[' . $porter . ']';
    }

    public function formatSchedule($data)
    {
        $json = (array)$data;
        $r = '[';

        foreach ($json as $value) {
            $predetermined_value = array(
                'inlineCheckbox1' => '0',
                'inlineCheckbox2' => '0',
                'inlineCheckbox3' => '0',
                'inlineCheckbox4' => '0',
                'inlineCheckbox5' => '0',
                'inlineCheckbox6' => '0',
                'inlineCheckbox7' => '0',
                'timei1ser' => '0',
                'timef1ser' => '0',
            );
            $r = $r . '{"status":"active"';
            $value = (array)$value;
            foreach ($predetermined_value as $key => $i) {
                if (array_key_exists($key, $value)) {
                    $predetermined_value[$key] = $value[$key];
                }
            }
            foreach ($predetermined_value as $key => $item) {
                $r = $r . ',"' . $key . '":"' . $item . '"';
            }
            $r = $r . '},';
        }
        $r = substr($r, 0, -1);
        $this->attributes['schedule'] = $r . ']';
    }

    public function formatAddress($data)
    {
        $address = $data;

        $this->attributes['address'] = json_encode($address);
    }


    public function createAdminAssociated($data)
    {
        $admin = $data;
        foreach ($admin as $adm) {
            if ($adm['id'] == 'New') {
                $a = new Company();
                $a->fill($adm);
                $a->type = 'administrator';
                $a->save();
                $this->attributes['administration_id'] = $a->id;
            } else {
                $this->attributes['administration_id'] = $adm['id'];
            }
        }
    }

    public function createCommissionAssociated($data)
    {
        $comisions = $data;
        $jsonc = json_encode($comisions);
        if (!is_null($comisions)) {
            foreach ($comisions as $comision) {
                $c = new CommissionIntegrant();
                $c->fill($comision);
                $c->building_id = $this->attributes['id'];
                $c->setBuildingInfo($jsonc);
                $c->save();
            }
        }
    }

    public function createServicesAssociated($data)
    {
        $services = $data;
        if (!is_null($services)) {
            foreach ($services as $ser) {
                if ($ser['name'] == 'Nuevo') {
                    $s = new Service();
                    $ser2 = $ser;
                    unset($ser2['other']);
                    unset($ser2['name']);
                    $s->fill($ser2);
                    $s->building_id = $this->attributes['id'];
                    if ($ser['service'] == 'Otro') {
                        if (!is_null($ser['other'])) {
                            $s->service = $ser['other'];
                        }
                    }
                    $s->save();

                    $rel = new BuildingService();
                    $rel->building_id = $this->attributes['id'];
                    $rel->service_id = $s->id;
                    $rel->save();
                } else {
                    $rel = new BuildingService();
                    $rel->building_id = $this->attributes['id'];
                    $srv_id = Service::where('service', 'LIKE', $ser['service'])->where('provider', 'LIKE', $ser['name'])->pluck('id');
                    $rel->service_id = $srv_id[0];
                    $rel->save();
                }
            }
        }
    }

    public function getAddressTowers()
    {

        $tower = Tower::where('building_id', $this->id)->get();
        $address = [];
        foreach ($tower as $t) {
            $aux = explode('-', $t->tower_address);
            $format_address = $t->tower_denomination . ' - ' . $aux[0];
            $address[] = $format_address;
        }
        return $address;
    }

    public function getAddressBetweenStreetsTowers()
    {
        $tower = Tower::where('building_id', $this->id)->get();
        $address = [];
        foreach ($tower as $t) {
            $aux = explode('-', $t->tower_address);
            $format_address = $aux;
            $address[] = $format_address;
        }
        return $address;
    }

    public function hasTower()
    {
        return $this->towers()->count() > 0;
    }

    public function verificateContactsMultiple($all = true, $needsVerification = false, $perPage = 20, $page = 1)
    {
        $query = $this->contacts()
            ->with(['contact' => function ($q2) {
                $q2->withOut('mainBuilding');
            }])
            ->with('flat')
            ->with('casesAnnouncement')
            ->whereHas('contact', function ($query) use ($needsVerification) {
                $query->where('needs_verification', $needsVerification ? 1 : null);
            });

        if (!$all) {
            $query->where('contact_type', '=', 'Autorizado')
                ->whereNotNull('start_date')
                ->whereNotNull('end_date');
        }

        $contacts = $query->get();

        $filteredContacts = $contacts->filter(function (BuildingContact $user) {
            return $this->isUserAvailable($user->schedule, $user->start_date, $user->end_date);
        });


        $sortedContacts = Building::sortedContacts($filteredContacts);

        $total = $sortedContacts->count();

        $paginatedContacts = $sortedContacts->slice(($page - 1) * $perPage, $perPage);

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedContacts->values(),
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }


    public function autorizedUserList($operator)
    {
        return $this->hasManyThrough(
            Contact::class,
            BuildingContact::class,
            'building_id',
            'id',
            'id',
            'contact_id'
        )
            ->where('needs_verification', null)
            ->where('contact_type', $operator, 'Autorizado');
    }


    public function isUserAvailable($schedule, $startDate, $endDate)
    {

        if ($this->isEmptySchedule($schedule)
            && $this->isEmptyStartDate($startDate)
            && $this->isEmptyEndDate($endDate)
        ) {
            return true;
        }


        $currentDay = now()->dayOfWeek;

        $scheduleArray = json_decode($schedule, true);

        if ($scheduleArray && is_array($scheduleArray)) {
            foreach ($scheduleArray as $scheduleItem) {
                if (count($scheduleItem) > 0) {
                    if (self::isTimeWithinSchedule($scheduleItem, $currentDay)) {
                        return true;
                    }
                }
            }
        }

        if (self::isCurrentDateWithinRange($startDate, $endDate)) {
            if ($scheduleArray && is_array($scheduleArray) && count($scheduleArray) > 0) {
                return false;
            }
            return true;
        }


        return false;
    }


    protected static function isCurrentDateWithinRange($startDate, $endDate)
    {
        if (self::isEmptyStartDate($startDate) || self::isEmptyEndDate($endDate)) return true;
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        return now()->between($startDate->subMinutes(20), $endDate->addMinutes(20));
    }

    protected
    static function isEmptyEndDate($endDate)
    {
        return empty($endDate);
    }

    protected
    static function isEmptyStartDate($startDate)
    {
        return empty($startDate);
    }

    public
    static function isEmptySchedule($schedule)
    {
        return empty($schedule) || empty(json_decode($schedule, true));
    }

    protected static function isTimeWithinSchedule($scheduleItem, $currentDay)
    {
        $currentDayKey = self::getDayOfWeekKey($currentDay);

        return self::isCurrentDayTimeWithinSchedule($scheduleItem, $currentDayKey);
    }


    protected static function isCurrentDayTimeWithinSchedule($scheduleItem, $dayKey)
    {
        if ($scheduleItem[$dayKey] == 0) {
            return false;
        }

        $startTime = Carbon::parse($scheduleItem['timei1ser'] ?? today()->setTime(0, 0))->subMinutes(20);
        $endTime = Carbon::parse($scheduleItem['timef1ser'] ?? today()->setTime(23, 59))->addMinutes(20);

        if (now() > $endTime) return false;
        if (now() < $startTime) return false;
        return true;
    }

    protected
    static function parseTime($timeString)
    {
        if (!$timeString)
            return null;
        $time = Carbon::createFromFormat('H:i', $timeString);
        return $time;
    }

    protected
    static function getDayOfWeekKey($currentDay)
    {
        return 'inlineCheckbox' . ($currentDay == 0 ? 7 : $currentDay);
    }


    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public
    function contacts()
    {
        return $this->hasMany(BuildingContact::class, 'building_id', 'id');
    }

    public
    function contactsSecondary()
    {
        return $this->belongsToMany(Contact::class, 'building_contact', 'building_id', 'contact_id');
    }

    public
    function residents()
    {
        return $this->hasMany(Contact::class, 'building_id', 'id')->whereIn('contact_type', User::$authorizedLoginTypes);
    }

    public
    function verificateContacts()
    {
        return $this->hasManyThrough(Contact::class, BuildingContact::class, 'building_id', 'id', 'id', 'contact_id');
    }

    public
    function principalContacts()
    {
        return $this->hasMany(Contact::class, 'building_id', 'id')->where('contact_type', 'Contacto Principal');
    }

    public
    function nonPrincipalContacts()
    {
        return $this->hasMany(Contact::class, 'building_id', 'id')->where('contact_type', '!=', 'Contacto Principal');
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'building_service', 'building_id', 'service_id');
    }

    public function cleaningServices()
    {
        return $this->belongsToMany(Service::class, 'building_cleaning_service', 'building_id', 'cleaning_service_id');
    }

    public function physicalResponse()
    {
        return $this->belongsToMany(Service::class, 'building_physical_response_service', 'building_id', 'physical_response_id');
    }


    public function servicesByType($type)
    {
        return $this->belongsToMany(Service::class, 'building_service', 'building_id', 'service_id')->where('service', $type);
    }

    public
    function servicesNotByTypes($types)
    {
        return $this->belongsToMany(Service::class, 'building_service', 'building_id', 'service_id')->whereNotIn('service', explode(',', $types));
    }

    public
    function activeHours()
    {
        return $this->hasMany(BuildingActiveHours::class, 'building_id', 'id');
    }

    public
    function buildingServices()
    {
        return $this->hasMany(BuildingService::class, 'building_id', 'id');
    }


    public
    function commissions()
    {
        return $this->belongsToMany(Contact::class, 'commission_integrants', 'building_id', 'contact_id');
    }

    public
    function referents()
    {
        return $this->hasManyThrough(
            Contact::class,
            BuildingContact::class,
            'building_id',
            'id',
            'id',
            'contact_id'
        )->where('referrer', true);
    }

    public
    function temporaryContacts()
    {
        return $this->hasMany(TemporaryContact::class, 'building_id', 'id')
            ->where('end_date', '>=', Carbon::now()->setTimezone('America/Montevideo')->toDateTimeString())->where('start_date', '<=', Carbon::now()->setTimezone('America/Montevideo')->toDateTimeString());
    }

    public
    function parsedName()
    {
        return str_replace('EDIFICIO ', '', $this->name);
    }

    public
    function operator()
    {
        return $this->belongsTo(Operator::class);
    }

    public
    function comercial()
    {
        return $this->belongsTo(Comercial::class, 'operator_id');
    }

    public
    function administration()
    {
        return $this->belongsTo(Administration::class);
    }


    public
    function towers()
    {
        return $this->hasMany(Tower::class)->orderBy('tower_denomination', 'asc');
    }

    public function flats()
    {
        return $this->hasMany(Flat::class)->orderBy('number', 'ASC');
    }

    public function cases()
    {
        return $this->hasMany(Caseq::class);
    }


    public function casesAnnouncement()
    {
        return $this->hasMany(Caseq::class, 'building_id', 'id')
            ->whereNotIn('state', config('constants.cases_close_states'))
            ->where('last_category_id', config('constants.category_announcement'))
            ->where('tracing', 1);
    }

    public function casesOpenPackages()
    {

        return $this->hasMany(Caseq::class)
            ->where('state', '=', 'pendiente')
            ->where('building_id', '=', $this->id)
            ->whereIn('last_category_id', config('constants.package_cases'));
    }

// Casos de tipo seguimientos
    public function casesTracings()
    {
        return $this->cases()->where('tracing', 1)->where('state', '!=', 'finalizado');
    }

    public function lastOpenCasesTracings()
    {
        return $this->cases()->where('tracing', 1)
            ->where('state', '!=', 'CERRADO')
            ->where('state', '!=', 'finalizado');
    }


    public function intercoms($userTower = null)
    {
        $relation = $this->hasMany(BuildingIntercom::class, 'building_id', 'building_number')->orderBy('door_name');

        if (!$this->hasTower()) {
            return $relation->with('intercomModel');
        }

        if ($userTower !== null) {
            return $relation->where(function ($query) use ($userTower) {
                $query->with('intercomModel')
                    ->where('tower_prefix', $userTower)
                    ->orWhereNull('tower_prefix');
            });
        }

        return $relation;
    }


    public function filteredIntercoms($userTower = null)
    {
        return $this->hasManyThrough(
            BuildingIntercom::class,
            Building::class,
            'id', // Foreign key on building table...
            'building_id', // Foreign key on intercoms table...
            'building_id', // Local key on users table...
            'id' // Local key on building table...
        );

    }

    public
    function accessCodes(): HasMany
    {
        return $this->hasMany(AccessCode::class);
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */
    public
    function scopeGetFlats($id)
    {
        return Flat::where('building_id', $id)->orderBy('number', 'ASC');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public
    function getAsteriskExtensionNumberAttribute()
    {
        return $this->attributes['asterisk_extension_number'] ?? config('app.asterisk_default_extension_number');
    }

    public
    function getAsteriskExtensionPasswordAttribute()
    {
        return $this->attributes['asterisk_extension_password'] ?? config('app.asterisk_default_extension_password');
    }

    public
    function getSearchDataAttribute()
    {
        return $this->building_number . ' - ' . $this->name;
    }

    public function getRulesCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['rules_comments'])) {
            $comments = array_filter(json_decode($this->attributes['rules_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getMalfunctionsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['malfunctions_comments'])) {
            $comments = array_filter(json_decode($this->attributes['malfunctions_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getArrangementsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['arrangements_comments'])) {
            $comments = array_filter(json_decode($this->attributes['arrangements_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getInstructionsExplanationsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['instructions_explanations_comments'])) {
            $comments = array_filter(json_decode($this->attributes['instructions_explanations_comments'], true), function ($comment) {
                return $comment['instructions_explanations_comments'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getInstructionsExplanationsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['instructions_explanations'])) {
            $comments = array_filter(json_decode($this->attributes['instructions_explanations'], true), function ($comment) {
                return $comment['instructions_explanations'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getPorterAttribute()
    {
        $porters = [];
        if (isset($this->attributes['porter'])) {
            array_filter(json_decode($this->attributes['porter'], true), function ($porter) use (&$porters) {
                if ($porter['name'] != "" && !is_null($porter['name']))
                    $porters[] = $porter;
            });
        }
        return json_encode($porters);
    }

    public function getCleaningTimeAttribute()
    {
        $cleaners = [];
        if (isset($this->attributes['cleaning_time'])) {
            array_filter(json_decode($this->attributes['cleaning_time'], true), function ($cleaner) use (&$cleaners) {
                if (!is_null($cleaner['name'])) {
                    $cleaners[] = $cleaner;
                }
            });
        }
        return json_encode($cleaners);
    }

    public function getPhonesCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['phones_comments'])) {
            $comments = array_filter(json_decode($this->attributes['phones_comments'], true), function ($comment) {
                return $comment['phone'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getSpeakersCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['speakers_comments'])) {
            $comments = array_filter(json_decode($this->attributes['speakers_comments'], true), function ($comment) {
                return $comment['speaker'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getSirensCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['sirens_comments'])) {
            $comments = array_filter(json_decode($this->attributes['sirens_comments'], true), function ($comment) {
                return $comment['siren'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getIntegratedAlarmsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['integrated_alarms_comments'])) {
            $comments = array_filter(json_decode($this->attributes['integrated_alarms_comments'], true), function ($comment) {
                return $comment['integrated_alarm'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getCommercialLocalsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['commercial_locals_comments'])) {
            $comments = array_filter(json_decode($this->attributes['commercial_locals_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getCadeteHallCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['cadete_hall_comments'])) {
            $comments = array_filter(json_decode($this->attributes['cadete_hall_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getCadeteUpCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['cadete_up_comments'])) {
            $comments = array_filter(json_decode($this->attributes['cadete_up_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getInstructionsExplanationAttribute()
    {
        $comments = [];
        if (isset($this->attributes['instructions_explanations'])) {
            $comments = array_filter(json_decode($this->attributes['instructions_explanations'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getServicesCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['services_comments'])) {
            $comments = array_filter(json_decode($this->attributes['services_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getRecordsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['records_comments'])) {
            $comments = array_filter(json_decode($this->attributes['records_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getTagsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['tags_comments'])) {
            $comments = array_filter(json_decode($this->attributes['tags_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getInvestigationsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['investigations_comments'])) {
            $comments = array_filter(json_decode($this->attributes['investigations_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getOthersCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['others_comments'])) {
            $comments = array_filter(json_decode($this->attributes['others_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getMovingCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['moving_comments'])) {
            $comments = array_filter(json_decode($this->attributes['moving_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getBathroomsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['bathrooms_comments'])) {
            $comments = array_filter(json_decode($this->attributes['bathrooms_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getRooftopCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['rooftop_comments'])) {
            $comments = array_filter(json_decode($this->attributes['rooftop_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getFireAlarmCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['fire_alarm_comments'])) {
            $comments = array_filter(json_decode($this->attributes['fire_alarm_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getOthersAlarmsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['others_alarms_comments'])) {
            $comments = array_filter(json_decode($this->attributes['others_alarms_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getAditionalInstructionsCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['aditional_instructions_comments'])) {
            $comments = array_filter(json_decode($this->attributes['aditional_instructions_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    public function getMedidoresCommentsAttribute()
    {
        $comments = [];
        if (isset($this->attributes['medidores_comments'])) {
            $comments = array_filter(json_decode($this->attributes['medidores_comments'], true), function ($comment) {
                return $comment['comment'] != "";
            });
        }

        return json_encode($comments);
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    public
    function setImageAttribute($value)
    {
        $attribute_name = "image";
        $disk = config('backpack.base.root_disk_name'); // or use your own disk, defined in config/filesystems.php
        $destination_path = "public/uploads/buildings"; // path relative to the disk above

        // if the image was erased
        if ($value == null) {

            // delete the image from disk
                $this->{$attribute_name} != null ?? \Storage::disk($disk)->delete($this->{$attribute_name});

            // set null in the database column
            $this->attributes[$attribute_name] = null;
        }

        // if a base64 was sent, store it in the db
        if (starts_with($value, 'data:image')) {
            // 0. Make the image
            $image = \Image::make($value)->encode('jpg', 90);

            // 1. Generate a filename.
            $filename = md5($value . time()) . '.jpg';
            // 2. Store the image on disk.
            \Storage::disk($disk)->put($destination_path . '/' . $filename, $image->stream());
            // 3. Save the public path to the database
            // but first, remove "public/" from the path, since we're pointing to it from the root folder
            // that way, what gets saved in the database is the user-accesible URL
            $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
            $this->attributes[$attribute_name] = $public_destination_path . '/' . $filename;
        }
    }

    public
    function setGatesRampsFileAttribute($value)
    {
        $attribute_name = "gates_ramps_file";
        $disk = "public";
        $destination_path = "uploads/gates_ramps_file";


        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);

        //return $this->attributes[$attribute_name]; // uncomment if this is a translatable field
    }

    public
    function setOrderJobAttribute($value)
    {
        $attribute_name = "order_job";
        $disk = "public";
        $destination_path = "uploads/order_job";


        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);

        //return $this->attributes[$attribute_name]; // uncomment if this is a translatable field
    }

    public
    function setOrderJobCheckedAttribute($value)
    {
        $attribute_name = "order_job_checked";
        $disk = "public";
        $destination_path = "uploads/order_job/checked";

        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);

        //return $this->attributes[$attribute_name]; // uncomment if this is a translatable field
    }

    public
    function setCleaningTimeAttribute($value)
    {
        $this->attributes['cleaning_time'] = json_encode($value);
    }

    public
    function setContractAttribute($value)
    {
        $attribute_name = "contract";
        $disk = "public";
        $destination_path = "uploads/contract";

        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);

        //return $this->attributes[$attribute_name]; // uncomment if this is a translatable field
    }

    public
    function setRevSecurityAttribute($value)
    {
        $attribute_name = "rev_security";
        $disk = "public";
        $destination_path = "uploads/contract/security";

        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);

        //return $this->attributes[$attribute_name]; // uncomment if this is a translatable field
    }


    public
    function getOperatorName()
    {
        if (is_null($this->operator_id)) {
            return "";
        }
        $user = User::find($this->operator_id);
        if ($user) {
            return User::find($this->operator_id)->complete_name;
        }
        return 'Operador eliminado';
    }

    public
    function getNumberCasesAttribute()
    {
        return $this->cases()->where('state', '!=', 'finalizado')->count();
    }

    public
    function changeDaysServices($day, $hour)
    {
        if (is_array($hour)) {
            $count = 0;
            $horario = '';
            foreach ($hour as $h) {
                $horario = $horario . $h[$day];
                if ($count == 0) {
                    $horario = $horario . '-';
                    $count = 1;
                } else {
                    $horario = $horario . ' / ';
                    $count = 0;
                }
            }
            $this->attributes[$day] = $horario;
        }
    }

    public
    function setMonServiceAttribute($hour)
    {
        $this->changeDaysServices('mon_service', $hour);
    }

    public
    function setThuServiceAttribute($hour)
    {
        $this->changeDaysServices('thu_service', $hour);
    }

    public
    function setWenServiceAttribute($hour)
    {
        $this->changeDaysServices('wen_service', $hour);
    }

    public
    function setThaServiceAttribute($hour)
    {
        $this->changeDaysServices('tha_service', $hour);
    }

    public
    function setFreServiceAttribute($hour)
    {
        $this->changeDaysServices('fre_service', $hour);
    }

    public
    function setSatServiceAttribute($hour)
    {
        $this->changeDaysServices('sat_service', $hour);
    }

    public
    function setSunServiceAttribute($hour)
    {
        $this->changeDaysServices('sun_service', $hour);
    }

    public
    function getServicesModal($id)
    {
        $service = [];
        $provider = [];
        $r = '';
        $services_id = BuildingService::where('building_id', $id)->pluck('service_id');
        foreach ($services_id as $s) {
            array_push($service, Service::where('id', $s)->pluck('service'));
            array_push($provider, Service::where('id', $s)->pluck('provider'));
        }
        for ($i = 0; $i < sizeof($service); $i++) {
            $r = $r . $service[$i] . ' - ' . $provider[$i] . '<br>';
        }
        return $r;
    }

    public
    function getComissionModal($id)
    {
        $c = '';
        $comissions = CommissionIntegrant::where('building_id', $id)->pluck('name');
        foreach ($comissions as $s) {
            $c = $c . $s . '<br>';
        }
        return $c;
    }

    public
    function getFakeSchedule()
    {
        $array = gettype($this->schedule) === 'array' ? $this->schedule : json_decode($this->schedule);

        $day = ['inlineCheckbox1' => 'Lunes', 'inlineCheckbox2' => 'Martes', 'inlineCheckbox3' => 'Miercoles', 'inlineCheckbox4' => 'Jueves', 'inlineCheckbox5' => 'Viernes', 'inlineCheckbox6' => 'Sabado', 'inlineCheckbox7' => 'Domingo'];
        $hour = '';
        $days = '*';
        if ($array != null && $array != '') {
            foreach ($array as $item) {
                $especifico = $item;
                foreach ($especifico as $key => $valor) {
                    if ($valor === '1') {
                        $days = $days . $day[$key] . ', ';
                    }
                }
                if (gettype($especifico) === 'object') {
                    $especifico = (array)$especifico;
                }
                $days = nl2br($days . ' de ' . $especifico['timei1ser'] . ' a ' . $especifico['timef1ser'] . '\n' . '*');
            }
            $days = substr($days, 0, -1);

            return nl2br($days);
        } else {
            return $this->doorman_service_hours;
        }
    }

    public
    function getFakeScheduleCleaningTime()
    {
        $array = json_decode($this->cleaning_time);
        $day = ['inlineCheckbox1' => 'Lunes', 'inlineCheckbox2' => 'Martes', 'inlineCheckbox3' => 'Miercoles', 'inlineCheckbox4' => 'Jueves', 'inlineCheckbox5' => 'Viernes', 'inlineCheckbox6' => 'Sabado', 'inlineCheckbox7' => 'Domingo'];
        $hour = '';
        $days = '*';
        if ($array != null && $array != '') {
            foreach ($array as $item) {
                $especifico = $item;
                foreach ($especifico as $key => $valor) {
                    if ($valor === '1') {
                        $days = $days . $day[$key] . ', ';
                    }
                }
                $days = nl2br($days . ' de ' . $especifico->timei1ser . ' a ' . $especifico->timef1ser . '\n' . '*');
            }
            $days = substr($days, 0, -1);

            return nl2br($days);
        }
    }

    public
    function getFakeKeys()
    {
        $sentence = '';
        if ($this->extra_keys_porter && $this->extra_keys_porter > 0) {
            $sentence = 'P: ' . $this->extra_keys_porter . ', ';
        }
        if ($this->extra_keys_admin && $this->extra_keys_admin > 0) {
            $sentence = $sentence . 'A: ' . $this->extra_keys_admin . ', ';
        }
        if ($this->extra_keys_clear && $this->extra_keys_clear > 0) {
            $sentence = $sentence . 'L: ' . $this->extra_keys_clear . ', ';
        }
        if ($this->extra_keys_other && $this->extra_keys_other > 0) {
            $sentence = $sentence . 'O: ' . $this->extra_keys_other . ', ';
        }

        $sentence = substr($sentence, 0, -2);


        if ($sentence == '') {
            return 'No hay llaves extras';
        }

        return $sentence;
    }


    public
    function getFakeDaySchedule($day)
    {
        $array = gettype($this->schedule) === 'array' ? $this->schedule : json_decode($this->schedule);

        $name_day = ['inlineCheckbox1' => 'Lunes', 'inlineCheckbox2' => 'Martes', 'inlineCheckbox3' => 'Miercoles', 'inlineCheckbox4' => 'Jueves', 'inlineCheckbox5' => 'Viernes', 'inlineCheckbox6' => 'Sabado', 'inlineCheckbox7' => 'Domingo'];
        $horario = [];
        foreach ($array as $hour) {
            $hour = json_encode($hour);
            $hour = json_decode($hour, true);
            if ($hour['inlineCheckbox' . $day] == 1) {
                array_push($horario, $hour['timei1ser']);
                array_push($horario, $hour['timef1ser']);
            }
        }
        $result = $name_day['inlineCheckbox' . $day];
        foreach ($horario as $item) {
            $result = nl2br($result . ' ' . array_shift($horario) . '-' . array_shift($horario) . '\n' . '*');
            if (sizeof($horario) == 0) {
                $result = substr($result, 0, -1);
                break;
            }
        }
        if (sizeof(explode(" ", $result)) > 1) {
            if (explode(" ", $result)[1] == '00:00-23:59\n') {
                $divi = explode(" ", $result);
                $divi[1] = '24hs';
                $result = $divi[0] . ' ' . $divi[1];
            }
        }

        if ($result == $name_day['inlineCheckbox' . $day]) {
            $result = $result . ' - Sin servicio';
        }

        return $result;
    }

    public
    function getComisionAttribute()
    {
        $comisions = CommissionIntegrant::where('building_id', $this->id)->get();
        $result = [];
        $text = '[';
        foreach ($comisions as $comision) {
            array_push($result, \GuzzleHttp\json_encode($comision->getBuildingInfo()));
        }
        foreach ($result as $r) {
            $text = $text . $r . ',';
        }
        $text = rtrim($text, ',');
        $text = $text . ']';
        return $text;
    }

    public
    function finalAddress($principales, $esquinas)
    {
        $principales = explode('/', $principales);
        $esquinas = explode('/', $esquinas);
        $final = '';
        $index = 0;
        foreach ($principales as $p) {
            if (array_key_exists($index, $esquinas)) {
                $final = $final . $p . ' ' . $esquinas[$index] . ' / ';
                $index = $index + 1;
            } else {
                $final = $final . $p . ' / ';
                $index = $index + 1;
            }
        }
        return $final;
    }

    public
    function firstAddress($principales, $esquinas)
    {
        $principales = explode('/', $principales);
        $esquinas = explode('/', $esquinas);
        $final = '';
        if (array_key_exists(0, $esquinas)) {
            $final = $principales[0] . ' ' . $esquinas[0];
        } else {
            $final = $principales[0];
        }
        return $final;
    }

    public
    function getAddressFakeAttribute()
    {
        if (gettype($this->address) == 'array') {
            $this->attributes['address'] = json_encode($this->attributes['address']);
        }
        if (strpos($this->attributes['address'], '{')) {
            $json = json_decode($this->attributes['address']);
            $r = '';
            $json = (array)$json;
            foreach ($json as $item) {
                $item = (array)$item;
                if ($r == null) {
                    $r = $r . (ucwords(mb_strtolower($item['street'] ?? ''))) . ' ' . (ucwords(mb_strtolower($item['number'] ?? ''))) . ' ';
                } else {
                    $r = $r . ' / ' . (ucwords(mb_strtolower($item['street'] ?? ''))) . ' ' . (ucwords(mb_strtolower($item['number'] ?? '')));
                }
            }
            return $r;
        } else {
            return $this->attributes['address'];
        }
    }

    public
    function getBetweenStreetsFakeAttribute()
    {
        if (strpos($this->attributes['address'], '{')) {
            $json = gettype($this->attributes['address']) === 'array' ? $this->attributes['address'] : json_decode($this->attributes['address']);
            $r = '';
            $json = (array)$json;
            foreach ($json as $item) {
                $item = (array)$item;
                if ($r == null) {
                    if (!array_key_exists('between_streets', $item)) {
                        $r = $r . ($item['corner'] ?? '');
                    } else {
                        $r = $r . ($item['between_streets'] ?? '');
                    }
                } else {
                    if (!array_key_exists('between_streets', $item)) {
                        $r = $r . ' / ' . ($item['corner'] ?? '');
                    } else {
                        $r = $r . ' / ' . ($item['between_streets'] ?? '');
                    }
                }
            }
            return $r;
        } else {
            return $this->attributes['between_streets'] ?? '';
        }
    }

    public
    function setNameAttribute($name)
    {
        $this->attributes['name'] = ucwords(mb_strtolower($name));
    }

    public
    function getFlats()
    {
        $flats = Flat::where('building_id', $this->attributes['id'])->pluck('number');
        $r = '';
        foreach ($flats as $f) {
            $r = $r . $f . ',';
        }
        if ($r == '') {
            return $r;
        }
        return substr($r, 0, -1);
    }


    public function getDoorsQuantityAttribute()
    {

        if (array_key_exists('doors_quantity', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $puertas = 0;
                foreach ($towers as $t) {
                    $puertas = $puertas + is_array($t) ? $t['doors_quantity'] : $t->doors_quantity;
                }
                return $puertas;
            }
            return $this->attributes['doors_quantity'];
        }
    }

    public
    function getTowerAccessAttribute()
    {
        if (array_key_exists('tower_access', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['tower_access'] : $t->tower_denomination . ' - ' . $t->tower_access;
                }
                return $comentary;
            }
            return $this->attributes['tower_access'];
        }
    }

    public
    function getMeasuresLocationsAttribute()
    {
        if (array_key_exists('measures_locations', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['measures_locations'] . ' / ' : $t->tower_denomination . ' - ' . $t->measures_locations . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['measures_locations'];
        }
    }

    public
    function getLockAttribute()
    {
        if (array_key_exists('lock', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['lock'] ?? 'No' . ' / ' : $t->tower_denomination . ' - ' . $t->lock ?? 'No' . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['lock'];
        }
    }

    public
    function getTowerInfoAttribute()
    {
        $towers = $this->towers;
        if (sizeof($towers) > 0) {
            return json_encode($towers);
        } else {
            return '[' . json_encode([
                    'flats' => $this->getFlats(),
                    'doors_quantity' => $this->doors_quantity,
                    'tower_access' => $this->tower_access,
                    'tower_denomination' => $this->tower_denomination,
                    'tower_comment' => $this->tower_comment,
                    'opens_from_flat' => $this->opens_from_flat,
                    'lock' => $this->lock,
                    'measures_locations' => $this->measures_locations,
                    'allows_elevator_comunication' => $this->allows_elevator_comunication,
                    'doorman_format' => $this->doorman_format,
                    'gates_ramps_controllers' => $this->gates_ramps_controllers,
                    'gates_ramps_denomination' => $this->gates_ramps_denomination,
                    'gates_ramps_file' => $this->gates_ramps_file,
                    'tower_corner' => $this->tower_corner,
                    'tower_address' => $this->tower_address,
                ]) . ']';
        }
    }

    public
    function getOpensFromFlatAttribute()
    {
        if (array_key_exists('opens_from_flat', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['opens_from_flat'] . ' / ' : $t->tower_denomination . ' - ' . $t->opens_from_flat . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['opens_from_flat'];
        }
    }

    public
    function getDoormanFormatAttribute()
    {
        if (array_key_exists('doorman_format', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['doorman_format'] . ' / ' : $t->tower_denomination . ' - ' . $t->doorman_format . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['doorman_format'];
        }
    }

    public
    function getAllowsElevatorComunicationAttribute()
    {
        if (array_key_exists('allows_elevator_comunication', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['allows_elevator_comunication'] . '/ ' : $t->tower_denomination . ' - ' . $t->allows_elevator_comunication . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['allows_elevator_comunication'];
        }
    }

    public
    function getGatesRampsControllersAttribute()
    {
        if (array_key_exists('gates_ramps_controllers', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['gates_ramps_controllers'] . ' / ' : $t->tower_denomination . ' - ' . $t->gates_ramps_controllers . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['gates_ramps_controllers'];
        }
    }

    public
    function getGatesRampsDenominationAttribute()
    {
        if (array_key_exists('gates_ramps_denomination', $this->attributes)) {
            $towers = $this->towers;
            if (sizeof($towers) != 0) {
                $comentary = '';
                foreach ($towers as $t) {
                    $comentary = $comentary . ' ' . is_array($t) ? $t['tower_denomination'] . ' - ' . $t['gates_ramps_denomination'] . ' / ' : $t->tower_denomination . ' - ' . $t->gates_ramps_denomination . ' / ';
                }
                return $comentary;
            }
            return $this->attributes['gates_ramps_denomination'];
        }
    }

    public
    function getCommentsAttribute()
    {
        if (substr($this->attributes['comments'] ?? 'NaN', 0, 2) == '[{') {
            return str_replace(',', ', ', $this->attributes['comments']);
        } elseif (substr($this->attributes['comments'] ?? '[]', 0, 2) == '[]') {
            return '';
        } else {
            return str_replace(',', ', ', '[{"com_com":"' . $this->attributes['comments'] . '"}]');
        }
    }

    public
    function setCommentsAttribute($value)
    {
        $value = json_decode($value);

        foreach ($value ?? [] as $comment) {
            $comment->com_com = str_replace("\"", "'", $comment->com_com);
        }


        $this->attributes['comments'] = json_encode($value);
    }


    public
    function getCommentsForIndex($index)
    {
        if (array_key_exists($index, json_decode($this->getCommentsAttribute()) ?? [])) {
            return json_decode($this->getCommentsAttribute())[$index]->com_com;
        }
    }

    public function getTowersQuantityAttribute()
    {
        $towers = $this->towers;
        return $towers->count();
    }


    public function getWarrantyDateAttribute()
    {
        if (!isset($this->attributes['warranty_date'])) {
            return (object)['other' => null, 'equipament' => null];
        }

        $warrantyDate = $this->attributes['warranty_date'];

        if (is_array($warrantyDate)) {
            return (object)$warrantyDate;
        }

        return json_decode($warrantyDate) ?? (object)['other' => null, 'equipament' => null];
    }

    public
    function hasAccessByPin()
    {
        return $this->access_by_pin;
    }

    public
    function hasIntercom($intercom_type)
    {
        return BuildingIntercom::query()
                ->where('building_id', $this->building_number)
                ->whereRaw('lower(intercom_type) = ?', [strtolower($intercom_type)])
                ->get()
                ->count() > 0;
    }

}
