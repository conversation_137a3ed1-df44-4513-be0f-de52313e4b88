<?php

namespace App\Models;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Model;

class Log extends Model
{
    protected $fillable = ['user_id', 'user_name', 'user_building', 'user_flat', 'text'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getBuildingAttribute()
    {
        return $this->user()->get()->buildingName();
    }

    public function getFlatAttribute()
    {
        return $this->user()->get()->flatName();
    }

}
