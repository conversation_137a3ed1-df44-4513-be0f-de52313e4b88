<?php

namespace App\Models\User;

use App\Events\ContactUpdatedEvent;
use App\Helpers\Helper;
use App\Http\Helpers\ContactScope;
use App\Imports\ContactsImport;
use App\Models\AccessCode;
use App\Models\Building;
use App\Models\BuildingIntercom;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\KazooFlatsConsecutive;
use App\Models\Relations\CarContact;
use App\Models\Relations\CarFlat;
use App\Models\BuildingsRelationships\CommissionIntegrant;
use App\Models\Car;
use App\Models\Flat;
use App\Models\TemporaryContact;
use App\Models\Tower;
use App\Notifications\RegisterUser;
use App\Services\KazooService;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Carbon\Carbon;
use Hash;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use PhpParser\Node\NullableType;
use PHPUnit\Exception;
use Spatie\Permission\Traits\HasRoles;
use App\Integrations\Wittybots\ApiWittybots;

class Contact extends User
{
    use CrudTrait, SoftDeletes, HasRoles;

    public static $allContactTypes = [];

    public static $usersTypeCanHaveImage = [
        'Residente',
        'No Residente',
        'Contacto Principal',
        'Contacto Principal - Comisión',
        'Comisión',
        'Responsable',
        'Empleado',
        'Foxsys',
        'Autorizado'
    ];

    public static $usersCanChangeContactImage = [
        'Representante Atención al Cliente',
        'Jefe Atención al Cliente',
        'Team Leader',
        'Jefe Operaciones',
        'Admin'
    ];


    public static $officeContactTypes =
        [
            'Autorizado',
            'Empleado',
            'Oficina',
            'Prohibido Ingreso',
            'Proveedor',
            'Responsable',
            'Foxsys'
        ];
    public static $intercomName;
    public static $residentialContactTypes =
        [
            'Residente',
            'No Residente',
            'Autorizado',
            'Contacto Principal',
            'Contacto Principal - Comisión',
            'Comisión',
            'Responsable',
            'Prohibido Ingreso',
        ];

    public static $casesResidentialContactTypes =
        [
            'Residente',
            'No Residente',
            'Contacto Principal',
            'Contacto Principal - Comisión',
            'Comisión',
            'Responsable',
            'Foxsys'
        ];

    public static $sortedMergedContactTypes =
        [
            'Contacto Principal',
            'Contacto Principal - Comisión',
            'Comisión',
            'Oficina',
            'Residente',
            'Responsable',
            'Empleado',
            'Autorizado',
            'Proveedor',
            'No Residente',
            'Prohibido Ingreso',
            'Foxsys',
            'S/D'
        ];


    public static $sortedToTableTypesOnResident =
        [
            'Comisión',
            'Contacto Principal - Comisión',
            'Contacto Principal',
            'Residente',
            'Prohibido Ingreso',
            'Autorizado',
            'No Residente',
            'Oficina',
            'Responsable',
            'Empleado',
            'Proveedor',
            'Foxsys',
            'S/D'
        ];

    public static $sortedToTableTypesOnOffice =
        [
            'Oficina',
            'Responsable',
            'Prohibido Ingreso',
            'Empleado',
            'Autorizado',
            'Proveedor',
            'Foxsys',
            'S/D',
            'Comisión',
            'Contacto Principal - Comisión',
            'Contacto Principal',
            'Residente',
            'No Residente',
        ];

    public static $appFoxsysUsers = [
        'Residente',
        'Contacto Principal - Comisión',
        'Contacto Principal',
        'Comisión',
        'Responsable',
        'Foxsys',
    ];

    public static function boot()
    {
        parent::boot();
        static::addGlobalScope(new ContactScope());
    }


    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'users';
    protected $guard_name = 'web';
    // protected $primaryKey = 'id';
    // public $timestamps = false;
    // protected $fillable = [];
    // protected $hidden = [];
    // protected $dates = [];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */
    public static function checkInlineCheckboxesScheduleDays($array)
    {
        foreach ($array as $key => $value) {
            if (strpos($key, 'inlineCheckbox') === 0 && $value !== "1") {
                return false;
            }
        }
        return true;
    }

    public static function checkInlineCheckboxesScheduleTimes($array, $days)
    {
        if ($array->timei1ser && $array->timef1ser) {
            return nl2br($days . ' de ' . $array->timei1ser . ' a ' . $array->timef1ser . '<br>');
        }
        if ($array->timei1ser == null && $array->timef1ser == null) {
            return nl2br($days . '<br>');
        }

        if ($array->timei1ser != null && $array->timef1ser == null) {
            return nl2br($days . ' desde las ' . $array->timei1ser . '<br>');
        }

        if ($array->timei1ser == null && $array->timef1ser != null) {
            return nl2br($days . ' hasta las ' . $array->timef1ser . '<br>');
        }
    }


    public static function getFakeSchedule($user_id, $flat_id)
    {
        $contact = BuildingContact::where('contact_id', $user_id)
            ->where('flat_id', $flat_id)
            ->first();
        $array = json_decode($contact['schedule']);
        $day = ['inlineCheckbox1' => 'Lunes', 'inlineCheckbox2' => 'Martes', 'inlineCheckbox3' => 'Miércoles', 'inlineCheckbox4' => 'Jueves', 'inlineCheckbox5' => 'Viernes', 'inlineCheckbox6' => 'Sábado', 'inlineCheckbox7' => 'Domingo'];
        $hour = '';
        $allDays = 'Todos los días';
        $resultDays = '';
        if ($array != null && $array != '') {
            foreach ($array as $item) {
                $days = '';
                $especifico = $item;
                foreach ($especifico as $key => $valor) {
                    if ($valor === '1') {
                        $days = $days . $day[$key] . ', ';
                    }
                }
                $days = rtrim($days, ', ');

                if (self::checkInlineCheckboxesScheduleDays($especifico)) {
                    $days = self::checkInlineCheckboxesScheduleTimes($especifico, $allDays);
                } else {
                    $days = self::checkInlineCheckboxesScheduleTimes($especifico, $days);
                }
                $resultDays .= $days;
            }

            $days = substr($resultDays, 0, -1);

            return response()->json([
                'data' => (nl2br($days))
            ]);
        } else {
            return $contact->doorman_service_hours;
        }
    }


    public static function buildingHasKeyboardForWittyBots($phoneNumber)
    {
        $number = 0 . substr($phoneNumber, 3);
        $contact = BuildingContact::where('phone_mobile', $number)->first();
        $buildingHasKeyboard = 0;
        if ($contact) {
            $buildingHasKeyboard = $contact->building->access_by_pin;
        }
        return json_encode(['status' => '201', 'response' => ['has_keyboard' => $buildingHasKeyboard]]);
    }

    public static function buildingHasPin($userId)
    {
        $contact = Contact::find($userId);

        return $buildingHasPin = $contact->building()->get()->first()?->access_by_pin;
    }


    public function buildingHasAccessByPIN()
    {
        if ($this->building) {
            return $this->building()->where('building_id', $this->building_id)->first()?->access_by_pin;
        } else {
            return false;
        }
    }


    public function userBuildingHasAkuvoxIntercom()
    {
        foreach ($this->building->intercoms as $intercom) {
            if ($intercom->intercomModel->type === Config('constants.akuvox'))
                return true;

        }
        return false;
    }

    public function storeContact($request): User
    {
        try {
            $this->fill($request->all());
            $attributes = $request->all();
            $attributes['type'] = 'contact';
            $attributes['created_by'] = backpack_user()->id;
            $attributes['token'] = md5(now());
            $attributes['notified'] = true;
            $attributes['password'] = Hash::make(Str::random(8));
            $attributes['name'] = $this->name;
            $attributes['ci'] = $this->ci;

            if (backpack_user()->hasRole('Cadete')) {
                $attributes['needs_verification'] = 2;
            }

            $contact = self::create($attributes);

            $contact->formatUser();

            $vehicles = explode(',', $request->field_of_vehicles);
            if ($vehicles) {
                CarContact::createCarContact($vehicles, $contact->id);
            }

            $contact->addNewsCarsFields($request->all(), $contact);

            if ($request->temporary_contact) {
                TemporaryContact::removeTemporaryContact($request->temporary_contact_id);
            }

            $contact->createBuildingContactRelation($request->all(), $contact->id);

            return $contact;
        } catch (\Throwable $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function isPermanentCode($codeType, $code, $buildingId, $flatId)
    {
        $accessCode = new AccessCode();
        if ($codeType == 'code') {
            $oldCode = $accessCode->getPermanentAccessCodeByUserAndBuildingAndFlat($this->id, $buildingId, $flatId);
            if ($oldCode != $code) {
                $accessCode->deleteForAkuvoxOr2NAndDatabase($this->id, $oldCode, $buildingId);
                return $code;
            }
        }
        return null;
    }

    public function isTermporalCode($codeType, $code)
    {
        if ($codeType == 'code-temporal') {
            return $code;
        }
        return null;
    }

    public function generateAccessCodes($buildingId, $flatId, $code, $codeType)
    {
        $isAccessCodeCreated = false;
        $newCode = new AccessCode();
        $buildingPermanentCode = $this->isPermanentCode($codeType, $code, $buildingId, $flatId);
        $buildingTemporaryCode = $this->isTermporalCode($codeType, $code);


        if ($buildingPermanentCode) {
            $isAccessCodeCreated = $newCode->generateAccessCode($buildingPermanentCode, $this->id, $buildingId, $flatId, false);
        }
        if ($buildingTemporaryCode) {
            $isAccessCodeCreated = $newCode->generateAccessCode($buildingTemporaryCode, $this->id, $buildingId, $flatId, true);
        }
        return $isAccessCodeCreated;
    }


    public function storeInlineContact($request, $building_id)
    {
        $this->fill($request->all());
        $this->type = 'contact';
        $this->created_by = backpack_user()->id;
        $pass = Str::random(8);
        $this->token = md5(Carbon::now());
        $this->notified = true;
        $this->password = Hash::make($pass);
        $this->formatUser();
        $this->building_id = $building_id;
        $this->save();
    }

    /**
     * @throws \Exception
     */
    public function createBuildingContactRelation($request, $contactId)
    {
        if ($request) {
            if (array_key_exists('building_secret_form', $request)) {
                $all_building_data = json_decode($request['building_secret_form']);

                foreach ($all_building_data ?: [] as $key => $building_datum) {
                    $building_datum->main_building = $key == 0;
                    BuildingContact::createBuildingContact($building_datum, $contactId);
                }
            }
        }
    }

    /**
     * @throws \Exception
     */
    public function updateBuildingContactRelation($request)
    {
        try {
            if ($request && array_key_exists('building_secret_form', $request)) {
                $all_building_data = json_decode($request['building_secret_form']);
                Contact::$intercomName = !empty($all_building_data[0]->intercom_name) ? $all_building_data[0]->intercom_name : false;
                if (count($all_building_data ?? []) > 0) {
                    BuildingContact::query()->where('contact_id', $this->id)
                        ->get()
                        ->each(function (BuildingContact $old) use ($all_building_data) {
                            $kazooData = KazooFlatsConsecutive::query()
                                ->where('contact_id', $old->contact_id)
                                ->where('flat_id', $old->flat_id)
                                ->first();
                            if ($kazooData) {
                                KazooService::deleteCallFlow($old->building->account_kazoo_id, $kazooData->callflow_id);
                            }
                            $old->forceDelete();
                        });

                    self::createBuildingContactWithUpdateData($all_building_data, $this->id);

                }
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public static function createBuildingContactWithUpdateData($all_building_data,$contactId)
    {
        foreach ($all_building_data ?? [] as $key => $building_datum) {
            if ($building_datum) {
                $building_datum->main_building = $key == 0;
                BuildingContact::createBuildingContact($building_datum, $contactId);
            }
        }

        $flatsId = BuildingContact::where('contact_id', $contactId)->pluck('flat_id');
        KazooFlatsConsecutive::where('contact_id',$contactId)->whereNotIn('flat_id',$flatsId)->delete();


    }
    public function initializeStartDates($request)
    {
        if ($request->contact_type != 'Autorizado') {
            $this['schedule'] = null;
            $this['start_date'] = null;
            $this['end_date'] = null;
        }

        if ($request->dateRangeClicked == 'false') {
            $this['start_date'] = null;
            $this['end_date'] = null;
        }
    }

    /**
     * @throws \Exception
     */
    public function updateContact($request)
    {
        try {
            $this->fillAttributes($request);

            BuildingContact::$contactOriginalIntercomName = !empty($this->getOriginal('intercom_name')) ? $this->getOriginal('intercom_name') : false;

            $attributes = [
                'email' => $request->input('email'),
                'security_word' => $request->input('security_word'),
                'foreign_document' => $request->input('foreign_document'),
                'gender' => $request->input('gender'),
                'birthday' => $request->input('birthday'),
                'updated_by' => backpack_user()->id,
                'notification_service_and_products' => $request->input('notification_service_and_products')
            ];

            $vehicles = explode(',', $request->field_of_vehicles);

            $this->revokeAccessToProhibidoIngreso($request);

            if ($vehicles) {
                $carContact = new CarContact();
                $carContact->updateCarContact($vehicles, $this->id);
            }

            $this->addNewsCarsFields($request->all(), $this);
            $this->formatUser();

            $attributes['name'] = $this->name;
            $attributes['surname'] = $this->surname;
            $attributes['ci'] = $this->ci;
            $this->updateBuildingContactRelation($request->all());

            self::update($attributes);


                self::checkIfUserHasManyBuildingsAndGenerateBuildingsString(Contact::find($this->id)) ?? $this->checkIfUserHasBuilding(Contact::find($this->id));
                self::checkIfUserHasManyBuildingsAndGenerateBuildingsString(User::find($this->id)) ?? $this->checkIfUserHasBuilding(User::find($this->id));

            ContactUpdatedEvent::dispatch($this);

        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function revokeAccessToProhibidoIngreso($request)
    {
        if ($request->contact_type == 'Prohibido Ingreso') {
            $aC = new AccessCode();

            if (isset($request->code)) {
                $aC->deleteForAkuvoxOr2NAndDatabase($this->id, $request->code, $request->input('building_id'));
            }

            if (isset($request->access_code_temporal)) {
                $aC->deleteForAkuvoxOr2NAndDatabase($this->id, $request->access_code_temporal, $request->input('building_id'));
            }
        }
    }


    public function addNewsCarsFields($request, $contact)
    {
        if (array_key_exists('add_matricula0', $request)) {
            $car = new Car();
            $car->building_id = $contact->building_id; //Proximamente se va a asociar solo al contacto
            $car->flat_id = $contact->flat_id; //No quiere decir que el auto pertenezca a este edificio, este es el edificio principal del usuario que posee el auto
            $car->plate = $request['add_matricula0'];
            $car->company = $request['add_marca0_'];
            $car->model = $request['add_modelo0_'];
            $car->save();
            CarContact::createCarContact([$car->id], $contact->id);
        }

        if (array_key_exists('add_matricula1', $request)) {
            $car = new Car();
            $car->building_id = $contact->building_id; //Proximamente se va a asociar solo al contacto
            $car->flat_id = $contact->flat_id; //No quiere decir que el auto pertenezca a este edificio, este es el edificio principal del usuario que posee el auto
            $car->plate = $request['add_matricula1'];
            $car->company = $request['add_marca1_'];
            $car->model = $request['add_modelo1_'];
            $car->save();
            CarContact::createCarContact([$car->id], $contact->id);
        }

        if (array_key_exists('add_matricula2', $request)) {
            $car = new Car();
            $car->building_id = $contact->building_id; //Proximamente se va a asociar solo al contacto
            $car->flat_id = $contact->flat_id; //No quiere decir que el auto pertenezca a este edificio, este es el edificio principal del usuario que posee el auto
            $car->plate = $request['add_matricula2'];
            $car->company = $request['add_marca2_'];
            $car->model = $request['add_modelo2_'];
            $car->save();
            CarContact::createCarContact([$car->id], $contactId);
        }

        if (array_key_exists('add_matricula3', $request)) {
            $car = new Car();
            $car->building_id = $contact->building_id; //Proximamente se va a asociar solo al contacto
            $car->flat_id = $contact->flat_id; //No quiere decir que el auto pertenezca a este edificio, este es el edificio principal del usuario que posee el auto
            $car->plate = $request['add_matricula3'];
            $car->company = $request['add_marca3_'];
            $car->model = $request['add_modelo3_'];
            $car->save();
            $carContact = new CarContact();
            CarContact::createCarContact([$car->id], $contact->id);
        }
    }

    public function scopeOnlyDeleted(Builder $query)
    {
        unset($query->getQuery()->wheres[0], $query->getQuery()->wheres[1]);
        return $query->withoutGlobalScopes()
            ->where(function ($query) {
                $query->whereNotNull('building_contact.deleted_at');
            });

    }

    public static function deleteDuplicatedContacts()
    {
        foreach (Contact::all() as $first) {
            foreach (Contact::all() as $second) {
                if ($first != $second) {
                    if (Contact::compareContactsAreSame($first, $second)) {
                        $second->delete();
                    }
                }
            }
        }
        return 'OK';
    }

    public static function compareContactsAreSame($first, $second)
    {

        return $first->name === $second->name && $first->building_id === $second->building_id && $first->flat_id === $second->flat_id;
    }

    public static function forceDeleteSoftDeleted()
    {
        foreach (Contact::onlyTrashed()->get() as $contact) {
            $contact->forceDelete();
        }
        return 'OK';
    }

    public static function restoreUser($userId)
    {
        self::withTrashed()->find($userId)->restore();
    }

    public function sortedResidentialContactTypes()
    {
        return array_sort(Contact::$residentialContactTypes);
    }

    public static function allContactTypes()
    {
        return array_unique(Contact::$sortedMergedContactTypes);
    }

    public static function mergeDescriptionAndRelationFields()
    {
        foreach (Contact::all() as $contact) {
            foreach ($contact->contacts as $buildingContact) {
                if ($buildingContact->description && $buildingContact->relation) {
                    $buildingContact->description = $buildingContact->description . "-" . $buildingContact->relation;
                } elseif (!$buildingContact->description && $buildingContact->relation) {
                    $buildingContact->description = $buildingContact->relation;
                }
                $buildingContact->relation = null;
            }

            $contact->save();
        }
        return 'OK';
    }

    public function authorizations()
    {
        if ($this->flat_id == null) {
            return null;
        }
        return BuildingContact::query()
            ->where('flat_id', $this->flat_id)
            ->where('building_id', $this->building_id)
            ->where('contact_id', '!=', $this->id)
            ->where('contact_type', 'Autorizado');
    }

    public static function storeApiContact($request)
    {
        try {

            if (!$request->name) {
                return json_encode([
                    'status' => '403',
                    'message' => 'El nombre es requerido.',
                ]);
            }

            $contact = new Contact();
            $contact->fillData($request);
            $contact->save();
            $contact->addCars($request->cars);


            return json_encode([
                'status' => '202',
                'message' => 'Contacto agregado correctamente.',
            ]);
        } catch (\Exception $e) {
            return json_encode([
                'status' => '500',
                'message' => 'No se ha podido agregar. Error: ' . $e,
            ]);
        }
    }

    public function addCars($cars)
    {
        try {
            foreach (json_decode($cars, true) as $carData) {
                $car = new Car();
                $car->fill($carData);
                $car->needs_verification = true;
                $car->save();
                $this->cars()->attach($car->id);
            }
        } catch (\Exception $e) {
            return json_encode([
                'status' => '500',
                'message' => 'No se ha podido agregar el vehiculo. Error: ' . $e,
            ]);
        }
    }

    public function fillData($request)
    {
        $this->fill($request->all());
        $this->name = $request->name;
        $this->surname = $request->surname;
        $this->complete_name = $request->complete_name;
        $this->ci = $request->document;
        $this->password = Hash::make($request->password ?? $this->ci);
        $this->phone_mobile = $request->phone_mobile;
        $this->needs_verification = true;
    }

    public static function sendRegistrationMail($type, $ids)
    {
        if ($type === 'all') {
            return User::sendRegistrationMailAll();
        }
        if ($type === 'building') {
            return Contact::sendRegistrationMailBuilding($ids);
        }
        return Contact::sendRegistrationMailContacts($ids);
    }

    public static function sendRegistrationMailAll()
    {
        foreach (Contact::all() as $contact) {
            //            Contact::findOrFail($contact->id)->sendRegisterEmail();
            //todo: commented to not send unwanted emails
        }
    }

    public static function sendRegistrationMailBuilding($building_id)
    {
        $building = Building::findOrFail($building_id);

        foreach ($building->residents as $contact) {
            Contact::findOrFail($contact->id)->sendRegisterEmail();
        }
    }

    public static function sendRegistrationMailContacts($contacts_id)
    {
        foreach ($contacts_id as $contact_id) {
            User::findOrFail($contact_id)->sendRegisterEmail();
        }
    }

    public function sendRegisterEmail()
    {
        if (!$this->notified && $this->email) {
            $this->token = md5(rand(0, 100000) . Carbon::now());
            $this->notify(new RegisterUser($this->token));
            $this->notified = true;
            $this->save();
            return redirect()->back();
        }
    }

    public function getIconOfType($contact_type = null): array
    {
        if (is_null($contact_type)) {
            $contact_type = $this->contact_type;
        }
        switch (strtolower($contact_type)) {
            case 'prohibido ingreso':
            case 'banned':
                return ['Prohibido ingreso', 'ban'];
            case 'no residente':
            case 'no_resident':
                return ['No Residente', 'bag'];
            case 'autorizado':
            case 'authorized':
                return ['Autorizado', 'person-add'];
            case 'empleado':
            case 'employer':
                return ['Empleado', 'briefcase'];
            case 'oficina':
            case 'office':
                return ['Oficina', 'cafe'];
            case 'responsable':
                return ['Responsable', 'medal'];
            case 'proveedor':
            case 'cadete':
                return ['Proveedor', 'cube-outline'];
            case 'foxsys':
                return ['Foxsys', 'body-outline'];
            default:
                return ['Residente', 'business'];
        }
    }

    public function getOthersTypesOfContact($contact_type = null)
    {
        if (is_null($contact_type)) {
            $contact_type = $this->attributes['contact_type'];
        }

        switch (strtolower($contact_type)) {
            case 'comisión':
            case 'commision':
                return [['Comision', 'las la-shield-alt']];
            case 'contacto principal - comisión':
            case 'principal_contact_commission':
                return [['Contacto Principal - Comision', 'las la-shield-alt'], ['Comision', 'la la-check']];
            case 'contacto principal':
            case 'principal_contact':
                return [['Contacto Principal', 'la la-check']];
            default:
                return [];
        }
    }

    public static function getOthersTypesOfContactFlat($contact_type = null)
    {
        if (!$contact_type)
            $contact_type = self::attributesToArray()['contact_type'];
        //        $this->contact_type;

        return match (strtolower($contact_type)) {
            "comisión", "commision" => [['Comision', 'la la-shield']],
            "contacto principal - comisión", "principal_contact_commision" => [['Contacto Principal - Comision', 'la la-shield'], ['Comision', 'la la-check']],
            "contacto principal", "principal_contact" => [['Contacto Principal', 'la la-check']],
            default => [],
        };
    }

    public static function getSearchAttributes()
    {
        return Contact::all()->pluck('search_data', 'id');
    }

    public
    function getCodeAttribute()
    {
        return $this->getAccessCodePermanent();
    }

    public function getAccessCodeTemporalAttribute()
    {
        return $this->getAccessCodeTemporal();
    }

    public function getAccessCodeExpirationDatesAttribute()
    {
        return $this->getAccessCodeExpirationDates();
    }

    public static function checkIfUserHasManyBuildingsAndGenerateBuildingsString($user)
    {
        $query = BuildingContact::where('contact_id', '=', $user->id)->with('building')->with('flat')->get();
        $array = array();
        if (sizeof($query) != 0) {
            $flat_string = '';

            foreach ($query as $data) {
                if (isset($data->building->name) && isset($data->flat->number))
                    $array[] = $data->building->building_number . ' - ' . $data->building->name . ' - ' . $data->flat->number;
            }
            $building_and_flat_string = implode(' | ', array_map(function ($entry) {
                return ($entry);
            }, $array));

            self::updateFullFlatsBuildingsText($user, $building_and_flat_string);
            return true;
        }
        return false;
    }

    public static function updateFullFlatsBuildingsText($user, $building_and_flat_string)
    {
        $user->full_flats_buildings_text = $building_and_flat_string;
        $user->save();
    }

    public static function checkIfUserHasBuilding($user)
    {
        $building = '';
        $building_name = '';
        $building_id = '';
        $flat_string = '';
        $flat = \App\Models\Flat::query()->find($user->flat_id);
        if (isset($flat)) {
            $building = \App\Models\Building::query()->find($flat['building_id']) ?? $building;
            if (isset($building)) {
                $building_name = $building->name ?? $building_name;
                $building_id = $building->building_number ?? $building_id;
            }
            $flat_string = $flat->number;
        }

        $user = \App\User::query()->find($user->id);
        $user->full_flats_buildings_text = $building_id . ' - ' . $building_name . ' - ' . $flat_string;
        $user->save();
    }

    public static function contactExists($name, $surname, $building_number, $flat, $tower_denomination)
    {
        $building_id = Building::query()->where('building_number', $building_number)->value('id');

        return User::withTrashed()->where('name', $name)
            ->where('surname', $surname)
            ->whereHas('buildings', function (Builder $query) use ($flat, $building_id, $tower_denomination) {
                $query->where('building_id', $building_id)
                    ->where('flat_id', self::findOrCreateFlat($building_id, $flat, $tower_denomination));
            })->exists();
    }

    public static function getContactIdByInfo($name, $surname, $building_number, $flat_id)
    {
        $name = Helper::removeAccents($name);
        $surname = Helper::removeAccents($surname);

        return \App\Models\User\User::where('name', $name)
            ->where('surname', $surname)
            ->where('building_number', (int)$building_number)
            ->where('flat_id', (int)$flat_id)
            ->first()
            ->id;
    }


    public static function findOrCreateFlat($building_id, $flat_number, $tower_denomination)
    {
        $building = Building::query()->find($building_id);
        $aux_flat = null;
        if ($building) {
            if ($building->buildingHasATower()) {
                foreach ($building->flats()->get() as $flat) {
                    $current_tower = Tower::find($flat->tower_id);

                    if (Tower::isSameTowerOrTowerIsNull($current_tower, $tower_denomination)) {
                        if(!in_array($flat_number, explode(',', $current_tower->flats))) {
                            $current_tower->flats = $current_tower->flats . ',' . $flat_number;
                            $current_tower->save();
                        }
                        $flat = Flat::where('number', $flat_number)->where('building_id', $building_id)?->first();
                        $aux_flat = $flat;
                    }
                }
            } else {
                $aux_flat = Flat::where('number', $flat_number)->where('building_id', $building_id)?->first();
                if (!$aux_flat) {
                    $attributes = [
                        'number' => $flat_number,
                        'building_id' => $building_id,
                        'created_by' => backpack_user()->id,
                    ];
                    $aux_flat = Flat::query()->create($attributes);
                }
            }
        }
        return $aux_flat?->id;
    }

    public
    function generateRandomNumberOf5Figures()
    {
        return rand(10000, 99999);
    }

    public static function removeContactFromUser($contact_id)
    {
        $lastRelation = false;
        $contact = self::query()
            ->where('id', $contact_id)
            ->first();

        if ($contact && sizeof($contact?->contacts) == 1) {
            $contact->update([
                'deleter_user_id' => backpack_user()->id,
                'asterisk_extension_number' => null,
                'asterisk_extension_password' => null
            ]);
            $contact->delete();
            $lastRelation = true;
        }

        return $lastRelation;
    }

    public static function saveSchedule($userId, $flatId, $schedule)
    {
        return BuildingContact::saveSchedule($userId, $flatId, $schedule);
    }

    public function mapBuildingContactData()
    {
        return $this->buildingContacts->map(function ($buildingContact) {
            return [
                "id" => $buildingContact->id,
                "building_id" => $buildingContact->building_id,
                "flat_id" => $buildingContact->flat_id,
                "contact_id" => $buildingContact->contact_id,
                "description" => $buildingContact->description,
                "contact_type" => $buildingContact->contact_type,
                "owner_or_tenant" => $buildingContact->owner_or_tenant,
                "type_of_contact_in_building" => $buildingContact->type_of_contact_in_building,
                "referrer" => $buildingContact->referrer,
                "empty_flat" => $buildingContact->empty_flat,
                "main_building" => $buildingContact->main_building,
                "phone_mobile" => $buildingContact->phone_mobile,
                "phone_home" => $buildingContact->phone_home,
                "relation" => $buildingContact->relation,
                "dont_call" => $buildingContact->dont_call,
                "foreign_phone" => $buildingContact->foreign_phone,
                "home_is_primary_phone" => $buildingContact->home_is_primary_phone,
                "contact_email" => $buildingContact->contact_email,
                "address" => $buildingContact->address,
                "door_number" => $buildingContact->door_number,
                "address_flat_number" => $buildingContact->address_flat_number,
                "pin" => $buildingContact->pin,
                "asterisk_extension_number" => $buildingContact->asterisk_extension_number,
                "videocall_notifications_disabled" => $buildingContact->videocall_notifications_disabled,
                "access_code" => $buildingContact->access_code,
                "access_code_2" => $buildingContact->access_code_2,
                "created_at" => $buildingContact->created_at,
                "updated_at" => $buildingContact->updated_at,
                "deleted_at" => $buildingContact->deleted_at,
                "notes_2" => $buildingContact->notes_2,
                "notes_3" => $buildingContact->notes_3,
                "schedule" => $buildingContact->schedule,
                "deleter_user_id" => $buildingContact->deleter_user_id,
                "cases_open" => $buildingContact->cases_open,
                "total_cases_announcements" => $buildingContact->total_cases_announcements,
                "announcements" => $buildingContact->announcements,
                "start_date" => $buildingContact->start_date,
                "end_date" => $buildingContact->end_date,
                "complete_name" => $buildingContact->complete_name,
                "building_name" => $buildingContact->building_name,
                "email" => $buildingContact->email,
                "ci" => $buildingContact->ci,
                "security_word" => $buildingContact->security_word,
                "image" => $buildingContact->image,
                "intercom_name" => $buildingContact->intercom_name,
                "intercom_incoming_call" => $buildingContact->intercom_incoming_call,
                "name_kazoo" => $buildingContact->name_kazoo,
                "user_sip_kazoo" => $buildingContact->user_sip_kazoo,
                "password_kazoo" => $buildingContact->password_kazoo,
                "kazoo_device_id" => $buildingContact->kazoo_device_id,
                "callflow_id" => $buildingContact->callflow_id,
                "created_by" => $buildingContact->created_by,
                "building_contact_id" => $buildingContact->building_contact_id,
                "code" => $buildingContact->code,
                "access_code_temporal_2" => $buildingContact->access_code_temporal_2,
                "building_number" => $buildingContact->building_number,
                "flat_number" => $buildingContact->flat_number,
                "tags" => $buildingContact->tags,
                "dateRangeClicked" => $buildingContact->dateRangeClicked,
                "real_primary_phone" => $buildingContact->real_primary_phone,
                "real_secondary_phone" => $buildingContact->real_secondary_phone,
                "name" => $buildingContact->name,
                "surname" => $buildingContact->surname,
                "foreign_document" => $buildingContact->foreign_document,
                "announcements_cases" => $buildingContact->announcements_cases,
                "number_kazoo" => $buildingContact->number_kazoo,
                "flat" => $buildingContact->flat
            ];
        });
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function companies()
    {
        return $this->belongsToMany(\App\Models\Company\Company::class, 'company_contact', 'contact_id', 'company_id');
    }

    public function tags()
    {
        return $this->hasMany(\App\Models\Tag::class, 'contact_id');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public
    function getBirthdayAttribute()
    {
        if (isset($this->attributes['birthday'])) {
            return Carbon::parse($this->attributes['birthday']);
        }
        return null;
    }

    public function getSearchDataAttribute()
    {
        return $this->complete_name . ' - ' . $this->getOnlyPrimaryPhone();
    }


    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

}
