<?php

namespace App\Models\User;

use App\Events\ContactUpdatedEvent;
use App\Http\Helpers\UserScope;
use App\Integrations\FreePBX\FreePBXExtension;
use App\Integrations\FreePBX\PushToken;
use App\Integrations\FreePBX\RingingGroup;
use App\Models\AccessCode;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\BuildingIntercom;
use App\Models\Caseq;
use App\Models\Flat;
use App\Models\InternalPushToken;
use App\Models\KazooFlatsConsecutive;
use App\Models\Log;
use App\Models\ModelHasRoles;
use App\Models\Tower;
use App\Notifications\AsteriskDownNotification;
use App\Notifications\RegisterUser;
use App\Traits\ContactTrait;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Matrix\Exception;
use Spatie\Permission\Traits\HasRoles;
use Tests\Browser\ConfigureAsteriskExtensions;


class User extends \App\User
{

    use CrudTrait, SoftDeletes, HasRoles, Notifiable, ContactTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $dontKeepRevisionOf = ['created_by', 'updated_by', 'image'];
    protected $table = 'users';

    // protected $primaryKey = 'id';
    // public $timestamps = false;
    // protected $guarded = ['id'];
    protected $fillable = [
        'name',
        'email',
        'contact_email',
        'surname',
        'complete_name',
        'password',
        'ci',
        'permissions',
        'flat_adm',
        'tags',
        'pin',
        'type',
        'access_code',
        'api_token',
        'foreign_document',
        'under_age',
        'created_by',
        'updated_by',
        'needs_verification',
        'image',
        'is_admin',
        'token',
        'notified',
        'gender',
        'birthday',
        'get_keys',
        'no_resident',
        'deleter_user_id',
        'created_from_app',
        'asterisk_extension_number',
        'asterisk_extension_password',
        'last_login',
        'principal_contact',
        'others_mobil_phones',
        'device_brand',
        'device_model',
        'software_version',
        'last_auto_login',
        'app_version',
        'connectivity',
        'screen_size',
        'auto_login_status',
        'principal_contact',
        'device_supports_video_call',
        'emergency_contact_id',
        'emergency_contact_notes',
        'full_flats_buildings_text',
        'extension_3cx',
        'start_date',
        'end_date',
        'entity_type',
        'security_word',
        'notification_service_and_products'
    ];

    protected $dates = ['birthday', 'last_login'];

    protected $hidden = [
        'password',
        'remember_token',
    ];
    protected $types = ['user' => 'Usuario', 'admin' => 'Admin'];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'referrer' => 'boolean',
    ];

    protected $with = ['mainBuilding'];

    protected $appends = [
        'intercoms',
        'rtspUrl',
        'open_door_disabled',
        'flat_number',
        'search_data',
        'video_disabled',
        'videocall_disabled',
        'video_call_hability',

        'building_contact_id',
        'building_id',
        'flat_id',
        'description',
        'contact_type',
        'owner_or_tenant',
        'type_of_contact_in_building',
        'referrer',
        'empty_flat',
        'phone_home',
        'relation',
        'dont_call',
        'foreign_phone',
        'home_is_primary_phone',
        'contact_email',
        'address',
        'door_number',
        'address_flat_number',
        'notes_2',
        'notes_3',
        'schedule',
        'cases_open',
        'real_primary_phone',
        'announcements',
        'start_date',
        'end_date',
        'building_name',
        'building_number',
        'phone_mobile',
        'video_call_hability',
        'temporary_contact',
        'kazoo_extension',
        'intercom_name',
        'name_kazoo',
        'user_sip_kazoo',
        'password_kazoo',
        'number_kazoo',
        'kazoo_device_id',
        'callflow_id',
        'intercom_incoming_call',
    ];

    public function getAddressAttribute()
    {
        return $this->mainBuilding?->address;
    }

    public function getCasesOpenAttribute()
    {
        return $this->mainBuilding?->cases_open;
    }

    public function getBuildingContactIdAttribute()
    {
        return $this->mainBuilding?->contact_id;
    }

    public function getDescriptionAttribute()
    {
        return $this->mainBuilding?->description;
    }

    public function getContactTypeAttribute()
    {
        return $this->mainBuilding?->contact_type;
    }

    public function getOwnerOrTenantAttribute()
    {
        return $this->mainBuilding?->owner_or_tenant;
    }

    public function getTypeOfContactInBuildingAttribute()
    {
        return $this->mainBuilding?->type_of_contact_in_building;
    }

    public function getReferrerAttribute()
    {
        return $this->mainBuilding?->referrer;
    }

    public function getEmptyFlatAttribute()
    {
        return $this->mainBuilding?->empty_flat;
    }

    public function getPhoneHomeAttribute()
    {
        return $this->mainBuilding?->phone_home;
    }

    public function getRelationAttribute()
    {
        return $this->mainBuilding?->relation;
    }

    public function getDontCallAttribute()
    {
        return $this->mainBuilding?->dont_call;
    }

    public function getForeignPhoneAttribute()
    {
        return $this->mainBuilding?->foreign_phone;
    }

    public function getHomeIsPrimaryPhoneAttribute()
    {
        return $this->mainBuilding?->home_is_primary_phone;
    }

    public function getContactEmailAttribute()
    {
        return $this->mainBuilding?->contact_email;
    }

    public function getDoorNumberAttribute()
    {
        return $this->mainBuilding?->door_number;
    }

    public function getAddressFlatNumberAttribute()
    {
        return $this->mainBuilding?->address_flat_number;
    }

    public function getNotes2Attribute()
    {
        return $this->mainBuilding?->notes_2;
    }

    public function getNotes3Attribute()
    {
        return $this->mainBuilding?->notes_3;
    }

    public function getScheduleAttribute()
    {
        return $this->mainBuilding?->schedule;
    }

    public function getStartDateAttribute()
    {
        if (!$this->mainBuilding || !$this->mainBuilding->contact_type == 'Autorizado')
            return null;

        return $this->mainBuilding->start_date;
    }


    public function getEndDateAttribute()
    {
        if (!$this->mainBuilding || !$this->mainBuilding->contact_type == 'Autorizado')
            return null;

        return $this->mainBuilding->end_date;
    }

    public static $authorizedTypes = [
        'Autorizado' => 'Autorizado',
        'Comisión' => 'Comisión',
        'Proveedor' => 'Proveedor',
        'Empleado' => 'Empleado',
        'Prohibido Ingreso' => 'Prohibido Ingreso'
    ];


    public static $residentTypes = [
        'Contacto Principal' => 'Contacto Principal',
        'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
        'Residente' => 'Residente',
        'Responsable' => 'Responsable',
        'Comisión' => 'Comisión',
        'Foxsys' => 'Foxsys',
    ];

    public static $authorizedLoginTypes = [
        'Contacto Principal' => 'Contacto Principal',
        'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
        'Residente' => 'Residente',
        'Responsable' => 'Responsable',
        'Oficina' => 'Oficina',
        'Comisión' => 'Comisión',
        'Foxsys' => 'Foxsys'
    ];

    public static $authorizedAccessTypes = [
        'Contacto Principal' => 'Contacto Principal',
        'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
        'Residente' => 'Residente',
        'Responsable' => 'Responsable',
        'Oficina' => 'Oficina',
        'Comisión' => 'Comisión',
        'Foxsys' => 'Foxsys',
        'Autorizado' => 'Autorizado',
        'Proveedor' => 'Proveedor',
        'Empleado' => 'Empleado',
    ];

    public static $nonAuthorizedAccessTypes = [
        'Prohibido Ingreso' => 'Prohibido Ingreso'
    ];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    public function getBuildingIdAttribute()
    {
        return $this->mainBuilding?->building_id;
    }

    public function getValueOfAppFields()
    {
        return [
            'name' => $this->name,
            'surname' => $this->surname,
            'ci' => $this->ci,
            'foreign_document' => $this->foreign_document,
            'under_age' => $this->under_age,
            'email' => $this->email,
            'phone_mobile' => $this->phone_mobile,
            'phone_home' => $this->phone_home,
            'foreign_phone' => $this->foreign_phone,
            'owner_or_tenant' => $this->owner_or_tenant,
            'address' => $this->address,
            'door_number' => $this->door_number,
            'address_flat_number' => $this->address_flat_number,
        ];
    }

    public function routeNotificationForMail($notification)
    {
        // Return email address only...
        return $this->email;
    }

    public function getBuildingNameAttribute()
    {
        return $this->mainBuilding->building_name ?? '';
    }

    public function getBuildingNumberAttribute()
    {
        return $this->mainBuilding->building_number ?? '';
    }

    public function buildingNumber()
    {
        return $this->mainBuilding->building_number ?? '';
    }


    public function flatName()
    {
        if ($this->flat) {
            return $this->flat->number;
        }
        return 'No se asignó apartamento.';
    }

    public function formatUser()
    {
        $this->formatDocument();
        $this->formatNames();
        $this->formatPhones();
    }

    public function formatNames()
    {
        $names = explode(' ', trim($this->name));
        $parsed = [];
        foreach ($names as $name) {
            $name = mb_strtolower($name);
            $parsed[] = Str::ucfirst($name);
        }
        $this->name = implode(' ', $parsed);
        //        $this->save();
    }


    public function formatDocument()
    {
        $this->ci = $this->formatDocumentByCi($this->ci, $this->foreign_document);
    }

    public static function formatDocumentByCi($ci, $foreign_document)
    {
        if ($ci) {
            if (!$foreign_document && User::startsWithNumber($ci)) {
                $document = str_replace(
                    ' ',
                    '',
                    str_replace(
                        '-',
                        '',
                        str_replace('.', '', $ci)
                    )
                );
                if (strlen($document) == 8) {
                    $document = substr($document, 0, 1) . '.' .
                        substr($document, 1, 3) . '.' .
                        substr($document, 4, 3) . '-' .
                        substr($document, 7, 1);
                } else if (strlen($document) == 7) {
                    $document =
                        substr($document, 0, 3) . '.' .
                        substr($document, 3, 3) . '-' .
                        substr($document, 6, 1);
                }
                $ci = $document;
            }
            $ci = strtoupper($ci);
        }
        return $ci;
    }

    public static function startsWithNumber($string)
    {
        return preg_match('/^\d/', $string) === 1;
    }

    public function formatPhones()
    {
        BuildingContact::trimPhones($this);
    }


    public function getPrimaryPhone()
    {
        return $this->formatPhone($this->home_is_primary_phone ? $this->phone_home : $this->phone_mobile);
    }

    public function getSecondaryPhone()
    {
        return $this->formatPhone(!$this->home_is_primary_phone ? $this->phone_home : $this->phone_mobile);
    }

    public function getOnlyPrimaryPhone()
    {
        if ($this->home_is_primary_phone == 0 && $this->phone_mobile != null && $this->phone_mobile != '') {
            return $this->formatPhone($this->phone_mobile);
        }
        return $this->formatPhone($this->phone_home);
    }

    public function getOnlySecondaryPhone()
    {
        if ($this->home_is_primary_phone == 1 || $this->phone_mobile == null) {
            return $this->formatPhone($this->phone_mobile);
        }
        return $this->formatPhone($this->phone_home);
    }

    public function getOnlyExtraPhone()
    {
        if (!$this->others_mobil_phones) {
            return false;
        }
        $phones = json_decode($this->others_mobil_phones);
        $phoneNumbers = [];
        foreach ($phones as $phone) {
            if ($phone->phone_number != '') {
//                $phoneNumbers[] = $this->formatPhone($phone->phone_number);
                $phoneNumbers[] = $phone->phone_number;
            }
        }

        return $phoneNumbers;
    }

    public function formatPhone($phone)
    {
        if (starts_with($phone, '09')) {
            $splitted = str_split(str_replace(' ', '', $phone), 3);
            $formatted = implode(' ', $splitted);
            $phone = $formatted;
        } else if (starts_with($phone, '2')) {
            $splitted = str_split(str_replace(' ', '', $phone), 4);
            $formatted = implode(' ', $splitted);
            $phone = $formatted;
        }
        return $phone;
    }

    public function fillAttributes($request)
    {
        if ($request->password == null || $request->password_confirmation == null) {
            $this->fill($request->except(['password', 'password_confirmation']));
        } else {
            $this->fill($request->all());
        }
    }

    public function appContacts()
    {
        return User::whereNotNull('token')->get();
    }

    public function authorizations()
    {
        if ($this->mainBuilding->flat_id == null) {
            return null;
        }
        return BuildingContact::query()
            ->where('flat_id', $this->mainBuilding->flat_id)
            ->where('building_id', $this->mainBuilding->building_id)
            ->where('contact_id', '!=', $this->id)
            ->whereIn('contact_type', User::$authorizedTypes);
    }

    public function residents()
    {
        if ($this->mainBuilding->flat_id == null) return null;

        return BuildingContact::where('flat_id', $this->mainBuilding->flat_id)
            ->where('building_id', $this->building_id)
            ->whereIn('contact_type', User::$residentTypes);
    }

    public static function info($number)
    {
        return User::whereHas('buildingContacts', function ($query) use ($number) {
            $query->where('phone_mobile', $number)
                ->orWhere('phone_home', $number);
        })->first() ?? null;
    }


    /*
     * ===================================
     * Tower Building Flat Examples
     * ===================================
     * T1-101
     * TA-101
     * P1-101
     */

    public function getUserTower()
    {

        if ($this->flat->count() > 0) {
            if ($this->flat[0]->tower) {
                return $this->flat[0]->tower->tower_denomination;
            }
        }
        return null;
    }

    public function storeDeviceInfo($deviceBrand, $deviceModel, $softwareVersion)
    {
        $this->device_brand = $deviceBrand;
        $this->device_model = $deviceModel;
        $this->software_version = $softwareVersion;
        $this->save();
    }

    public function updateLastAutoLogin(Request $request)
    {
        $now = Carbon::now('America/Montevideo')->format('Y-m-d H:i:s');
        $this->last_auto_login = $now;
        $this->app_version = $request->app_version;
        $this->connectivity = $request->connectivity;
        $this->screen_size = $request->screen_size;
        $this->auto_login_status = $request->auto_login_status;
        $this->last_auto_login_list = $this->populateLastAutoLoginList($now);
        $this->save();
    }


    private function populateLastAutoLoginList($date): string
    {
        $items = json_decode($this->last_auto_login_list);
        $logins = array();

        $logins[] = $date;
        if ($items) {
            foreach ($items as $array_item) {
                $logins[] = $array_item;
            }
        }

        return json_encode($logins);
    }

    public function getAccessCodeTemporal()
    {
        $codes = $this->accessCodes()->whereNotNull('expires_at')->whereNotNull('code')
            ->where('expires_at', '>', Carbon::now())->get();

        return $codes->pluck('code')->toArray();
    }


    public function getAccessCodeExpirationDates()
    {
        $codes = $this->accessCodes()->whereNotNull('expires_at')->whereNotNull('code')
            ->where('expires_at', '>', Carbon::now())->get();

        return $codes->pluck('expires_at')->map(function ($date) {
            return Carbon::parse($date)->format('d/m/y H:i:s');
        })->toArray();
    }


    public function getAccessCodePermanent()
    {
        $code = $this->accessCodes()->whereNull('expires_at')->first();
        return $code ? $code->code : null;
    }

    public function userAccessAuthorized()
    {
        return !in_array($this->contact_type, self::$nonAuthorizedAccessTypes);
    }

    public function isTemporalContact()
    {
        $currentContact = $this->contacts
            ->where('contact_id', $this->id)
            ->where('building_id', $this->building_id)
            ->where('flat_id', $this->flat_id)
            ->first();

        return $currentContact?->start_date && $currentContact?->end_date;
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function mainBuilding()
    {
        return $this->hasOneThrough(BuildingContact::class, User::class, 'id', 'contact_id')
            ->where('building_contact.main_building', true);
    }


    public function activeTracings()
    {
        return $this->hasMany(Caseq::class, 'user_id')
            ->where('tracing', 1)
            ->where('state', '!=', 'finalizado');
    }

    public function cases()
    {
        return $this->hasMany(Caseq::class, 'user_id');
    }

    public function accessCodes()
    {
        return $this->hasMany(AccessCode::class, 'user_id');
    }


    public function casesOpen()
    {
        return $this->hasMany(Caseq::class, 'user_id')->where('state', '!=', 'finalizado');
    }

    //Todo: Ver si sacar o no!!
    public function building()
    {
        return $this->belongsToMany(Building::class, 'building_contact', 'contact_id', 'building_id')
            ->where('buildings.id', $this->building_id);
    }

    public function contacts()
    {
        return $this->hasMany(BuildingContact::class, 'contact_id', 'id');
    }

    public function buildings()
    {
        return $this->belongsToMany(Building::class, 'building_contact', 'contact_id', 'building_id')
            ->whereNull('building_contact.deleted_at');
    }

    public function buildingContacts()
    {
        return $this->hasMany(BuildingContact::class, 'contact_id', 'id');
    }

    public function flats()
    {
        return $this->belongsToMany(Flat::class, 'building_contact', 'contact_id', 'flat_id');
    }

    public function cars()
    {
        return $this->belongsToMany('App\Models\Car', 'car_contact');
    }


    public function logs()
    {
        return $this->hasMany(Log::class, 'user_id', 'id');
    }

    public function flat()
    {
        $flatId = null;
        if ($this->mainBuilding) {
            $flatId = $this->mainBuilding->flat_id;
        }
        return $this->belongsToMany(Flat::class, 'building_contact', 'contact_id', 'flat_id')->where('flats.id', $flatId);
    }

    public function intercoms()
    {
        return $this->hasManyThrough(
            BuildingIntercom::class,
            Building::class,
            'id', // Foreign key on building table...
            'building_id', // Foreign key on intercoms table...
            'building_id', // Local key on users table...
            'id' // Local key on building table...
        );
    }


    public function buildingIntercoms()
    {
        return $this->building->intercoms($this->getUserTower());
    }


    public function secondaryInfo()
    {
        return $this->hasMany(BuildingContact::class, 'contact_id', 'id');
    }

    public function userRoles(): MorphToMany
    {

        return $this->morphToMany(
            config('permission.models.role'),
            'model',
            config('permission.table_names.model_has_roles'),
            config('permission.column_names.model_morph_key'),
            'role_id'
        );
    }


    public function splitName()
    {
        $fullName = $this->name;
        $name = explode(' ', $fullName, 2)[0];
        if (count(explode(' ', $fullName, 2)) >= 2) {
            $surname = explode(' ', $fullName, 2)[1];
            if ($this->surname == null) {
                $this->name = $name;
                $this->surname = $surname;
                $this->save();
            }
        }
    }


    public function generateAsteriskFlatExtension()
    {
        $extension = new \App\Integrations\AsteriskAPI\Extension(null, $this->flat);

        if ($this->flat->asterisk_extension_number) {
            //if this extensions flat has already the asterisk ext created
            //echo 'USERS FLAT ALREADY REGISTERED IN ASTERISK. EXT NUMBER: ' . $this->flat->asterisk_extension_number;
            return;
        }
        try {
            //Crate Extension and retrieve number and password
            $this->setExtension($extension);
        } catch (Exception $e) {
            echo 'ERROR: ' . $e->getMessage();
        }
    }

    public static function generate2nUser($flat)
    {
        echo 'Generating 2n User... ' . PHP_EOL;
        $flatNumber = $flat->number;
        $extNumber = $flat->asteriskExtensionNumber;
        $ip = $flat->building->intercoms()->first()->ip;

        $host = '**********';
        if (env('LOGS_HOST')) {
            $host = env('LOGS_HOST') . ':' . env('LOGS_HTTP_PORT');
        }

        $url = $host . '/api/user/create/2n';

        echo 'Url: ' . $url . PHP_EOL;

        $header = array("Content-Type: application/json");
        $settings = [];
        $settings['flatNumber'] = $flatNumber;
        $settings['extensionNumber'] = $extNumber;
        $settings['ip'] = $ip;


        $query = '';
        try {
            $curl = curl_init();

            curl_setopt_array(
                $curl,
                array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($settings),
                    CURLOPT_HTTPHEADER => $header,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                )
            );
            $response = curl_exec($curl);
            if ($response === FALSE) {
                echo 'RESPONSE CREATE 2N USER: ' . $response . PHP_EOL;

                die('Curl failed: ' . curl_error($curl));
            }
            echo 'RESPONSE CREATE 2N USER: ' . $response . PHP_EOL;

            curl_close($curl);

            if (
                array_key_exists('errors', json_decode($response, true))
                && json_decode($response, true)['errors']['0']['status'] === 'false'
            ) {
                return json_encode(['status' => '501', 'message' => json_decode($response, true)['errors']['0']['message']]);
            }
            echo '2n user generated... ' . PHP_EOL;

            return json_encode(['status' => '201', 'message' => json_decode($response, true)]);
        } catch (\Http\Client\Exception $exception) {
            return json_encode(['status' => '501', 'message' => $exception->getMessage()]);
        }
    }

    public function generateAsteriskExtension()
    {

        $extension = new \App\Integrations\AsteriskAPI\Extension($this, null);

        if ($this->flat && $this->flat->asterisk_extension_number) {
            //if this extensions flat has already the asterisk ext created
            echo 'USERS FLAT ALREADY REGISTERED IN ASTERISK. EXT NUMBER: ' . $this->flat->asterisk_extension_number;
            return;
        }
        if ($this->flatName() === 'Edificio') {
            //for now they wont have an app
            return;
        }

        try {
            echo 'Generating User Configurations... ' . PHP_EOL;
            $this->setExtension($extension);
            // Run dusk browser automation manually
            // php artisan dusk .\tests\Browser\ConfigureAsteriskExtensions.php with extensions

            // $this->generate2nUser();

            echo 'All OK!... extension created and configured: ' . $this->flat->asterisk_extension_number . PHP_EOL;
        } catch (Exception $e) {
            echo 'ERROR: ' . $e->getMessage();
        }
    }


    public static function generateAllAsteriskFlatsExtensions($buildingNumber)
    {
        echo 'Generating flats extensions and 2n users for building ' . $buildingNumber . PHP_EOL;

        //        foreach (\DB::table('building_intercoms')->select('building_id')->distinct()->take(1)->get() as $building) {
        //        echo 'Building number: ' . $buildingNumber . PHP_EOL;

        foreach (Building::where('building_number', $buildingNumber)->first()->flats()->whereNull('deleted_at')->whereNull('asterisk_extension_number')->get() as $flat) {

            echo 'Setting up... Flat: ' . $flat->full_name . PHP_EOL;
            $extension = new \App\Integrations\AsteriskAPI\Extension(null, $flat);

            if ($flat->asterisk_extension_number) {
                //if this extensions flat has already the asterisk ext created
                //echo 'USERS FLAT ALREADY REGISTERED IN ASTERISK. EXT NUMBER: ' . $this->flat->asterisk_extension_number;
                return;
            }
            try {
                //Crate Extension and retrieve number and password
                self::setFlatExtension($extension, $flat);
                //needed: now run dusk to configure the dtls enabled

                self::generate2nUser($flat);

                echo 'DONE!' . PHP_EOL;
            } catch (Exception $e) {
                echo 'ERROR: ' . $e->getMessage();
            }
        }
        //        }
    }

    private function setExtension($extension)
    {
        echo 'Storing flat extension... ' . PHP_EOL;
        $access_token = json_decode($extension->authenticateAsterisk(), true)['access_token'];
        if (json_decode($extension->createExtension($access_token), true)['status'] === '201') {
            $password = json_decode($extension->getExtensionPassword($access_token), true)['password'];

            //Store Extension credentials in users flat
            $this->storeFlatExtension($extension->extId, $password);

            //Edit Extensions advanced params
            FreePBXExtension::updateFlat($extension->extName, $extension->extId, $password);
        }
    }

    public static function storeFlatExtensionWithFlat($number, $password, $flat)
    {
        $flat->asterisk_extension_number = $number;
        $flat->asterisk_extension_password = $password;
        $flat->save();
    }

    private static function setFlatExtension($extension, $flat)
    {
        $access_token = json_decode($extension->authenticateAsterisk(), true)['access_token'];
        if (json_decode($extension->createExtension($access_token), true)['status'] === '201') {
            $password = json_decode($extension->getExtensionPassword($access_token), true)['password'];

            //Store Extension credentials in users flat
            self::storeFlatExtensionWithFlat($extension->extId, $password, $flat);

            //Edit Extensions advanced params
            FreePBXExtension::updateFlat($extension->extName, $extension->extId, $password);
        }
    }

    public function storeFlatExtension($number, $password)
    {
        $this->flat->asterisk_extension_number = $number;
        $this->flat->asterisk_extension_password = $password;
        $this->flat->save();
    }

    public function changeVideoCallNotificationPreferences($disabled)
    {

        $token = PushToken::where('p_extension_number', '=', $this->asteriskExtensionNumber)->first();
        if ($token) {
            $token->p_notifications_enabled = !$disabled;
            $token->save();
        }
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeVerification($query)
    {
        unset($query->getQuery()->wheres[0]);

        return $query->whereNull('needs_verification');
    }

    public function scopeNoVerification($query)
    {
        unset($query->getQuery()->wheres[0]);
        return $query->where('users.needs_verification', 1);
    }

    public function scopeNoVerificationCAdete($query)
    {
        unset($query->getQuery()->wheres[0]);

        return $query->where('users.needs_verification', 2);
    }

    public function scopeCi($query, $value)
    {
        unset($query->getQuery()->wheres[0]);
        $ci = User::find($value) ? User::find($value)->ci : null;
        return $ci ? $query->where('users.ci', 'LIKE', "%$ci%")->where('users.id', '!=', $value) : null;
    }

    public function uploadMultipleFilesToDisk($value, $attribute_name, $disk, $destination_path)
    {

        $validFile = false;


        if (!is_array($this->{$attribute_name})) {
            $attribute_value = json_decode($this->{$attribute_name}, true) ?? [];
        } else {
            $attribute_value = $this->{$attribute_name};
        }
        $files_to_clear = request()->get('clear_' . $attribute_name);
        // if a file has been marked for removal,
        // delete it from the disk and from the db
        if ($files_to_clear) {
            foreach ($files_to_clear as $key => $filename) {
                \Storage::disk($disk)->delete($filename);
                $attribute_value = Arr::where($attribute_value, function ($value, $key) use ($filename) {
                    return $value != $filename;
                });
            }
        }
        // if a new file is uploaded, store it on disk and its filename in the database
        foreach ((array)$value as $file) {
            if ($file->isValid()) {
                $validFile = true;
            }
        }

        if ($validFile) {
            foreach (request()->$attribute_name as $file) {
                if ($file->isValid() && $file->getSize() <= 50000000) {
                    // 1. Generate a new file name
                    $new_file_name = $file->getClientOriginalName() . '-' . random_int(1, 99999) . '.' . $file->getClientOriginalExtension();
                    // 2. Move the new file to the correct path
                    $file_path = $file->storeAs($destination_path, $new_file_name, $disk);

                    // 3. Add the public path to the database
                    $attribute_value[] = $file_path;
                }
            }
        }
        $this->attributes[$attribute_name] = json_encode($attribute_value);
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getTemporaryContactAttribute()
    {
        return $this->start_date || $this->end_date;
    }

    public function getKazooExtensionAttribute()
    {
        return $this->mainBuilding?->kazoo_extension;
    }

    public function getIntercomNameAttribute()
    {
        return $this->mainBuilding?->intercom_name;
    }

    public function getNameKazooAttribute()
    {
        return $this->mainBuilding?->name_kazoo;
    }

    public function getUserSipKazooAttribute()
    {
        return $this->mainBuilding?->user_sip_kazoo;
    }

    public function getPasswordKazooAttribute()
    {
        return $this->mainBuilding?->password_kazoo;
    }

    public function getNumberKazooAttribute()
    {
        $kazooFlatsConsecutives = KazooFlatsConsecutive::query()->where('contact_id', $this->mainBuilding?->contact_id)->where('flat_id',
            $this->flat_id)->value('kazoo_number');
        return $this->mainBuilding?->kazoo_device_id && $kazooFlatsConsecutives ? $kazooFlatsConsecutives : "";
    }

    public function getCallflowIdAttribute()
    {

        return KazooFlatsConsecutive::query()->where('contact_id', $this->mainBuilding?->contact_id)->where('flat_id', $this->mainBuilding?->flat_id)->value('callflow_id') ?? "";
    }

    public function getKazooDeviceIdAttribute()
    {

        return KazooFlatsConsecutive::query()->where('contact_id', $this->mainBuilding?->contact_id)->where('flat_id', $this->mainBuilding?->flat_id)->value('device_id') ?? "";
    }

    public function getIntercomIncomingCallAttribute()
    {
        return $this->mainBuilding?->intercom_incoming_call;
    }


    public function getPhoneMobileAttribute()
    {
        return $this->mainBuilding?->phone_mobile;
    }

    public function getAnnouncementsAttribute()
    {
        $announcements = $this->contacts
            ->where('contact_id', $this->id)
            ->where('flat_id', $this->flat_id)
            ->where('building_id', $this->building_id)
            ->value('announcements');
        return $announcements ?? [];
    }

    public function getFlatIdAttribute()
    {
        return $this->mainBuilding ? $this->mainBuilding->flat_id : null;
    }

    public function getFlatNumberAttribute()
    {
        if ($this->mainBuilding) {
            return $this->mainBuilding->flat?->number_with_tower;
        }
    }

    public function getFlatNumberWithTowerAttribute()
    {
        if ($this->mainBuilding->flat) {
            return $this->mainBuilding->flat_number;
        }
    }


    //TODO:por que?????
    public function getName($id)
    {
        return User::get('id', $id)->first()->name;
    }

    public function getIntercomsAttribute()
    {
        $userTower = $this->getUserTower();
        $response = [];

        if (!is_null($this->building)) {
            $intercoms = json_decode($this->building()->first()?->intercomsData($this->id)) ?? [];

            if (!is_null($userTower)) {
                foreach ($intercoms as $intercom) {
                    if (
                        !$intercom->tower_prefix ||
                        ($intercom->tower_prefix && $intercom->tower_prefix == $userTower)
                    ) {
                        $response[] = $intercom;
                    }
                }
            } else {
                $response = $intercoms;
            }
        }


        return json_encode($response);
    }

    public function getOpenDoorDisabledAttribute()
    {
        return $this->building->count() > 0 ? $this->building()->first()->open_door_disabled : false;
    }

    public function getVideoDisabledAttribute()
    {
        return $this->mainBuilding->videocall_notifications_disabled ?? false;
    }

    //old version kept for older versions of the app
    public function getVideocallDisabledAttribute()
    {
        return $this->building->count() > 0 ? $this->building()->first()->open_door_disabled : false;
    }

    //old version kept for older versions of the app
    public function getVideoCallHabilityAttribute()
    {
        return $this->building->count() > 0 ? !$this->building()->first()->video_disabled : false;
    }


    public function getRtspUrlAttribute()
    {
        if (!is_null($this->building)) {

            $intercom = $this->building->count() > 0
                ? $this->building()->first()->intercoms()->where('rtsp_port', '!=', null)->first()
                : null;

            if ($intercom) {
                return $intercom->dns . ':' . $intercom->rtsp_port;
            }
            return null;
        }
    }

    public function getAsteriskExtensionNumberAttribute()
    {
        if ($this->flat->count() > 0 && $this->flat()->first()->asterisk_extension_number) {
            return $this->flat[0]->asterisk_extension_number;
        }
        if ($this->building->count() > 0 && $this->building()->first()->asterisk_extension_number) {
            return $this->building[0]->asterisk_extension_number;
        }
        return config('app.asterisk_default_extension_number');
    }

    public function getAsteriskExtensionPasswordAttribute()
    {

        if ($this->flat->count() > 0 && $this->flat()->first()->asterisk_extension_password) {
            return $this->flat[0]->asterisk_extension_password;
        }
        if ($this->building->count() > 0 && $this->building()->first()->asterisk_extension_password) {
            return $this->building[0]->asterisk_extension_password;
        }
        return config('app.asterisk_extension_password');
    }

    public function getUserNameBuildingFlatAttriute()
    {
        return $this->attributes['complete_name'];
    }


    public function getSearchDataAttribute()
    {
        return $this->complete_name . ' - ' . $this->getOnlyPrimaryPhone();
    }


    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */


    public function setImageAttribute($value)
    {
        $attribute_name = "image";
        $disk = 's3';
        $destination_path = "uploads/contact";

        if ($value == null) {
            if (!empty($this->{$attribute_name})) {
                \Storage::disk($disk)->delete($this->{$attribute_name});
            }

            $this->attributes[$attribute_name] = null;
            return;
        }

        if (request() && array_key_exists('building_secret_form', request()->all())) {
            $mainContact = json_decode(request()->input('building_secret_form'), true);

            if (!in_array($mainContact[0]['contact_type'], Contact::$usersTypeCanHaveImage)) {
                $this->removeUserProfileImage($disk, $attribute_name);
                return;
            }

            if ($mainContact[0]['dateRangeClicked']) {
                $this->removeUserProfileImage($disk, $attribute_name);
                return;
            }

            if (isset($mainContact[0]['schedule']) && strval($mainContact[0]['schedule']) != "[{}]" && strval($mainContact[0]['schedule']) != "[]") {
                $this->removeUserProfileImage($disk, $attribute_name);
                return;
            }
        }

        if (starts_with($value, 'data:image')) {
            $image = \Image::make($value);

            $original_width = $image->width();
            $original_height = $image->height();

            $target_width = 900;
            $target_height = 1600;

            $new_width = $original_width;
            $new_height = (int)($original_width * (16 / 9));

            if ($new_height > $original_height) {
                $new_height = $original_height;
                $new_width = (int)($original_height * (9 / 16));
            }

            $image->crop($new_width, $new_height);

            if ($new_width > $target_width || $new_height > $target_height) {
                $image->resize($target_width, $target_height);
            }

            $filename = md5($value . time()) . '.jpg';

            \Storage::disk($disk)->put($destination_path . '/' . $filename, $image->encode('jpg', 90)->stream());

            $this->attributes[$attribute_name] = trim(config('constants.foxsys_aws') . $destination_path . '/' . $filename);
        }
    }

    public function removeUserProfileImage(string $disk, string $attributeName)
    {
        \Storage::disk($disk)->delete($this->$attributeName);
        $this->attributes[$attributeName] = null;
    }


    //TODO:redirect()->back(); aca esta mal
    public function sendRegisterEmail()
    {
        if (!$this->notified) {
            $this->token = md5(Carbon::now());
            $this->notify(new RegisterUser($this->token));
            $this->notified = true;
            $this->save();
            return redirect()->back();
        }
    }

    public static function sendAsteriskDownMail()
    {

        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            return response(['message' => 'Usuario/s a notificar no encontrado/s'], 500);
        } else {
            try {
                $user->notify(new AsteriskDownNotification());
            } catch (\Mockery\Exception $e) {
                return response(['message' => 'Error inesperado: ' . $e], 500);
            }
        }

        return response(['message' => 'Usuario/s notificado/s!'], 201);
    }


    public static function redirectToRegister($email)
    {
        $mailExists = \App\Models\User\User::mailExists($email);
        $mailDeleted = User::mailDeleted($email);
        if ($mailDeleted) {
            return response()->json(
                [
                    'error' => [
                        'code' => 'Unauthorized',
                        'message' =>
                            'Su usuario se encuentra registrado pero no cumple con los requisitos. Por favor, comuníquese con <NAME_EMAIL> o al 2716 5206'
                    ]
                ],
                401
            );
        } elseif ($mailExists) {
            return \App\Models\User\User::registerUserWithMailAndReturnURL($email);
        } else {
            return \App\Models\User\User::registerUserWithoutMail($email);
        }
    }

    public static function registerUserWithMailAndReturnURL($email)
    {
        $user = User::where('email', '=', $email)->first();

        if (!$user->notified) {
            $user->token = md5(Carbon::now());
            $user->notified = true;
            $user->save();
        }

        return json_encode(['status' => '200', 'msg' => '/app/contact/update/token=' . $user->token]);
    }

    public static function registerUserWithoutMail($email)
    {
        return json_encode(['status' => '200', 'msg' => '/app/contact/register/email=' . $email]);
    }

    public static function mailExists($mail)
    {
        return User::where('email', '=', $mail)->exists();
    }

    public static function mailDeleted($mail)
    {
        return !User::where('email', $mail)->exists()
            && User::withTrashed()->where('email', $mail)->exists();
    }

    public function unreadNotifications()
    {
        return $this->notifications()->take(30)->whereNull('read_at');
    }

    public function allUnreadNotifications()
    {
        return $this->notifications()->whereNull('read_at');
    }


    public function setNameAttribute($name)
    {
        $this->attributes['name'] = str_replace('"', "'", $name);
        $this->attributes['name'] = ucwords(mb_strtolower($this->attributes['name']));
        $this->attributes['complete_name'] = ucwords(mb_strtolower($this->attributes['name']));;
        if (array_key_exists('surname', $this->attributes)) {
            $this->attributes['complete_name'] = ucwords(mb_strtolower($this->attributes['name'])) . ' ' . ucwords(mb_strtolower($this->attributes['surname']));
        }
    }

    public function setSurnameAttribute($name)
    {
        $this->attributes['surname'] = str_replace('"', "'", $name);
        $this->attributes['surname'] = ucwords(mb_strtolower($this->attributes['surname']));
        if ($this->table == 'temporary_contacts') {
            $this->attributes['complete_name'] = ucwords(mb_strtolower($this->attributes['contact_name'])) . ' ' . ucwords(mb_strtolower($this->attributes['surname']));
        } else {
            $this->attributes['complete_name'] = ucwords(mb_strtolower($this->attributes['name'])) . ' ' . ucwords(mb_strtolower($this->attributes['surname']));
        }
    }

    public function getSurnameAttribute()
    {
        if (array_key_exists('surname', $this->attributes)) {
            $sn = $this->attributes['surname'];
            $sn = ucwords($sn);
            return $sn;
        }
    }

    public function getCiAttribute()
    {
        if (array_key_exists('foreign_document', $this->attributes) && array_key_exists('ci', $this->attributes)) {
            return $this->formatDocumentByCi($this->attributes['ci'], $this->attributes['foreign_document']);
        }
        if (array_key_exists('ci', $this->attributes)) {
            return $this->formatDocumentByCi($this->attributes['ci'], false);
        }
    }

    public function getImageAttribute()
    {
        if (isset($this->attributes['image']) && !empty($this->attributes['image'])) {

            if (starts_with($this->attributes['image'], 'http')) {
                return $this->attributes['image'];
            }

            return Str::start($this->attributes['image'], "/");
        }

        return config('constants.image_placeholder');
    }


    public function getAllBuildings()
    {
        return BuildingContact::where('contact_id', $this->id)->get();
    }

    public function getRealPrimaryPhoneAttribute()
    {
        return $this->formatPhone($this->contacts()->value('home_is_primary_phone') ? $this->contacts()->value('phone_home') : $this->contacts()->value('phone_mobile'));
    }

    public function getRealSecondaryPhoneAttribute()
    {
        return $this->formatPhone(!$this->contacts()->value('home_is_primary_phone') ? $this->contacts()->value('phone_home') : $this->contacts()->value('phone_mobile'));
    }
}
