<?php

namespace App\Http\Livewire;

use App\Models\Building;
use Livewire\Component;

class BuildingDoorsCleaning extends Component
{
    public $buildingId;
    public $building;
    public $search;
    public $porters;
    public $cleaningTime;
    public $doorFilter;
    public $cleaningFilter;
    public $showAccordion = false;


    protected $listeners = ['reloadComponentCleaningBuilding', 'changeBuildingResetCleaningAndPorterFilter'];

    public function mount($building)
    {
        $this->setData($building);
    }

    public function setData($building)
    {

        if (is_array($building)) {
            $building = (new Building())->newFromBuilder((object)$building);
        }

        $this->buildingId = $building->id;
        $this->building = $building;
        $this->search = '';

    }

    public function reloadComponentCleaningBuilding($building)
    {
        $this->setData($building);
        $this->changeBuildingResetCleaningAndPorterFilter();
    }

    public function getDoorFilter()
    {
        $this->doorFilter = !$this->doorFilter;
        $this->cleaningFilter = false;
        $this->showAccordion = true;
    }

    public function getCleaningTimeFilter()
    {
        $this->cleaningFilter = !$this->cleaningFilter;
        $this->doorFilter = false;
        $this->showAccordion = true;
    }

    public function setShowAccordion()
    {
        $this->showAccordion = !$this->showAccordion;
    }

    public function getPorters()
    {
        $this->porters = !$this->search ?
            json_decode($this->building->porter, true) ?? []
            : array_filter(json_decode($this->building->porter, true) ?? [], function ($porter) {
                return str_contains(strtolower($porter['name']), strtolower($this->search));
            });
    }

    public function getCleaningTime()
    {
        $this->cleaningTime = !$this->search ?
            json_decode($this->building->cleaning_time, true) ?? []
            : array_filter(json_decode($this->building->cleaning_time, true) ?? [], function ($cleaner) {
                return str_contains(strtolower($cleaner['name']), strtolower($this->search));
            });
    }

    public function changeBuildingResetCleaningAndPorterFilter()
    {
        $this->showAccordion = false;
        $this->doorFilter = false;
        $this->cleaningFilter = false;
    }

    public function render()
    {
        return view('livewire.building-doors-cleaning', [
            'porters' => $this->getPorters(),
            'cleaningTime' => $this->getCleaningTime(),
        ]);
    }
}
