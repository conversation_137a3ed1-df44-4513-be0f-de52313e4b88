<?php

namespace App\Http\Livewire;

use App\Models\RecentlyInteraction;
use Livewire\Component;

class ContactInteractionLivewire extends Component
{
    protected $listeners = ['showModalInteractions' => 'updateModalState', 'showModalInteractionsOtherPhones' => 'updateModalStateOtherPhones', 'reloadComponentPopUpInteraction' => 'updateBuilding'];

    public $building;
    public $showModal = false;
    public $contact = null;
    public $interactionComment = 'Autoriza';
    public $selectedComment = 'Autoriza';
    public $availableOptions = [
        'Autoriza',
        'No autoriza',
        'Baja residente',
        'No atiende',
    ];


    public function updateBuilding($building)
    {
        $this->building = $building;
    }

    public function updateComment($comment)
    {
        $this->selectedComment = $comment;

        $currentText = $this->interactionComment;

        $prefixes = $this->availableOptions;

        $additionalText = $currentText;

        foreach ($prefixes as $prefix) {
            if (strpos($currentText, $prefix) === 0) {
                $additionalText = trim(substr($currentText, strlen($prefix)));
                break;
            }
        }
        if (empty($additionalText) || $additionalText === $comment) {
            $this->interactionComment = $comment;
        } else {
            $this->interactionComment = $comment . ' ' . $additionalText;
        }
    }

    public function updateLastInteraction()
    {
        $this->showModal = false;
        $this->createInteractionCallFromContactPopup($this->contact['id'], $this->contact['phone_mobile'], $this->interactionComment);

    }


    public function updateModalState($contact, $call = false, $comment = 'No hay descripción')
    {
        $this->showModal = true;
        $this->contact = $contact;


        if (empty($this->interactionComment) || $this->interactionComment === 'No hay descripción') {
            $this->interactionComment = 'Autoriza';
            $this->selectedComment = 'Autoriza';
        }

        if ($call) {
            $this->createInteractionCall($contact['id'], $contact['phone_mobile'], $comment);
        }
    }

    public function updateModalStateOtherPhones($contact, $phone, $call = false, $comment = 'No hay descripción')
    {
        $this->showModal = true;
        $this->contact = $contact;


        if (empty($this->interactionComment) || $this->interactionComment === 'No hay descripción') {
            $this->interactionComment = 'Autoriza';
            $this->selectedComment = 'Autoriza';
        }

        if ($call) {
            $this->createInteractionCall($contact['id'], $phone, $comment);
        }
    }

    public function render()
    {
        return view('livewire.contact-interaction-livewire');
    }

    public function createInteractionCall($id, $phoneMobile, $comment)
    {
        $this->dispatchBrowserEvent('createInteractionCall', [
            'id' => $id,
            'phone_mobile' => $phoneMobile,
            'comment' => $comment
        ]);
    }


    public function createInteractionCallFromContactPopup($id, $phoneMobile, $comment)
    {
        RecentlyInteraction::query()
            ->where('caller_id', $id)
            ->orWhere('receiver_id', $id)
            ->latest('id')
            ->first()
            ->update([
                'caller_id' => backpack_user()->id,
                'comment' => $comment,
            ]);
    }
}
