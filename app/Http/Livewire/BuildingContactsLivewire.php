<?php

namespace App\Http\Livewire;

use App\Helpers\Paginate;
use App\Models\BuildingsRelationships\BuildingContact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use App\Models\Building;
use App\Http\Resources\BuildingContactResource;
use Illuminate\Support\Facades\Http;
use function Clue\StreamFilter\fun;
use Illuminate\Support\Facades\DB;

class BuildingContactsLivewire extends Component
{
    protected $listeners = [
        'handleFlatSelectInput' => 'handleFlatSelectInput',
        'caseCreated' => 'updateCountActivePackages',
        'caseUpdated' => 'updateCountActivePackages',
        'handleLoadNextPage',
        'handleChangeBuilding',
        'handleTextSearchInput'
    ];
    public $activeTab = 'allContact';
    public $buildingId;
    public $contacts = [];
    public $totalResults = [];
    public $sortDirection = 'desc';
    protected $queryBuilder;
    public $sortColumn = null;
    public $searchContactByCompleteNameInput = '';
    public $searchContactByFlatInput = '';
    public $phoneTitle = '';
    public $announcementsToShowModal = '';
    public $flats = [];
    public $building;
    public $contactPackages = [];
    public $contactNoneAutorizedPackages = ['Autorizado', 'Oficina', 'Prohibido', 'Ingreso', 'Empleado', 'Proveedor', 'Prohibido Ingreso'];
    public $contactsWithPackages = [];
    public $page = 1;
    public $contactPerRequest = 20;
    public $totalPages = 0;
    public $hasNextPage = true;
    public $orderAllContacts = false;
    public $delateIconSearch = false;
    public $isLoading = true;


    public function render()
    {
        return view('livewire.building-contacts', [
            'contacts' => $this->contacts,
            'flats' => $this->flats,
        ]);
    }

    public function setData($building)
    {

        $this->buildingId = $building->id;
        $this->building = $building;
        $this->activeTab = 'allContact';
        $this->makeNewSearch();
        $this->loadFlatsSelect();
        $this->loadContactPackages();

        $this->isLoading = false;

    }

    public function mount($building)
    {

        $this->setData($building);
    }


    public function loadFlatsSelect()
    {
        $this->flats = $this->building->flats()->pluck('number_with_tower', 'id');
    }

    public function handleChangeBuilding($id)
    {
        $this->building = Building::find($id);


        $this->isLoading = true;

        $this->dispatchBrowserEvent('scrollContainer');

        $this->resetInputFilters();

        $this->resetPaginate();


        $this->setData($this->building);

        $this->emit('getCasesFromContact', "", "");

    }

    public function handleLoadNextPage()
    {
        if (!$this->hasNextPage || $this->isLoading) {
            return;
        }

        $this->isLoading = true;

        $this->page++;

        $currentResults = $this->contacts;

        $this->makeSearch();

        $this->contacts = array_merge($currentResults, $this->contacts);

        $this->totalResults = $this->contacts;

        $this->loadContactPackages();

        $this->isLoading = false;


    }


    public function handleFilterTab($column)
    {
        if ($column == 'allContact') {
            $this->resetInputFilters();
            $this->delateIconSearch = false;
        }
        $this->activeTab = $column;
        $this->resetPaginate();
        $this->makeSearch();
    }


    public function handleOrderFlats()
    {
        $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortColumn = 'flats';
        $this->resetPaginate();
        $this->makeSearch();
    }

    public function handleFlatSelectInput($value)
    {
        $this->searchContactByFlatInput = $value;
        $this->makeNewSearch();

        $this->emit('getCasesFromContact', $this->searchContactByCompleteNameInput, $this->searchContactByFlatInput);

    }

    public function handleTextSearchInput($value)
    {
        $this->searchContactByCompleteNameInput = $value;
        $this->makeNewSearch();

        $this->emit('getCasesFromContact', $this->searchContactByCompleteNameInput, $this->searchContactByFlatInput);
        $this->delateIconSearch();

    }

    public function delateIconSearch()
    {
        $this->searchContactByCompleteNameInput == ''
            ? $this->delateIconSearch = false
            : $this->delateIconSearch = true;;
    }

    public function clearSearchIcon()
    {
        $this->delateIconSearch = false;
        $this->emit('clearSearchIcon');
    }


    public function makeSearch()
    {
        $this->queryBuilder = BuildingContact::query()
            ->with('casesAnnouncementFromFlat')
            ->without('mainBuilding')
            ->where('building_id', $this->buildingId)
            ->whereHas('contact', function (Builder $query) {
                $query->whereNull('needs_verification');
            });

        $this->setTypeFilter();
        $this->setFlatFilter();
        $this->setTextFilter();
        $this->setOrder();
        $this->setPaginate();

        $contactResult = $this->queryBuilder->get();
        $this->checkNextPage($contactResult);
        $this->transformContacts($contactResult);
        $this->postFiltering();

        $this->loadContactPackages();
    }

    private function checkNextPage($contactResult)
    {
        if ($contactResult->count() < $this->contactPerRequest) {
            $this->hasNextPage = false;
            $this->dispatchBrowserEvent('noMoreData');
        } else {
            $this->hasNextPage = true;
        }
    }

    private function setFlatFilter()
    {
        $this->queryBuilder->when($this->searchContactByFlatInput, function ($q) {
            $q->where('flat_id', $this->searchContactByFlatInput);
        });
    }

    private function resetPaginate()
    {
        $this->page = 1;
        $this->totalResults = [];
        $this->contacts = [];
        $this->hasNextPage = true;

        $this->dispatchBrowserEvent('scrollToTop');
        $this->dispatchBrowserEvent('resetNoMoreData');
        $this->emit('resetPagination');
    }

    private function setTextFilter()
    {
        $searchInput = $this->searchContactByCompleteNameInput;

        if (empty($searchInput)) {
            return;
        }

        if ($this->isNumericSearch($this->searchContactByCompleteNameInput)) {
            $normalizedInput = $this->normalizeNumericInput($searchInput);
            $this->applyNumericSearch($this->queryBuilder, $searchInput, $normalizedInput);
        } else {
            $this->applyTextSearch($this->queryBuilder, $searchInput);
        }
    }


    private function setPaginate()
    {
        $this->queryBuilder->limit($this->contactPerRequest)
            ->offset(($this->page - 1) * $this->contactPerRequest);
    }

    private function setOrder()
    {
        if ($this->sortColumn == null) {
            $this->setdefaultOrder();
        };

        if ($this->sortColumn == 'flats') {
            $this->setSortFlat();
        }

    }

    private function setSortFlat()
    {
        $this->queryBuilder
            ->orderBy('tower_denomination', $this->sortDirection)
            ->orderByRaw("
                CASE
                    WHEN REPLACE(TRIM(flat_number), ' ', '') REGEXP '^[0-9]+$' THEN 1
                    WHEN REPLACE(TRIM(flat_number), ' ', '') REGEXP '^[0-9]+[A-Za-z]' THEN 2
                    WHEN REPLACE(TRIM(flat_number), ' ', '') REGEXP '^[A-Za-z]+[0-9]' THEN 3
                    WHEN REPLACE(TRIM(flat_number), ' ', '') REGEXP '^[A-Za-z]' and flat_number = 'Edificio' THEN 4
                    ELSE 5
                END {$this->sortDirection}
            ")
            ->orderByRaw("cast(REGEXP_REPLACE(flat_number, '[^0-9]', '') as unsigned) {$this->sortDirection}")
            ->orderBy('flat_number', $this->sortDirection);
    }


    private function setDefaultOrder()
    {
        if ($this->building->hasTower()) {
            $this->queryBuilder->sortContactsWithTower();
        } else {
            $this->queryBuilder->sortContactsWithoutTower();
        }
    }

    private function setTypeFilter()
    {
        match ($this->activeTab) {
            "temporal" => $this->setTemporaryContactsFilter(),
            "packages" => $this->setPackagesFilter(),
            "comision" => $this->setComisionFilter(),
            default => collect([]),
        };

    }

    private function setComisionFilter()
    {
        $this->queryBuilder->whereIn('contact_type', [
            'Contacto Principal - Comisión',
            'Contacto Principal',
            'Comisión'
        ]);
    }

    private function setPackagesFilter()
    {
        $this->queryBuilder->whereHas('casePackages');
    }

    private function setTemporaryContactsFilter()
    {

        $this->queryBuilder
            ->where('contact_type', 'Autorizado')
            ->whereNotNull('start_date')
            ->whereNotNull('end_date');

    }

    private function transformContacts($listContacts)
    {
        $listContacts->each(function ($contact) {
            $contact->append('real_primary_phone');
        });

        $this->contacts = $listContacts->map(fn($contact) => new BuildingContactResource($contact))->toArray(request());
    }

    private function postFiltering()
    {
        $this->contacts = $this->filterAuthorizedContacts($this->contacts);
    }

    private function filterAuthorizedContacts($contacts)
    {
        return collect($contacts)->filter(fn($contact) => $this->building->isUserAvailable($contact['schedule'], $contact['start_date'], $contact['end_date'])
        )->values()->all();
    }


    public function getPackagesInformation(): void
    {
        $this->contactsWithPackages = collect($this->contacts)->mapWithKeys(function ($contact) {
            $isAuthorized = $this->contactHasAutorizedPackage($contact['contact_type']);
            $hasPackages = $isAuthorized && $this->hasPackages($contact['contact_id']);
            $packageCount = $hasPackages ? $this->countPackages($contact['contact_id']) : 0;
            return [
                $contact['contact_id'] => [
                    'isAuthorized' => $isAuthorized,
                    'hasPackages' => $hasPackages,
                    'packageCount' => $packageCount,
                ],
            ];
        })->toArray();
    }


    private function applyNumericSearch($query, string $searchInput, string $normalizedInput)
    {
        $query->where(function ($subQuery) use ($searchInput, $normalizedInput) {
            if (strlen($normalizedInput) >= 4) {
                $subQuery->orWhere(DB::raw("REPLACE(REPLACE(ci, '.', ''), '-', '')"), 'LIKE', '%' . $normalizedInput . '%')
                    ->orWhere('phone_mobile', 'LIKE', '%' . $searchInput . '%')
                    ->orWhere('phone_home', 'LIKE', '%' . $searchInput . '%')
                    ->orWhere('security_word', 'LIKE', '%' . $searchInput . '%');
            }

            $subQuery->orWhereHas('flat', function ($relationQuery) use ($searchInput) {
                $relationQuery->where('number_with_tower', 'LIKE', '%' . $searchInput . '%');
            });
        });
    }

    private function applyTextSearch($query, string $searchInput)
    {
        $searchTerm = '%' . $searchInput . '%';

        $query->where(function ($q) use ($searchTerm) {
            $q->where('complete_name', 'LIKE', $searchTerm)
                ->orWhere('security_word', 'LIKE', $searchTerm)
                ->orWhere(DB::raw("REPLACE(REPLACE(ci, '.', ''), '-', '')"), 'LIKE', $searchTerm)
                ->orWhereHas('flat', function ($subQuery) use ($searchTerm) {
                    $subQuery->where('number_with_tower', 'LIKE', $searchTerm);
                });
        });
    }

    private function normalizeNumericInput(string $input): string
    {
        return preg_replace('/[^0-9]/', '', $input);
    }


    private function isNumericSearch(string $input): bool
    {
        return ctype_digit($input);
    }


    public function resetInputFilters()
    {
        $this->searchContactByCompleteNameInput = '';
        $this->searchContactByFlatInput = '';

        $this->activeTab = 'allContact';
        $this->sortDirection = 'desc';
        $this->sortColumn = null;

    }


    public function makeNewSearch()
    {
        $this->resetPaginate();
        $this->makeSearch();
    }


    public function contactHasMorePhone($contact): bool
    {
        $hasPhoneMobile = $contact['phone_mobile'] > 0;
        $hasPhoneHome = $contact['phone_home'] > 0;
        $hasOthersMobilPhones = $contact['others_mobil_phones'] > 0;
        $hasMoreThanOnePhone = ($hasPhoneMobile ? 1 : 0) + ($hasPhoneHome ? 1 : 0) + ($hasOthersMobilPhones ? 1 : 0);
        return $hasMoreThanOnePhone > 1 ? $this->formatPhoneToShowTitle($contact, $hasPhoneMobile, $hasPhoneHome, $hasOthersMobilPhones) : false;
    }

    public function contactHasAnnouncements($contact): bool
    {
        $announcements = json_decode($contact['announcements'], true) ?? [];
        if (count($announcements) > 0) {
            return $this->formatAnnouncementsToShow($contact);
        }
        return false;
    }

    public function formatAnnouncementsToShow($contact): string
    {
        if (!is_array($contact['announcements'])) {
            return 'No hay anuncios disponibles.';
        }

        $announcements = array_map(function ($announcement) {
            return trim($announcement);
        }, $contact['announcements']);

        $this->announcementsToShowModal = implode("\n\n", $announcements);
        return $this->announcementsToShowModal;
    }


    public function formatPhoneToShowTitle($contact, $hasPhoneMobile, $hasPhoneHome, $hasOthersMobilPhones): string
    {
        $phone = '';

        $principalPhone = $contact['real_primary_phone'] ?? '';

        if ($hasPhoneMobile && $contact['phone_mobile'] !== $principalPhone) {
            $phone .= $contact['phone_mobile'] . "\n";
        }

        if ($hasPhoneHome && $contact['phone_home'] !== $principalPhone) {
            $phone .= $contact['phone_home'] . "\n";
        }

        if ($hasOthersMobilPhones) {
            $phone .= implode("\n", $contact['others_mobil_phones']);
        }

        return $this->phoneTitle = $phone;
    }


    public function showModalInteractions($contact, $call = false)
    {
        $this->emit('showModalInteractions', $contact, $call);
    }

    public function showModalPackage($contact)
    {
        $this->emit('showModalPackage', $contact);
    }


    public function loadContactPackages(): array
    {
        $this->contactPackages = [];

        foreach ($this->building->casesOpenPackages()->get() as $case) {
            $this->contactPackages[$case->user_id] = $this->contactPackages[$case->user_id] ?? 0;
            $this->contactPackages[$case->user_id]++;
        }

        $this->getPackagesInformation();

        return $this->contactPackages;
    }


    public function hasPackages($contactId): bool
    {
        return isset($this->contactPackages[$contactId]) && $this->contactPackages[$contactId] > 0;
    }

    public function countPackages($contactId)
    {
        return $this->contactPackages[$contactId] ?? 0;
    }

    public function contactHasAutorizedPackage($contactType): bool
    {
        return !in_array($contactType, $this->contactNoneAutorizedPackages);
    }

    public function setIconContactType($contactType)
    {
        return Building::$iconsContactTypes[$contactType] ?? '';
    }


    public function changeColorRedContactNotAllowed($contactType)
    {
        return $contactType == 'Prohibido Ingreso' ? 'color-red' : '';
    }

    public function formatPhoneNumber($number)
    {
        return $number;
    }

    public function updateCountActivePackages()
    {
        $this->loadContactPackages();
    }


}
