<?php

namespace App\Http\Livewire;

use App\Models\Flat;
use App\Models\User\Contact;
use Livewire\Component;
use App\Models\Caseq;
use App\Models\Building;
use App\Models\Category;
use mysql_xdevapi\Collection;

class BuildingPackageLivewire extends Component
{
    public bool $showModalPackage = false;
    public bool $showModalActivePackage = false;
    public array $activePackages = [];
    public string $title;
    public string $description;
    public string $placeholder;
    public int $category = 101;
    public array $contact;
    public int $buildingId;
    public int $flatId;
    public array $stateActiveCases = [];
    public string $stateInProgress = 'en_curso';

    protected $listeners = [
        'showModalPackage' => 'openModal'
    ];

    protected array $rules = [
        'category' => 'required|in:101,102',
        'title' => 'required',
        'description' => 'required'
    ];

    public function mount(): void
    {
        $this->resetFields();
    }

    public function render(): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        return view('livewire.building-package');
    }

    public function openModal($contact): void
    {
        $this->contact = $contact;
        $this->flatId = $contact['flat_id'];
        $this->buildingId = $contact['building_id'];
        $building = Building::find($this->buildingId);
        $contactPackages = $building->casesOpenPackages->where('user_id', $contact['id']);
        $contactHasPackages = $contactPackages->count();
        $this->activePackages = $contactPackages->toArray();
        $contactHasPackages > 0 ? $this->showModalWithCurrentPackage() : $this->createNewCasePackage();

    }

    public function createNewCasePackage(): void
    {
        $this->titleCase();
        $this->showModalPackage = true;
        $this->showModalActivePackage = false;
    }

    public function showModalWithCurrentPackage(): void
    {
        $this->showModalActivePackage = true;
        $this->setRadioButtonActiveCaseModal();
    }

    public function setRadioButtonActiveCaseModal(): void
    {
        foreach ($this->activePackages as $package) {
            $this->stateActiveCases[$package['id']] = $package['state'];
        }
    }

    public function closeModal(): void
    {
        $this->showModalPackage = false;
        $this->showModalActivePackage = false;
        $this->resetFields();
    }

    public function titleCase(): void
    {
        switch ($this->category) {
            case '101':
                $this->title = 'Recepción - ' . $this->contact['complete_name'];
                $this->description = "Proviene de: \nQuedó en: \nOtros comentarios: ";
                $this->placeholder = 'Ingrese de qué empresa proviene el paquete y en dónde quedó.';
                break;
            case '102':
                $this->title = 'Entrega - ' . $this->contact['complete_name'];
                $this->description = "Quién retira: \nDe dónde retira:";
                $this->placeholder = 'Se confirmó la recepción del paquete para Adriana Bonomi. Está en el hall.';
                break;
        }
    }

    public function savePackageCase(): void
    {
        $flat = Flat::query()->find($this->flatId);
        try {
            $this->validate();
            $case = Caseq::create([
                'title' => $this->title,
                'description' => $this->description,
                'last_category_id' => $this->category,
                'building_id' => $this->buildingId,
                'flat_id' => $this->flatId,
                'created_by' => backpack_user()->id,
                'state' => $this->stateInProgress,
                'priority' => 'MEDIA',
                'user_id' => $this->contact['id'],
                'area_id' => 11, // foxsys
                'last_category_name' => Category::find($this->category)->name,
                'flat_number' => $flat['number'],
                'tower_denomination' => $flat['tower_denomination'],
                'number_flat' => $flat['number'],
            ]);

            $this->sendWittyNotification($case);

            $this->dispatchBrowserEvent('alert', [
                'type' => 'success',
                'message' => '¡Caso creado exitosamente!'
            ]);

            $this->closeModal();
            $this->resetFields();
            $this->emit('caseCreated', $case->user_id);

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->dispatchBrowserEvent('alert', [
                'type' => 'error',
                'message' => 'Por favor, complete los campos requeridos.'
            ]);
        } catch (\Exception $e) {
            $this->closeModal();
            $this->resetFields();

            $this->dispatchBrowserEvent('alert', [
                'type' => 'error',
                'message' => 'Ocurrió un error inesperado. Por favor, inténtelo nuevamente.'
            ]);
        }
    }

    private function resetFields(): void
    {
        $this->title = '';
        $this->description = '';
        $this->category = '101';
        $this->contact = [];
        $this->buildingId = 0;
        $this->flatId = 0;
    }

    private function sendWittyNotification($case): void
    {
        Caseq::sendNotificationTracingWittyBots($case);
    }

    public function setNewStateCase(): void
    {

        try {
            foreach ($this->stateActiveCases as $caseId => $state) {
                $case = Caseq::find($caseId);
                if ($case->state !== $state) {
                    $case->state = $state;
                    $case->save();
                    $this->sendWittyNotification($case);
                }
            }

            $this->activePackages = collect($this->activePackages)
                ->filter(fn($package) => $this->stateActiveCases[$package['id']] !== $this->stateInProgress)
                ->toArray();

            if (empty($this->activePackages)) {
                $this->closeModal();
            }

            $this->emit('caseUpdated');

            $this->dispatchBrowserEvent('alert', [
                'type' => 'success',
                'message' => '¡Estado del caso actualizado exitosamente!'
            ]);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('alert', [
                'type' => 'error',
                'message' => 'Ocurrió un error al actualizar los estados. Por favor, inténtelo nuevamente.'
            ]);
        }
    }


}
