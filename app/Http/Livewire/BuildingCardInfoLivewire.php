<?php

namespace App\Http\Livewire;

use App\Models\Building;
use App\Models\BuildingIntercom;
use App\Models\Caseq;
use App\Models\User\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class BuildingCardInfoLivewire extends Component
{
    public $building;
    public $activeTab = 'people';
    public $selectAllBuildingsData;
    public $announcements;
    public $hallComments, $upComments, $hallStatus, $upStatus, $hallIcon, $upIcon, $hallIconColor, $upIconColor;
    public $address, $street, $between, $towers, $videogarage, $serviceSchedule, $multipleAddressNoTower, $multipleAddressBetweenNoTower;
    public $records, $tags, $investigations, $others;
    public $dateToShow, $isValid, $operator, $intercoms;
    public $totalFlats, $totalContacts, $aditionsInstructions, $building_flats_list;
    protected $listeners = ['changeTab' => 'setActiveTab', 'changeBuildingReloadAllComponent', 'loadAllComponents'];


    public function mount($building)
    {
        $this->building = $building;

        $this->selectAllBuildingsData = Building::query()->get(['id', 'name', 'building_number']);

        $this->dispatchBrowserEvent('currentTab', $this->activeTab);

        $this->loadAllComponents();


    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->dispatchBrowserEvent('reloadTab');
    }

    public function render()
    {
        return view('livewire.building-card-info-livewire', [
            'building' => $this->building,
            'announcements' => $this->announcements,
            'activeTab' => $this->activeTab,
        ]);
    }

    public function changeBuildingReloadAllComponent($id)
    {

        $this->building = Building::query()->findOrFail($id);

        $componentEvents = [
            'building-edit-button' => 'reloadComponentBuildingEditButton',
            'building-case' => 'reloadComponentCasesBuilding',
            'building-service' => 'reloadComponentServicesBuilding',
            'building-doors-cleaning' => 'reloadComponentCleaningBuilding',
            'contact-interaction-livewire' => 'reloadComponentPopUpInteraction',
        ];

        foreach ($componentEvents as $component => $event) {
            $this->emitTo($component, $event, $this->building);
        }

        $this->emit('loadAllComponents');

    }

    public function loadAnnouncements()
    {

        $this->announcements = $this->building->casesAnnouncement()
            ->whereHas('flat', function (Builder $query) {
                $query->where('number_with_tower', 'like', '%Edificio%');
            })
            ->latest()
            ->get();
    }

    public function loadCadetsInfo()
    {
        $this->hallComments = json_decode($this->building->cadete_hall_comments, true) ?? [];
        $this->upComments = json_decode($this->building->cadete_up_comments, true) ?? [];
        $this->hallStatus = match ($this->building->cadete_hall) {
            0 => 'ko',
            1 => 'ok',
            default => 'exception',
        };
        $this->upStatus = match ($this->building->cadete_up) {
            0 => 'ko',
            1 => 'ok',
            default => 'exception',
        };
        $this->hallIcon = match ($this->building->cadete_hall) {
            0 => 'close',
            1 => 'checkmark',
            default => 'help',
        };
        $this->upIcon = match ($this->building->cadete_up) {
            0 => 'close',
            1 => 'checkmark',
            default => 'help',
        };
        $this->hallIconColor = match ($this->building->cadete_hall) {
            0 => 'cadete-icon-close',
            1 => 'cadete-icon-checkmark',
            default => 'cadete-icon-help',
        };
        $this->upIconColor = match ($this->building->cadete_up) {
            0 => 'cadete-icon-close',
            1 => 'cadete-icon-checkmark',
            default => 'cadete-icon-help',
        };
    }

    public function loadDoorsAvailable()
    {
        $this->multipleAddressNoTower = [];
        $this->multipleAddressBetweenNoTower = [];
        $this->address = json_decode($this->building->address, true) ?? [];
        $currentStreet = isset($this->address[0]['street']) ? $this->address[0]['street'] : '';
        $currentNumber = isset($this->address[0]['between_streets']) ? $this->address[0]['number'] ?? '' : '';
        $this->street = $currentStreet . ' ' . $currentNumber;
        $this->between = isset($this->address[0]['between_streets']) ? 'esq ' . $this->address[0]['between_streets'] ?? '' : '';
        $this->towers = $this->building->towers()->get();
        $intercoms = $this->building->intercoms()->get()->filter(function (BuildingIntercom $intercom) {
            return strtolower($intercom->intercom_service) == 'garage';
        });
        $this->videogarage = count($intercoms) >= 1;
        $this->serviceSchedule = json_decode($this->building->schedule, true) ?? [];
        if (count($this->towers) == 0 && count($this->address) > 1) {
            foreach ($this->address as $item) {
                $this->multipleAddressNoTower[] = $item['street'] . ' ' . $item['number'];
                $this->multipleAddressBetweenNoTower[] = isset($item['between_streets']) ? $item['between_streets'] : '';
            }
        }
    }

    public function loadFeaturesATC()
    {
        $this->records = json_decode($this->building->records_comments, true) ?? [];
        $this->tags = json_decode($this->building->tags_comments, true) ?? [];
        $this->investigations = json_decode($this->building->investigations_comments, true) ?? [];
        $this->others = json_decode($this->building->others_comments, true) ?? [];
    }

    public function loadEquipment()
    {

        $date = Carbon::parse($this->building->service_start_date);
        $yearsToAdd = $this->building->building_number <= 219 ? 3 : 2;
        $newDate = $date->addYears($yearsToAdd);
        $this->dateToShow = Carbon::parse($newDate)->format('d/m/y');
        $this->isValid = today() < $newDate;
        $this->operator = User::query()->find($this->building->operator_id)?->complete_name;
        $this->intercoms = json_encode($this->building->intercoms()->get());
    }

    public function loadInformation()
    {
        $this->totalFlats = $this->building->flats()->count();
        $this->totalContacts = $this->building->contacts()->count();
        $this->aditionsInstructions = json_decode($this->building->aditional_instructions_comments, true) ?? [];
        $building_flats_array = $this->building->flats()->pluck('number_with_tower')->toArray();
        sort($building_flats_array);
        $this->building_flats_list = implode(',', $building_flats_array);
    }

    public function loadAllComponents()
    {
        $this->loadAnnouncements();
        $this->setActiveTab('people');
        $this->loadCadetsInfo();
        $this->loadDoorsAvailable();
        $this->loadFeaturesATC();
        $this->loadEquipment();
        $this->loadInformation();


    }

}
