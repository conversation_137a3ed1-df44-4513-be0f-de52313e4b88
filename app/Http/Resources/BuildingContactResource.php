<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BuildingContactResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this['contact_id'],
            'contact_id' => $this['contact_id'],
            'building_contact_id' => $this['id'],
            'complete_name' => $this['complete_name'],
            'building_id' => $this['building_id'],
            'building_name' => $this['building_name'],
            'flat_id' => $this['flat_id'],
            'tower_id' => $this['tower_id'],
            'ci' => $this['ci'],
            'security_word' => $this['security_word'],
            'phone_mobile' => $this['phone_mobile'],
            'phone_home' => $this['phone_home'],
            'others_mobil_phones' => $this['others_mobil_phones'],
            'contact_type' => $this['contact_type'],
            'principal_phone' => $this['real_primary_phone'],
            'image' => $this['image'],
            'dont_call' => $this['dont_call'],
            'flat_number' => $this['flat_number'],
            'owner_or_tenant' => $this['owner_or_tenant'],
            'referrer' => $this['referrer'],
            'email' => $this['email'],
            'description' => is_null($this['description']) || strlen(trim($this['description'])) == 0 ? '' : $this['description'],
            'notes_2' => $this['notes_2'],
            'notes_3' => $this['notes_3'],
            'cases_open' => $this['cases_open'],
            'announcements' => $this['announcements'],
            'temporary_contact' => $this['temporary_contact'],
            'start_date' => $this['start_date'],
            'end_date' => $this['end_date'],
            'schedule' => $this['schedule'],
            'intercom_incoming_call' => $this['intercom_incoming_call'],
            'intercom_name' => $this['intercom_name'],
            'type_of_contact_in_building' => $this['type_of_contact_in_building'],
            "access_code" => $this['code'] ?? "",
            "access_code_temporal" => $this['access_code_temporal_2'],
            "address" => $this['address'],
            "address_flat_number" => $this['address_flat_number'],
            "contact_email" => $this['contact_email'],
            "contact_email_atc" => $this['contact_email_atc'],
            "dateRangeClicked" => $this['start_date']  || $this['end_date'] ? 1 : 0,
            "door_number" => $this['door_number'],
            "empty_flat" => $this['empty_flat'] ? 1 : 0,
        ];
    }
}
