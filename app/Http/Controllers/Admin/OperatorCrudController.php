<?php

namespace App\Http\Controllers\Admin;


use App\Http\Fields\OperatorFields;
use App\Http\Requests\ContactStoreRequest;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Models\User\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\ReviseOperation\ReviseOperationCustom;

/**
 * Class ContactCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class OperatorCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;

    public function setup()
    {
        $this->crud->setModel('App\Models\User\Operator');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/operator');
        $this->crud->setEntityNameStrings('operador', 'operadores');
        $this->crud->denyAccess('show');
        if (!backpack_user()->hasRole('Admin')) {
            if (backpack_user()->hasRole('Team Leader') || backpack_user()->hasRole('Supervisión')) {

                $this->crud->denyAccess(['reorder', 'delete', 'revise']);
            } else {
                $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revisions']);
            }
        }


    }

    protected function setupListOperation()
    {
        $fields = new OperatorFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());



    }


    protected function setupCreateOperation()
    {
        $this->crud->setValidation(ContactStoreRequest::class);

        $fields = new OperatorFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());

    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }


    public function store(UserStoreRequest $request)
    {
        $user = new User();

        $user->email = $request->email;
        $user->fill($request->all());
        $user->created_by = backpack_user()->id;
        $user->roles()->attach('1'); //pasar a config. operador
        $user->is_admin = true;

        $user->formatUser();

        $user->save();

        return redirect('admin/operator');

    }

    public function update(UserUpdateRequest $request, $id)
    {
        $user = User::findOrFail($id);

        $user->fillAttributes($request);
        $user->updated_by = backpack_user()->id;
        $user->roles()->sync(json_decode($request->roles, true));

        $user->formatUser();

        $user->save();

        return redirect('admin/operator');

    }


}
