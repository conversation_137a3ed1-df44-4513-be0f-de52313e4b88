<?php

namespace App\Http\Controllers\Admin;

use Alert;
use App\Http\Fields\ContactFields;
use App\Http\Requests\ContactStoreRequest;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\BuildingContactResource;
use App\Http\Resources\ServiceResource;
use App\Jobs\UpdateDeviceKazooAfterRestoreContactJob;
use App\Models\AccessCode;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Flat;
use App\Models\KazooFlatsConsecutive;
use App\Models\Log;
use App\Models\RecentlyInteraction;
use App\Models\TemporaryContact;
use App\Models\User\Contact;
use App\Notifications\RegisterUser;
use App\Notifications\UserVerified;
use App\Services\KazooService;
use App\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Carbon\Carbon;
use Hash;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use PHPUnit\Exception;
use Tymon\JWTAuth\Contracts\JWTSubject;

use Error;
use Widget;


/**
 * Class ContactCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class ContactCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;
    use \App\Traits\CustomRevisionsTrait {
        \App\Traits\CustomRevisionsTrait::listRevisions insteadof \App\Helpers\Revise\ReviseOperationCustom;
    }

    public function setup()
    {
        $this->crud->setModel('App\Models\User\Contact');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/contact');
        $this->crud->setEntityNameStrings('contacto', 'contactos');
        $this->crud->allowAccess('show');

        // Custom button for edit contact
        $this->crud->addButtonFromView('line', 'update_contact', 'update_contact');
        $this->crud->addButtonFromView('line', 'delete_contact', 'delete_contact');

        $this->crud->addClause('verification');
        $this->crud->addClause('with', 'building');
        $this->crud->addClause('with', 'flat');
        $this->crud->addClause('with', 'buildings');
        $this->crud->addClause('with', 'flats');
        if (backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Representante Atención al Cliente'])) {
            $this->crud->enableExportButtons();
        }


        $this->checkPermissions();

        // Configurar revisiones personalizadas
        $this->setupReviseDefaults();
    }



    protected function checkPermissions()
    {
        if (backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Representante Atención al Cliente'])) {
            $this->crud->enableExportButtons();
        }

        if (!backpack_user()->hasRole(['Admin', 'Jefe Atención al Cliente', 'Representante Atención al Cliente'])) {

            if (backpack_user()->hasRole(['Team Leader', 'Jefe Operaciones'])) {
                $this->crud->denyAccess(['reorder', 'delete', 'revise']);
            } else {
                $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revise']);
            }

        }


        if (backpack_user()->hasRole('Cadete')) {
            $this->crud->denyAccess(['reorder', 'revise']);
            $this->crud->allowAccess('create');
        }

        if (backpack_user()->hasPermissionTo('No crear Contacto')) {
            $this->crud->denyAccess(['create']);
        }

        if (backpack_user()->hasPermissionTo('Editar Contactos Temporales | Monitoreo')) {
            $this->crud->allowAccess(['update']);
        }
    }

    protected function setupListOperation()
    {
        $this->crud->query->select(
            'users.*',
            DB::raw('concat(buildings.building_number, " - ", buildings.name) as full_flats_buildings_text'),
            'building_contact.flat_id as building_flat_id',
            'building_contact.id as relation_id',
            'building_contact.deleter_user_id',
            'building_contact.deleted_at',
            'flats.number_with_tower',
            'buildings.building_number'
        )
            ->leftjoin('building_contact', 'users.id', '=', 'building_contact.contact_id')
            ->leftjoin('flats', 'flats.id', '=', 'building_contact.flat_id')
            ->leftjoin('buildings', 'buildings.id', '=', 'building_contact.building_id')
            ->where('building_contact.deleted_at', '=', null)
            ->where('users.type', 'contact');
        $this->crud->removeButtons(['show', 'update', 'delete']);
        $fields = new ContactFields();
        $this->crud->addColumns($fields->getColumnsFields());

        if (!backpack_user()->hasRole('Cadete')) {

            $this->crud->addFilter(
                [ // simple filter
                    'type' => 'simple',
                    'name' => 'trashed',
                    'label' => 'Mostrar eliminados'
                ],
                false,
                function () { // if the filter is active
                    $this->crud->addClause('onlyDeleted');
                    $this->crud->orderBy('building_contact.deleted_at', 'DESC');
                    $this->crud->addButtonFromView('line', 'restoreContact', 'restoreContact', 'beginning');
                    $this->crud->addButtonFromView('line', 'deletedBy', 'deletedBy');
                    $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revisions']);
                }
            );

            $this->crud->addFilter(
                [ // simple filter
                    'type' => 'simple',
                    'name' => 'verification',
                    'label' => '/ Mostrar contactos sin verificar'
                ],
                false,
                function () { // if the filter is active
                    $this->crud->addClause('noVerification');
                    $this->crud->addButtonFromView('line', 'verificateContact', 'verificateContact');
                    $this->crud->denyAccess(['reorder', 'revise']);
                }
            );

            $this->crud->addFilter(
                [
                    'type' => 'text',
                    'name' => 'ci',
                    'label' => ''
                ],
                false,
                function ($value) { // if the filter is active
                    $this->crud->addClause('ci', $value);
                    $this->crud->addButtonFromView('line', 'confrmContact', 'confrmContact');
                }
            );
        } else {
            $this->crud->addClause('noVerificationCadete');
        }


        $this->crud->addFilter(
            [
                'name' => 'search_field',
                'type' => 'dropdown',
                'label' => 'Buscar por'
            ],
            [
                'full_flats_buildings_text' => 'Edificio',
                'number_with_tower' => 'Apartamento',
                'name' => 'Nombre y Apellido',
                'email' => 'Email',
                'contact_type' => 'Tipo de contacto',
                'ci' => 'Doc./Clave',
                'phone_mobile' => 'Teléfono',
                'owner_or_tenant' => 'Propietario o inquilino'
            ],
            function ($value) {
                return $value;
            }
        );

        $this->crud->addFilter([
            'name' => 'building',
            'type' => 'select2',
            'label' => 'Edificios',
        ], function () {
            $count = 1;
            $array_info_building = [];
            foreach (Building::all() as $build) {
                $array_info_building[$build->building_number] = $build->building_number . '-' . $build->name;
            }
            return $array_info_building;

        }, function ($value) { // if the filter is active
            $isTrashed = array_filter($this->crud->filters()->toArray(), function ($filter) {
                if ($filter->name == 'trashed' && $filter->currentValue) {
                    return $filter;
                }
            });
            $this->crud->addClause('whereHas', 'contacts', function (Builder $query) use ($value, $isTrashed) {
                $buildingId = Building::query()->where('building_number', $value)->first()->id;
                $query->where('building_id', $buildingId)
                    ->when(sizeof($isTrashed) > 0, function (Builder $query) {
                        $query->withoutGlobalScopes()->whereNotNull('building_contact.deleted_at');
                    });
            });
            unset($this->crud->filters['flat_id']);
        });

        $this->crud->addFilter([
            'name' => 'flat_id',
            'type' => 'select2_building_flats',
            'label' => 'Apartamentos',
        ],
            false,
            function ($value) { // if the filter is active
                $isTrashed = array_filter($this->crud->filters()->toArray(), function ($filter) {
                    if ($filter->name == 'trashed' && $filter->currentValue) {
                        return $filter;
                    }
                });
                $this->crud->addClause('whereHas', 'contacts', function ($query) use ($value, $isTrashed) {
                    $query->where('flat_id', $value)
                        ->when(sizeof($isTrashed) > 0, function (Builder $query) {
                            $query->withoutGlobalScopes()->whereNotNull('building_contact.deleted_at');
                        });
                });
            });


        $this->crud->addFilter([
            'name' => 'contact_type',
            'type' => 'select2',
            'label' => 'Tipo de contacto'
        ], function () {

            return [
                'Residente' => 'Residente',
                'No Residente' => 'No Residente',
                'Autorizado' => 'Autorizado',
                'Comisión' => 'Comisión',
                'Contacto Principal' => 'Contacto Principal',
                'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
                'Oficina' => 'Oficina',
                'Prohibido Ingreso' => 'Prohibido Ingreso',
                'Empleado' => 'Empleado',
                'Proveedor' => 'Proveedor',
                'Responsable' => 'Responsable',
                'Foxsys' => 'Foxsys',
            ];

        }, function ($value) { // if the filter is active
            $isTrashed = array_filter($this->crud->filters()->toArray(), function ($filter) {
                if ($filter->name == 'trashed' && $filter->currentValue) {
                    return $filter;
                }
            });
            $this->crud->addClause('where', function (Builder $query) use ($value, $isTrashed) {
                $query->where('building_contact.contact_type', $value)
                    ->when(sizeof($isTrashed) > 0, function (Builder $query) {
                        $query->withoutGlobalScopes()->whereNotNull('building_contact.deleted_at');
                    });
            });
        });

        $this->crud->addFilter(
            [ // simple filter
                'type' => 'simple',
                'name' => 'mail',
                'label' => '/ Tiene mail'
            ],
            false,
            function () { // if the filter is active
                $isTrashed = array_filter($this->crud->filters()->toArray(), function ($filter) {
                    if ($filter->name == 'trashed' && $filter->currentValue) {
                        return $filter;
                    }
                });
                $this->crud->addClause('whereHas', 'contacts', function ($query) use ($isTrashed) {
                    $query->whereNotNull('email')
                        ->when(sizeof($isTrashed) > 0, function (Builder $query) {
                            $query->withoutGlobalScopes()->whereNotNull('building_contact.deleted_at');
                        });
                });
            }
        );
    }

    protected function setupInlineCreateDefaults()
    {
    }


    protected function setupInlineCreateOperation()
    {
        return $this->crud->addFields([
            [   // radio
                'name' => 'type_of_contact_in_building', // the name of the db column
                'label' => 'Unidad', // the input label
                'type' => 'radio',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    "Residencial" => "Residencial",
                    "Oficina" => "Oficina",
                ],
                'default' => 'Residencial',
                // optional
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'owner_or_tenant', // The db column name
                'label' => "Relación", // Table column heading
                'type' => 'radio',
                'options' => [
                    'S/D' => 'S/D',
                    'Propietario' => 'Propietario',
                    'Inquilino' => 'Inquilino',
                    'Apoderado' => 'Apoderado'
                ],
                'default' => 'S/D',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'principal_contact', // The db column name
                'label' => "Contacto principal del Apto.", // Table column heading
                'type' => "checkbox",
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: -6%'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'contact_type', // The db column name
                'label' => "Tipo de contacto", // Table column heading
                'type' => 'select_from_array_contact_type',
                'options' =>
                    [
                        'Residente' => 'Residente',
                        'No Residente' => 'No Residente',
                        'Comisión' => 'Comisión',
                        'Contacto Principal' => 'Contacto Principal',
                        'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
                        'Oficina' => 'Oficina',
                        'Prohibido Ingreso' => 'Prohibido Ingreso',
                    ],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'required' => true,
                    'id' => 'contact_type',
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'flat_id', // The db column name
                'label' => "Apartamento", // Table column heading
                'type' => "flat_in_own_buillding",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],

            ],
            [
                'name' => 'description', // The db column name
                'label' => "Descripción", // Table column heading
                'type' => "textarea",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],

            ],
            [
                'name' => 'name', // The db column name
                'label' => "Nombre", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'required' => true,
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'surname', // The db column name
                'label' => "Apellido", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'required' => true,
                    'column-order' => 2,
                ],
            ],
            [   // DateTime
                'name' => 'birthday',
                'label' => 'Fecha nacimiento',
                'type' => 'datetime_picker',

                // optional:
                'datetime_picker_options' => [
                    'format' => 'DD/MM/YYYY',
                    'language' => 'es'
                ],
                'allows_null' => true,
                // 'default' => '2017-05-12 11:59:59',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                ],

            ],
            [
                'name' => 'under_age', // The db column name
                'label' => "Menor de edad", // Table column heading
                'type' => "checkbox",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'padding-top: 7%;'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'gender', // the name of the db column
                'label' => 'Género', // the input label
                'type' => 'radio',
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    "Masculino" => "Masculino",
                    "Femenino" => "Femenino",
                    "Otro" => "Otro"
                ],
                'wrapper' => ['class' => 'form-group col-md-12'],
                'inline' => true, // show the radios all on the same line?
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'ci', // The db column name
                'label' => "Documento de identidad", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-7'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'foreign_document', // The db column name
                'label' => "Otro tipo de C.I", // Table column heading
                'type' => "checkbox",
                'wrapper' => ['class' => 'form-group col-md-5', 'style' => 'padding-top: 7%;'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'own_foxsys', // the name of the db column
                'label' => 'Telefonos', // the input label
                'type' => 'sub_label',
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'dont_call', // The db column name
                'label' => "No llamar", // Table column heading
                'icon-name' => "phone-slash", // Table column heading
                'icon-color' => "red", // Table column heading
                'type' => "checkbox_icon",
                'wrapper' => ['class' => 'form-group col-md-9', 'style' => 'padding-top: 1%;'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'phone_mobile', // The db column name
                'label' => "Teléfono celular", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'foreign_phone', // The db column name
                'label' => "Teléfono extranjero", // Table column heading
                'type' => "checkbox",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'phone_home', // The db column name
                'label' => "Teléfono hogar", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'home_is_primary_phone', // The db column name
                'label' => "Marcar teléfono hogar como primario", // Table column heading
                'type' => "checkbox",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
        ]);
    }

    protected function setupCreateOperation()
    {
        $fields = new ContactFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());
        if (backpack_user()->hasRole('Cadete') || backpack_user()->hasRole('Admin')) {

            $this->crud->addField(
                [   // Checkbox
                    'name' => 'get_keys',
                    'label' => 'Recibió llaves',
                    'type' => 'checkbox',
                ]
            )->beforeField('created_by');
            $this->crud->addField(
                [   // Checkbox
                    'name' => 'no_resident',
                    'label' => 'No es residente',
                    'type' => 'checkbox'
                ]
            )->beforeField('created_by');
        }
        Widget::add()->type('script')->content('/js/Contact/requiredFields.js');
        Widget::add(['type' => 'disable_form_fields_contact']);
    }

    protected function setupUpdateOperation()
    {
        if (backpack_user()->hasPermissionTo('Editar Contactos Temporales | Monitoreo') && ($this->crud->getCurrentEntry()->contact_type != 'Autorizado' || $this->crud->getCurrentEntry()->start_date == null)) {
            abort(403, 'No tienes permiso para editar este contacto.');
        }

        $this->setupCreateOperation();
    }

    protected function setupShowOperation()
    {
        $this->crud->setEntityNameStrings('contacto llamando', 'contacto llamando');

        $fields = new ContactFields();
        $this->crud->addColumns($fields->getShowFields());
        $this->autoSetupShowOperation();
    }

    public function store(UserStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $contact = new Contact();
            $newContact = $contact->storeContact($request);
            DB::commit();

            \Alert::add('success', '<strong>Acción completada con exito</strong><br>Contacto creado.')->flash();
            return redirect('/admin/contact/' . $newContact->id . '/info');
        } catch (\Throwable $e) {
            DB::rollBack();
            \Alert::add('warning', "<strong>Ha ocurrido un error</strong></br>{$e->getMessage()}<br>{}")->flash();
            return back();
        }
    }

    public function update(UserUpdateRequest $request, $id)
    {
        $contact = Contact::withTrashed()->where('id', $id)->first();

        DB::beginTransaction();
        try {
            $contact->updateContact($request);
            DB::commit();

            \Alert::add('success', '<strong>Acción completada con éxito</strong><br>Contacto actualizado.')->flash();

            return redirect('/admin/contact/' . $contact->id . '/info');
        } catch (\Throwable $e) {
            DB::rollBack();
            \Alert::add('warning', "<strong>Ha ocurrido un error</strong></br>{$e->getMessage()}<br>{}")->flash();
            return back();
        }
    }


    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        // get entry ID from Request (makes sure its the last ID for nested resources)
        $id = $this->crud->getCurrentEntryId() ?? $id;

        $temp = Contact::find($id);
        $temp->deleter_user_id = backpack_user()->id;
        $temp->save();

        KazooFlatsConsecutive::query()->where('contact_id', $id)->delete();

        BuildingContact::updateForDeleteContactFromShow($id);

        return $this->crud->delete($id);
    }

    public function restore($relation_id)
    {

        $contact = BuildingContact::withTrashed()->find($relation_id);
        $contact->restore();
        $contact->update([
            'deleter_user_id' => null
        ]);
        $contacts = BuildingContact::where('contact_id', $contact?->contact_id)
            ->get();
        if (count($contacts) == 1) {
            $contact->update([
                'main_building' => true,
            ]);
            Contact::restoreUser($contact->contact_id);
        }

        if ($contact->building->incoming_call) {
            \Artisan::call('queue:restart');
            UpdateDeviceKazooAfterRestoreContactJob::dispatch($contact);
        }

        return redirect('admin/contact');
    }

    public function getOtherTypeOfContact($id, $flat_id)
    {
        $contact = BuildingContact::where('contact_id', $id)
            ->where('flat_id', $flat_id)
            ->first();
        $otherTypesOfContact = Contact::getOthersTypesOfContactFlat($contact->contact_type);

        return response()->json([
            'status' => 'ok',
            'data' => $otherTypesOfContact,
            'message' => 'Otros tipos de contacto'
        ]);
    }

    public function deleteDuplicated()
    {
        return Contact::deleteDuplicatedContacts();
    }

    public function forceDeleteSoftDeleted()
    {
        return Contact::forceDeleteSoftDeleted();
    }

    public function mergeDescriptionAndRelationFields()
    {
        return Contact::mergeDescriptionAndRelationFields();
    }

    private function getContactInfoData($contact)
    {
        $contact->load([
            'cars',
            'tags',
            'flats',
            'mainBuilding.building',
            'mainBuilding.flat',
            'buildingContacts' => function ($query) {
                $query->without('contact');
            }
        ]);

        return [
            'contact' => $contact,
            'building' => $contact->mainBuilding->building,
            'cars' => $contact->cars,
            'tags' => $contact->tags,
            'flat' => $contact->mainBuilding->flat,
            'buildingContactsData' => $contact->mapBuildingContactData()
        ];
    }

    public function showInfo($id, Request $request)
    {
        $contact = Contact::findOrFail($id);
        $data = $this->getContactInfoData($contact);

        return view('contacts.show.main', $data);
    }

    public function showInfoByPhone($phone)
    {
        $buildingContact = BuildingContact::where('phone_mobile', $phone)->first();

        if (!$buildingContact) {
            return redirect('/admin/buildings-index');
        }

        $contact = Contact::findOrFail($buildingContact->contact_id);
        $data = $this->getContactInfoData($contact);

        return view('contacts.show.main', $data);
    }

    public function editLastInteractionInShow($id, Request $request)
    {
        $interact = RecentlyInteraction::where('receiver_id', $id)->orWhere('caller_id', $id)->orderBy('created_at',
            'DESC')->first();
        $interact->comment = $request->comment;
        $interact->save();
    }


    public function list(Request $request)
    {
        return \GuzzleHttp\json_encode(
            User::where('name', 'LIKE', '%' . $request->q . '%')
                ->whereNotNull('email')
                ->whereNull('notified')->get()
        );
    }

    public function listResidents(Request $request)
    {
        return \GuzzleHttp\json_decode(
            \App\Models\User\User::where('name', 'LIKE', '%' . $request->q . '%')
                ->whereHas('buildingContacts', function ($query) use ($request) {
                    $query->whereIn('contact_type', \App\Models\User\User::$authorizedLoginTypes);
                    $query->where('building_id', $request->building_id);
                })
                ->whereNull('notified')
                ->get()
        );
    }

    public function showContact($name, $phone, $address)
    {
        return view('show.contact_show', compact(['name', 'phone', 'address']));
    }

    public function aceptAppUser($id)
    {
        $user = \App\Models\User\User::find($id);
        if ($user->building_id && $user->flat_id) {
            $user->needs_verification = null;
            $user->notify(new UserVerified($id));
            $user->token = md5(Carbon::now());
            $user->notified = true;
            $user->save();
            return redirect('admin/contact?verification=true');
        }
        \Alert::add('error', 'Para verificar el contacto debe asignarle edificio y apartamento.')->flash();
        return redirect('admin/contact?verification=true');
    }

    public function inline(Request $request)
    {
        $fields = $request->all()['form'];
        $building_id = 0;
        foreach ($fields as $field) {
            if ($field['name'] == 'id') {
                $building_id = $field['value'];
            }
        }
        $search_term = $request->input('q');
        if ($search_term) {
            return Contact::where('building_id', $building_id)->Where('complete_name', 'LIKE',
                '%' . $search_term . '%')->paginate(10); //return building_>contactos con la relacion
        }

        return Contact::paginate(10);
    }

    public function storeInlineCreate(UserStoreRequest $request)
    {
        $building_id = explode('/', $request->all()['http_referrer'])[5];

        $contact = new Contact();
        $contact->storeInlineContact($request, $building_id);
        return redirect('admin/contact');

        // do not carry over the flash messages from the Create operation
        Alert::flush();

        return $result;
    }

    public function verificateContact($id, $acepter_id)
    {
        $contact = \App\Models\User\User::findOrFail($id);
        $acepter = \App\Models\User\User::findOrFail($acepter_id);
        if ($contact->building_id && $contact->flat_id) {
            $contact->fill($acepter->getValueOfAppFields());
            $contact->password = $acepter->password;
            self::syncAccessCode($id, $acepter->getAccessCodePermanent());
            foreach ($acepter->getAccessCodeTemporal() as $temporalCode) {
                self::syncAccessCode($id, $temporalCode);
            }

            self::syncLogs($id, $acepter_id);

            $contact->notify(new UserVerified($id));
            $contact->token = md5(Carbon::now());
            $contact->notified = true;
            $contact->needs_verification = null;
            $contact->save();
            $acepter->forceDelete();
            return redirect('admin/contact?verification=true');
        }
        \Alert::add('error', 'Para verificar el contacto debe asignarle edificio y apartamento.')->flash();
        return redirect('admin/contact');
    }

    public static function syncAccessCode($userId, $accessCode)
    {
        $ac = AccessCode::where('code', $accessCode)->first();
        if ($ac) {
            $ac->user_id = $userId;
            $ac->save();
        }
    }

    public static function syncLogs($newId, $oldId)
    {
        foreach (Log::where('user_id', $oldId)->get() as $log) {
            echo $log;
            $log->user_id = $newId;
            $log->save();
        }
    }


    public function deleteInInfo($id)
    {
        $this->crud->hasAccessOrFail('delete');
        $id = $this->crud->getCurrentEntryId() ?? $id;

        $temp = Contact::where('id', $id)->first();
        $temp->deleter_user_id = backpack_user()->id;
        $temp->save();
        KazooFlatsConsecutive::query()->where('contact_id', $temp->id)->delete();

        BuildingContact::createDeleteIntercomContact($temp);

        $this->crud->delete($id);
        $alert = \Alert::add('success',
            '<strong>Elemento eliminado</strong><br>EL elemento ha sido eliminado de forma correcta.');
        return redirect('admin/contact');
    }

    public function getContact($id)
    {
        return Contact::findOrFail($id);
    }

    public function getContactCi($ci)
    {
        $aux = false;
        if (Contact::where('ci', $ci)->first()) {
            return $aux = true;
        }
        return $aux;
    }

    public function getContactRelationshipBuilding($id)
    {
        return BuildingContact::query()
            ->where('contact_id', $id)
            ->orderByDesc('main_building')
            ->get()
            ->map(fn($contact) => (new BuildingContactResource($contact))->toArray(request()))->toArray();
    }

    public function getIntercomIncomingCall($contactId, $flatId)
    {
        return response()->json(
            [
                'data' => BuildingContact::where('contact_id', $contactId)
                    ->where('flat_id', $flatId)
                    ->value('intercom_incoming_call'),
                'message' => "Permite llamada al desde intercom?"
            ]
        );
    }

    public function deleteContactRelationshipBuilding($contactId, $flatId)
    {
        $contact = BuildingContact::query()
            ->where('contact_id', $contactId)
            ->where('flat_id', $flatId)->first();

        if (!$contact) {
            return;
        }

        $contact->deleter_user_id = backpack_user()->id;
        $contact->update();
        BuildingContact::createDeleteIntercomContact($contact);
        $contact->delete();
    }

    public function saveEmailCiSecurityWord($contactId)
    {
        $user = Contact::query()->find($contactId);
        if (!$user) {
            return;
        }
        $email = request()->input('email');
        $ci = request()->input('ci');
        $security_word = request()->input('security_word');

        $user->email = $email;
        $user->ci = $ci;
        $user->security_word = $security_word;
        $user->update();

        BuildingContact::query()->where('contact_id', $contactId)
            ->update([
                'security_word' => $security_word,
            ]);
    }

    public function deleteContactRelationshipBuildingFromList($relation_id)
    {
        DB::beginTransaction();
        try {
            $contact = BuildingContact::query()->find($relation_id);
            $last_relation = Contact::removeContactFromUser($contact?->contact_id);
            $contactId = $contact->contact_id;

            $contact->main_building = false;
            $contact->deleter_user_id = backpack_user()->id;
            $contact->update();

            if ($contact->callflow_id)
                KazooService::deleteCallFlow(md5($contact->building->building_number), $contact->callflow_id);

            $contact->update([
                'intercom_incoming_call' => false
            ]);

            BuildingContact::createDeleteIntercomContact($contact);

            KazooFlatsConsecutive::query()->where('contact_id', $contact->contact_id)->where('flat_id', $contact->flat_id)->delete();

            $res = $contact->delete();


            BuildingContact::updateMainBuilding($contactId, $last_relation);

            DB::commit();

            return $res;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw new Error($th->getMessage());
        }
    }

    public function indexFieldAjax(Request $request)
    {
        $search_term = $request->input('q');
        $page = $request->input('page');

        if ($search_term) {
            $results = Contact::where('complete_name', 'LIKE', '%' . $search_term . '%')->paginate(10);
        } else {
            $results = Contact::paginate(10);
        }

        return $results;
    }

    public function showFieldAjax($id)
    {
        return Contact::find($id);
    }

    public function getAllContacts(Request $request)
    {
        $users = Contact::where('complete_name', 'like', '%' . $request->q . '%')->get();

        $data = $users->filter(function ($user) {
            return !is_null($user->phone_mobile) && !empty($user->phone_mobile);
        })->map(function ($user) {
            return [
                'id' => $user->id,
                'text' => $user->complete_name . ' - ' . $user->phone_mobile,
            ];
        });

        return response()->json(['results' => $data->values()], 200);
    }


    public function getAuthorizedTime($id, $flat_id)
    {
        $contact = BuildingContact::query()->where('contact_id', $id)
            ->where('flat_id', $flat_id)->first();
        $startDate = $contact?->start_date;
        $endDate = $contact?->end_date;

        return response()->json([
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
    }

    public function getSchedule($id, $flat_id)
    {
        $contact = BuildingContact::query()->where('contact_id', $id)
            ->where('flat_id', $flat_id)->first();
        $schedule = $contact?->schedule;

        return response()->json([
            'schedule' => $schedule ? json_decode($schedule) : []
        ]);
    }

    public function saveSchedule()
    {
        $userId = request()->input('userId');
        $flatId = request()->input('flatId');
        $schedule = request()->input('schedule');

        return Contact::saveSchedule($userId, $flatId, $schedule);
    }
}
