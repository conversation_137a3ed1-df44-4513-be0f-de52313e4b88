<?php

namespace App\Http\Controllers\Admin;

use App\Http\Fields\FlatFields;
use App\Http\Requests\FlatRequest;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Flat;
use App\Models\Tower;
use App\Models\User\Contact;
use App\Models\User\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\ReviseOperation\ReviseOperationCustom;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Venturecraft\Revisionable\RevisionableTrait;

/**
 * Class FlatCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class FlatCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;

    public function setup()
    {
        $this->crud->setModel('App\Models\Flat');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/flat');
        $this->crud->setEntityNameStrings('apartamento', 'apartamentos');
        $this->crud->denyAccess('show');
        if (!backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Representante Atención al Cliente'])) {
            $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revisions']);
        }
        if (backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Representante Atención al Cliente'])) {
            $this->crud->enableExportButtons();
        }
    }


    public function store(FlatRequest $request)
    {
        $request->request->add(['creator_user_id' => Auth::user()]);
        $tower_id = Tower::find($request->input('tower_id'));
        $flat = new Flat();
        $flat->fill($request->all());
        $flat->created_by = backpack_user()->id;
//        $flat->number_with_tower = $flat->setNumberWithTowerRequest($tower_id, $request->input('number'));
        $flat->save();
        if ($request->tower_id != NULL) {
            $tower = Tower::findOrFail($request->tower_id);
            $tower->flats = $tower->flats . ',' . $request->number;
            $tower->save();
        }


        return redirect('admin/flat');
    }

    public function update(FlatRequest $request, $id)
    {

        $tower_id = Flat::select('tower_id')->where('id', $id)->first();

        $tower_request = Tower::find($request->input('tower_id'));

        $flat = Flat::findOrFail($id);
        $current_flat_number = $flat->number;

        $flat->fill($request->all());
        $flat->updated_by = backpack_user()->id;
        $flat->tower_id = $request->tower_id;
//        $flat->number_with_tower = $flat->setNumberWithTowerRequest($tower_request, $request->input('number'));
        $flat->save();

        if ($tower_id->tower_id == NULL) {
            return redirect('admin/flat');
        }
        $tower = Tower::findOrFail($tower_id->tower_id);
        $tower_flats_original = explode(',', $tower->flats);
        $searchIdxArray = array_search($current_flat_number, $tower_flats_original);


        // if towerNotChanged()
        if ($tower->id == intval($request->tower_id)) {
            Flat::updateFlatOnly($searchIdxArray, $tower_flats_original, $tower, $flat);
        } else {
            Flat::removeFlatFromTower($tower_flats_original, $searchIdxArray, $tower);
            Flat::insertFlatIntoNewTower($id, $flat);
        }
        return redirect('admin/flat');
    }

    protected function setupListOperation()
    {
        $fields = new FlatFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());
    }

    protected function setupCreateOperation()
    {
        $this->crud->setValidation(FlatRequest::class);
        $fields = new FlatFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function flatInfo($id)
    {
        return (string)Flat::findOrFail($id);
    }


    public function duplicateFlat($ci, $building_id)
    {
        $string_with_spaces = preg_replace("/_+/", " ", $building_id);
        $query = Contact::where('ci', '=', $ci)->get();
        $id = '';

        foreach ($query as $object) {
            if ($object->mainBuilding && $object->mainBuilding->name == $string_with_spaces) {
                $id = $object->mainBuilding->name;
            }
        }

        $query2 = Building::where('name', '=', $id)->first();

        $array = array();

        foreach ($query as $q) {
            if ($query2 && $query2->id == $q->building_id) {
                $array[] = array('flat_number' => $q->flat_number, 'building_id' => $q->building_id);
            }
        }

        return $array;

    }


    public function showContacts($flat_id)
    {
        $flat = Flat::findOrFail($flat_id);

        return response()->json($flat->contacts);
    }

    public function rearrangeFlats()
    {
        Flat::rearrangeFlats();
    }


    public function cleanFlats()
    {
        foreach (Flat::onlyTrashed()->get() as $flat) {
            $flat->forceDelete();
        }
        return 'OK';
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        // get entry ID from Request (makes sure its the last ID for nested resources)
        $id = $this->crud->getCurrentEntryId() ?? $id;
        foreach (BuildingContact::where('flat_id', $id)->get() as $user) {
            $user->flat_id = null;
            $user->save();
        }
        return $this->crud->delete($id);
    }

    public function getFlatsByBuildingNumber($number)
    {
        return response()->json([
            'flats' => Flat::query()->whereHas('building', function (Builder $query) use ($number) {
                $query->where('building_number', $number);
            })
                ->orderBy('number_with_tower', 'ASC')
                ->get()
        ]);
    }

}
