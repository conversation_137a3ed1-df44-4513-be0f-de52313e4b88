<?php

namespace App\Http\Controllers\Admin;

use App\Http\Fields\CaseFields;
use App\Http\Requests\CaseRequest;
use App\Models\Area;
use App\Models\Building;
use App\Models\Caseq;
use App\Models\Category;
use App\Models\Flat;
use App\Models\Notification;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\PermissionManager\app\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Widget;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;


/**
 * Class CaseCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CaseCrudController extends CrudController
{

    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;
    use \App\Traits\CustomRevisionsTrait {
        \App\Traits\CustomRevisionsTrait::listRevisions insteadof \App\Helpers\Revise\ReviseOperationCustom;
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Caseq::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/case');
        CRUD::setEntityNameStrings('caso', 'casos');
        $this->crud->addButtonFromView('line', 'showInfoCase', 'show_info_case');
        $this->crud->addButtonFromView('line', 'closeCase', 'close_cases');
        $this->crud->denyAccess(['delete', 'show']);
        if (backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Jefe Ingeniería', 'Jefe Administrativo', 'Jefe Operaciones', 'Jefe Técnico', 'Jefe Comercial', 'Representante Atención al Cliente'])) {
            $this->crud->enableExportButtons();
        }

        // Configurar revisiones personalizadas
        $this->setupReviseDefaults();
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    public function getUrlFilter()
    {
        $selectedStates = [];
        $announcement = 0;

        if (request()->has('opened')) {
            $selectedStates = array('pendiente', 'en_curso', 'solicitar_info', 'aviso_al_cliente', 'ABIERTO', 'REABIERTO', 'EN CURSO');
        }

        if (request()->has('state_pendiente')) {
            $selectedStates[] = 'pendiente';
            $selectedStates[] = 'ABIERTO';
            $selectedStates[] = 'REABIERTO';
        }

        if (request()->has('state_en_curso')) {
            $selectedStates[] = 'en_curso';
            $selectedStates[] = 'EN CURSO';
        }
        if (request()->has('state_sol_info')) {
            $selectedStates[] = 'solicitar_info';
        }

        if (request()->has('state_cliente')) {
            $selectedStates[] = 'aviso_al_cliente';
        }
        if (request()->has('state_cerrado')) {
            $selectedStates[] = 'finalizado';
            $selectedStates[] = 'CERRADO';

        }

        if (request()->has('state_all')) {
            $selectedStates = array('pendiente', 'en_curso', 'solicitar_info', 'aviso_al_cliente', 'finalizado', 'ABIERTO', 'REABIERTO', 'EN CURSO', 'CERRADO');
        }

        if (request()->has('state_announcement')) {
            $announcement = '1';
        }


        $values = [
            'states' => $selectedStates,
            'announcement' => $announcement,
        ];

        return $values;

    }

    protected function setupListOperation()
    {
        $announcementOn = $this->getUrlFilter()['announcement'];
        if ($announcementOn) {

        }

        $this->crud->addFilter(
            [
                'name' => 'opened',
                'type' => 'case_simple_state_all',
                'label' => 'Abiertos',
                'color' => 'bold'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                if ($announcementOn) {
                    $query->where('announcement', $this->getUrlFilter()['announcement']);
                }
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'state_pendiente',
                'type' => 'case_simple_state_color',
                'label' => 'Pendiente',
                'color' => 'color-red-cases'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                if ($announcementOn) {
                    $query->where('announcement', $this->getUrlFilter()['announcement']);
                }
            }
        );
        $this->crud->addFilter(
            [
                'name' => 'state_en_curso',
                'type' => 'case_simple_state_color',
                'label' => 'En curso',
                'color' => 'color-blue-cases'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                if ($announcementOn) {
                    $query->where('announcement', $this->getUrlFilter()['announcement']);
                }
            }
        );


        $this->crud->addFilter(
            [
                'name' => 'state_sol_info',
                'type' => 'case_simple_state_color',
                'label' => 'Solicitar info',
                'color' => 'color-orange-cases'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                if ($announcementOn) {
                    $query->where('announcement', $this->getUrlFilter()['announcement']);
                }

            }
        );

        $this->crud->addFilter(
            [
                'name' => 'state_cliente',
                'type' => 'case_simple_state_color',
                'label' => 'Aviso al cliente',
                'color' => 'color-green-cases'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                if ($announcementOn) {
                    $query->where('announcement', $this->getUrlFilter()['announcement']);
                }
            }
        );


        $this->crud->addFilter(
            [
                'name' => 'state_cerrado',
                'type' => 'case_simple_state_color',
                'label' => 'Finalizado',
                'color' => 'color-grey-cases'
            ],
            false,
            function ($value) use ($announcementOn) {
                $query = $this->crud->addClause('whereIn', 'state', $this->getUrlFilter()['states']);
                $this->crud->addButtonFromView('line', 'openCase', 'open_cases');
                $this->crud->denyAccess(['create', 'reorder', 'revisions', 'update']);
                $this->crud->removeButton('closeCase');
                $this->crud->addButtonFromView('line', 'openCase', 'open_cases');
            }

        );

        $this->crud->addFilter(
            [
                'name' => 'state_all',
                'type' => 'case_simple_state_color',
                'label' => 'Todos',
                'color' => 'gray'

            ],
            false,
            function ($value) use ($announcementOn) { // if the filter is active
                $this->crud->query->withoutGlobalScopes();

                if ($announcementOn) {
                    $this->crud->query->where('announcement', $this->getUrlFilter()['announcement']);
                }
            }
        );


        $this->crud->addFilter([
            'name' => 'last_category_id',
            'type' => 'select2_multiple_new_style',
            'label' => 'Categorias'
        ], function () {
            $count = 1;
            $array_info_category = [];
            foreach (Category::allCategoryFlatList() as $cat) {
                $array_info_category[$cat['id']] = $cat['name'];
            }
            return $array_info_category;

        }, function ($value) {

            $this->crud->addClause('whereIn', 'last_category_id', json_decode($value));
        });


        $this->crud->addFilter([
            'name' => 'type',
            'type' => 'select2_multiple_new_style',
            'label' => 'Areas'
        ], function () {

            return [
                1 => 'Área no definida',
                12 => 'Ingeniería N1',
                2 => 'Ingeniería N2',
                3 => 'Comercial',
                4 => 'Monitoreo',
                5 => 'Administración',
                6 => 'Mantenimiento',
                7 => 'Supervisión',
                8 => 'Atención',
                9 => 'FlowLabs',
                10 => 'Seguridad',
                11 => 'Foxsys',
                13 => 'Instalaciones',
                14 => 'Suministros',
                15 => 'App Foxsys',
                16 => 'Op Seguridad',
                17 => 'Diseño',
                18 => 'Investigaciones',
            ];

        }, function ($value) { // if the filter is active

            $this->crud->addClause('whereIn', 'area_id', json_decode($value));
        });

        $this->crud->addFilter([
            'name' => 'building',
            'type' => 'select2_multiple_new_style',
            'label' => 'Edificios'
        ], function () {
            $count = 1;
            $array_info_building = [];
            foreach (Building::all() as $build) {
                $array_info_building[$build->building_number] = $build->building_number . '-' . $build->name;
            }
            return $array_info_building;

        }, function ($value) { // if the filter is active
            $this->crud->addClause('whereHas', 'building', function ($query) use ($value) {
                $query->whereIn('building_number', json_decode($value));
            });
        });

        $this->crud->addFilter([
            'name' => 'comments',
            'type' => 'select2_multiple_new_style',
            'label' => 'Comentarios'
        ], function () {
            $array_comments_categories = [];
            foreach (Category::where('comment_check', '>', 0)->get() as $category) {
                $array_comments_categories[$category->name] = $category->name;
            }
            return $array_comments_categories;
        },
            function ($value) {
                $this->crud->addClause('where', function ($query) use ($value) {
                    foreach (json_decode($value) as $comment_category) {
                        $query->orWhere(function($q) use ($comment_category) {
                            $q->whereNotNull('comments')
                              ->whereRaw("JSON_VALID(comments)")
                              ->whereJsonContains('comments', ['category_comments' => $comment_category]);
                        });
                    }
                });
            });


        $this->crud->addFilter(
            [
                'type' => 'date_range',
                'name' => 'from_to',
                'label' => 'Fecha'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                $this->crud->addClause('whereBetween', 'created_at', [$dates->from, $dates->to]);
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'state_announcement',
                'type' => 'label_with_icon',
                'label' => 'Novedades',
                'color' => 'color-blue-cases',
                'icon' => 'newspaper-outline',

            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'announcement', $this->getUrlFilter()['announcement']);
            }
        );

        $fields = new CaseFields();

        $this->crud->addColumns($fields->getColumnsFields());

    }

    public function showInfoView($case_id)
    {
        return view('cases.show.main', compact('case_id'));
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        $this->crud->setValidation(CaseRequest::class);
        $fields = new CaseFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        Widget::add()->type('script')->content('/js/Cases/requiredFields.js');
        $this->crud->addSaveActions($this->saveBtnActions());
        CRUD::setOperationSetting('showSaveActionChange', false);
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $fields = new CaseFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        Widget::add()->type('script')->content('/js/Cases/requiredFields.js');
        Widget::add(['type' => 'disable_form_fields_case']);
        $this->crud->addSaveActions($this->saveBtnActions());
        CRUD::setOperationSetting('showSaveActionChange', false);

    }

    private function saveBtnActions()
    {
        $this->crud->removeSaveActions(['save_and_back', 'save_and_edit', 'save_and_new']);
        return [
            [
                'name' => 'save_and_show_case',
                'button_text' => 'Guardar y listar casos',
                'redirect' => function ($crud, $request, $itemId) {
                    return $crud->route;
                },
                'order' => 3,
            ],
            [
                'name' => 'save_and_edit',
                'button_text' => 'Guardar y ver',
                'redirect' => function ($crud, $request, $itemId) {
                    return $crud->route . '/' . $itemId . '/show';
                },
                'order' => 1,
            ],
            [
                'name' => 'save_and_new_custom',
                'button_text' => 'Guardar y nuevo',
                'redirect' => function ($crud, $request, $itemId) {
                    return $crud->route . '/create';
                },
                'order' => 4,
            ],
        ];


    }

    public function store(CaseRequest $request)
    {
        $case = new Caseq();
        $case->storeCase($request);
        $this->crud->setSaveAction('save_and_back');
        return $this->crud->performSaveAction($case->getKey());
    }


    public function updateComment($idCase, $data)
    {
        $case = Caseq::where('id', $idCase)->first();
        $case->comments = $data;
        $case->save();
        return true;
    }

    public function update(CaseRequest $request, $id)
    {
        $case = Caseq::findOrFail($id);
        \DB::transaction(function () use ($id, $request, $case) {
            $old_category = $case->last_category_id;
            $case->updateCase($request, $old_category);
            $case->updateCommentCase($request, $old_category);
            $this->data['entry'] = $this->crud->entry = $case;
            $this->crud->setSaveAction('save_and_back');
        });
        return $this->crud->performSaveAction($case->getKey());
    }


    public function closeCase($id)
    {
        $case_area_id = Caseq::where('id', $id)->first()?->area_id;
        $area = Area::query()->find($case_area_id);
        $rol_area = Role::where('area_id', $case_area_id)->pluck('name');
        $rol_area->prepend('no role area');
        foreach ($rol_area ?? [] as $rol) {
            if (backpack_user()->hasRole([$rol, 'Admin']) || (backpack_user()->hasRole('Representante Atención al Cliente') && $area->name == 'Monitoreo')) {
                $cas = Caseq::where('id', $id)->first();
                $cas->end_date = now('America/Montevideo');
                $cas->state = 'finalizado';
                $cas->verifyStateAndCloseAnnouncement();
                $cas->updated_by = backpack_user()->id;
                $cas->close_cases_user_id = backpack_user()->id;
                $related_notifications = Notification::where('data', 'LIKE', '%case":' . $id . '%')->get();
                foreach ($related_notifications ?? [] as $notification) {
                    $notification->delete();
                }
                $cas->save();
                return redirect('admin/case');
            }
        }
        return view('show/cant_do');
    }

    public function closeCaseDashboard($id)
    {
        $case = Caseq::find($id);
        $case_area_id = $case->area_id;
        $area = Area::query()->find($case_area_id);
        $rol_area = Role::where('area_id', $case_area_id)->pluck('name');
        $rol_area->prepend('no role areax');
        foreach ($rol_area as $rol) {
            if (backpack_user()->hasRole([$rol, 'Admin'])  || (backpack_user()->hasRole('Representante Atención al Cliente') && $area->name == 'Monitoreo')) {
                $case->end_date = now('America/Montevideo');
                $case->state = 'finalizado';
                $case->verifyStateAndCloseAnnouncement();
                $case->updated_by = backpack_user()->id;
                $case->verifyStateAndCloseAnnouncement();
                $case->close_cases_user_id = backpack_user()->id;
                $related_notifications = Notification::where('data', 'LIKE', '%case":' . $id . '%')->get();
                foreach ($related_notifications as $notification) {
                    $notification->delete();
                }
                $case->save();
                if (str_contains(url()->previous(), 'info'))
                    return;
                return redirect('admin/buildings-index');
            }
        }

        return response()->json([
            'message' => "No tiene permisos para cerrar el caso"
        ]);
    }

    public function closeCaseContact($id, $user_id)
    {
        $case_area_id = Caseq::where('id', $id)->first()->area_id;
        $rol_area = Role::where('area_id', $case_area_id)->pluck('name');
        $rol_area->prepend('no role areax');
        foreach ($rol_area as $rol) {
            if (backpack_user()->hasRole([$rol, 'Admin'])) {
                $cas = Caseq::where('id', $id)->first();
                $cas->end_date = now('America/Montevideo');
                $cas->state = 'finalizado';
                $cas->updated_by = backpack_user()->id;
                $cas->close_cases_user_id = backpack_user()->id;
                $related_notifications = Notification::where('data', 'LIKE', '%case":' . $id . '%')->get();
                foreach ($related_notifications as $notification) {
                    $notification->delete();
                }
                $cas->save();
                return redirect('admin/contact/' . $user_id . '/info');
            }
        }
        return view('show/cant_do');
    }

    public function openCase($id)
    {
        $case_area_id = Caseq::where('id', $id)->first()->area_id;
        $rol_area = Role::where('area_id', $case_area_id)->pluck('name');

        foreach ($rol_area as $rol) {

            if (backpack_user()->hasRole([$rol, 'Admin'])) {

                $cas = Caseq::where('id', $id)->first();
                $cas->end_date = null;
                $cas->state = 'pendiente';
                $cas->initial_date = getdate()['year'] . '-' . getdate()['mon'] . '-' . getdate()['mday'];
                $cas->updated_by = backpack_user()->id;
                $cas->save();
                return redirect('admin/case');
            }
        }
        return view('show/cant_do');
    }

    public function selectFieldsToEdit()
    {
        $can_edit = 0;
        $id = explode('/', $_SERVER["REQUEST_URI"])[3];
        $area_rol = Caseq::FindOrFail($id)->area_id;
        $rolesUser = backpack_user()->roles()->get();

        foreach ($rolesUser as $r) {
            if ($r->area_id == $area_rol || backpack_user()->hasRole('Admin')) {
                $this->crud->setValidation(CaseRequest::class);
                $fields = new CaseFields();
                $this->crud->addFields($fields->getFormFieldsUpdate(), 'both');
                $this->crud->addField([
                    // Select2
                    'type' => 'created_by',
                    'name' => 'created_by',
                    // the db column for the foreign key
                    'wrapper' => ['class' => 'form-group col-md-12'],
                ]);
                $can_edit = 1;
            }
        }

        if ($can_edit == 0) {
            $this->crud->setValidation(CaseRequest::class);
            $fields = new CaseFields();
            $this->crud->addFields($fields->getFieldsUpdateNoRole(), 'both');
        }
        Widget::add()->type('script')->content('/js/Cases/requiredFields.js');


    }

    public function typeOfRequestSend()
    {
        $can_edit = 0;
        $id = explode('/', $_SERVER["REQUEST_URI"])[3];
        $area_rol = Caseq::FindOrFail($id)->area_id;
        $rolesUser = backpack_user()->roles()->get();

        foreach ($rolesUser as $r) {
            if ($r->area_id == $area_rol || backpack_user()->hasRole('Admin')) {
                $can_edit = 1;
            }
        }
        return $can_edit;
    }

    public function caseOptions(Request $request)
    {
        $term = $request->input('term');
        $options = App\Models\Category::where('name', 'like', '%' . $term . '%')->get()->pluck('name', 'id');
        return $options;
    }

    public function show($case_id)
    {
        return redirect('/admin/case/' . $case_id . '/info');
    }

    public function deleteCaseRelated($case_id)
    {
        $case = Caseq::where('id', $case_id)->first();
        $case->parent_case_id = null;
        $case->save();
    }

    public function announcementCase($case_id, $value)
    {
        $values = [
            1 => 0,
            0 => 1
        ];
        $case = Caseq::where('id', $case_id)->first();
        $case->announcement = $values[$value];
        $case->save();
        return $values[$value];
    }

    public function updateCaseState($case_id, $state)
    {
        $case = Caseq::query()->find($case_id);

        if ($state == 'aviso_al_cliente') {
            $area = $case->area
                ->whereRaw('lower(name) = ?', ['atención'])
                ->first();
            $case->area_id = $area?->id ?? 1;
        }

        if ($state == 'finalizado') {
            $this->closeCase($case_id);
        } else {
            $case->end_date = null;
            $case->close_cases_user_id = null;
            $case->archived = false;
        }


        $case->state = $state;
        $case->updated_by = backpack_user()->id;
        $case->last_category_name = $case->last_category->name;
        $case->update();
        Caseq::sendNotificationTracingWittyBots($case);
    }

    public function getCases()
    {
        $q = request()->query('q');
        $flat = request()->route('flat_id');
        $buildingId = Flat::find($flat)?->building_id;
        $cases = $q
            ? Caseq::where('id', 'like', '%' . $q . '%')
                ->where('building_id', $buildingId)
                ->get()
            : Caseq::where('building_id', $buildingId)
                ->where('flat_id', $flat)->get();
        return response()->json($cases);
    }
}
