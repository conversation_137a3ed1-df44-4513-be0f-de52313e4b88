<?php

namespace App\Http\Controllers\Admin;

use App\Http\Fields\TagFields;
use App\Http\Requests\TagRequest;
use App\Models\Building;
use App\Models\Flat;
use App\Models\Tag;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\ReviseOperation\ReviseOperationCustom;

/**
 * Class TagCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class TagCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;

    public function setup()
    {
        $this->crud->setModel('App\Models\Tag');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/tag');
        $this->crud->setEntityNameStrings('tag', 'tags');
        $this->crud->denyAccess('show');

        if (backpack_user()->hasRole(['Admin', 'Team Leader', 'Jefe Operaciones', 'Jefe Ingeniería', 'Auxiliar Ingeniería', 'Auxiliar Ingeniería N1'])) {
            $this->crud->enableExportButtons();
            $this->crud->allowAccess('show');
        } else {
            $this->crud->denyAccess(['create', 'update', 'delete']);
        }
    }

    protected function setupListOperation()
    {
        $this->crud->addButton('line', 'delete', 'view', 'crud::buttons.deleteTag', 'end');
        $this->crud->removeButton('show');
        // $this->crud->removeButton('delete');
        $fields = new TagFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());

        $this->crud->addFilter([
            'name' => 'buildingId',
            'type' => 'select2',
            'label' => 'Edificio'
        ], function () {
            $buildingList = Building::all()->pluck('search_data', 'id')->toArray();

            //   return dd($buildingList);
            return $buildingList;
        }, function ($value) { // if the filter is active
            // return dd($value);
            $this->crud->addClause('where', 'building_id', (int)$value);
        });

        $this->crud->addFilter([
            'name' => 'status',
            'type' => 'select2',
            'label' => 'Estado'
        ], function () {
            return [
                'ACTIVE' => 'ACTIVOS',
                'INACTIVE' => 'INACTIVOS',
            ];
        }, function ($value) { // if the filter is active
            $this->crud->addClause('where', 'status', $value);
        });
    }

    protected function setupCreateOperation()
    {
        $this->crud->setValidation(TagRequest::class);
        $fields = new TagFields();
        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        $tag = Tag::find($id);
        if ($tag) {
            Tag::whereCode($tag->code)->where('id', '!=', $tag->id)->delete();
        }

        return $this->crud->delete($id);

    }

    public function deleteTag()
    {
        $this->crud->hasAccessOrFail('delete');
        $tag = Tag::findOrFail(request()->route('id'));
        if ($tag) $tag->delete();
        return;
    }


    public function store(TagRequest $request)
    {

        $tag = new Tag();
        $tag->fill($request->all());
        $tag->save();
        \Alert::add('success', '<strong>Acción completada con éxito</strong><br>Tag creado.')->flash();

        if (str_contains(url()->previous(), '/contact')) {
            return;
        }

        return redirect('admin/tag');
    }

    public function update(TagRequest $request, $id)
    {
        $tag = Tag::findOrFail($id);
        $contact_id = $tag->contact_id;
        $case_id = $tag->case_id;
        $tag->fill($request->all());
        if (!$request->input('contact_id')) {
            $tag->contact_id = $contact_id;
        }
        if (!$request->input('case_id')) {
            $tag->case_id = $case_id;
        }
        $tag->update();
        \Alert::add('success', '<strong>Acción completada con éxito</strong><br>Tag actualizado.')->flash();

        if (str_contains(url()->previous(), '/contact')) {
            return;
        }

        return redirect('admin/tag');
    }

    public function fetchCase()
    {
        return $this->fetch([
            'model' => \App\Models\Caseq::class, // required
            'query' => function ($model) {
                $search = request()->input('q') ?? false;
                if ($search) {
                    return $model->where('id', 'LIKE', '%' . $search . '%');
                } else {
                    return $model;
                }
            },
            'searchable_attributes' => []
        ]);
    }

    public function getById()
    {
        $id = request()->route('id');
        $tag = Tag::with(
            'building:id,name,building_number',
            'flat:id,number_with_tower',
            'contact:id,complete_name'
        )->find($id);


        return response()->json($tag);
    }

    public function getByFlatId($flat_id)
    {

        return response()->json(Tag::query()
            ->with('building')
            ->where('flat_id', $flat_id)
            ->get()
        );
    }
}
