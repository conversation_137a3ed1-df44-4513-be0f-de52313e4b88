<?php

namespace App\Http\Controllers\Api;

use App\Helpers\FilterData;
use App\Helpers\Paginate;
use App\Http\Requests\CaseRequest;
use App\Http\Resources\BuildingCasesResource;
use App\Http\Resources\CaseqResource;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Caseq;
use App\Models\Category;
use App\Models\RecentlyInteraction;
use App\Models\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Illuminate\Database\Eloquent\Builder;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;
use Illuminate\Http\Request;


class CaseController extends CrudController
{


    public function getCasesWithFlat($user_id, $flat_id)
    {
        $cases = Caseq::where('flat_id', $flat_id)->orWhere('user_id', $user_id)->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getCasesWithFlatNotPrincipal($user_id, $flat_id, $state = 'todos')
    {
        $search = request()->query('search');
        $cases = Caseq::query()
            ->where('user_id', $user_id)
            ->where('flat_id', $flat_id)
            ->where(function (Builder $builder) use ($state) {
                if ($state != 'todos')
                    $builder->where('state', $state);
            })
            ->latest()->get();

        $otherCasesWithoutUserId = Caseq::query()
            ->where('flat_id', $flat_id)
            ->whereNull('user_id')
            ->where(function (Builder $builder) use ($state) {
                if ($state != 'todos')
                    $builder->where('state', $state);
            })
            ->latest()->get();

        $cases = collect($cases)->concat($otherCasesWithoutUserId);

        if ($search) {
            $arrayFilter = ['id', 'description'];
            FilterData::queryData($cases, $search, $arrayFilter);
        }
        return json_encode(response(['message' => 'Lista de casos del usuario o el apartamento', 'data' => Paginate::paginate(CaseqResource::collection($cases))], 200));
    }

    public function getCasesWithFlatOpen($user_id, $flat_id)
    {
        $cases = Caseq::where('state', 'pendiente')
            ->where(function (Builder $query) use ($user_id, $flat_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('flat_id', $flat_id);
            })
            ->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getCasesWithFlatCruso($user_id, $flat_id)
    {
        $cases = Caseq::where('state', 'en_curso')
            ->where(function (Builder $query) use ($user_id, $flat_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('flat_id', $flat_id);
            })
            ->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getCasesWithFlatClose($user_id, $flat_id)
    {
        $cases = Caseq::whereIn('state', ['finalizado', 'CERRADO'])
            ->where(function (Builder $query) use ($user_id, $flat_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('flat_id', $flat_id);
            })
            ->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getCasesWithFlatWarning($user_id, $flat_id)
    {
        $cases = Caseq::where('state', 'aviso_al_cliente')
            ->where(function (Builder $query) use ($user_id, $flat_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('flat_id', $flat_id);
            })
            ->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getCasesWithFlatSInfo($user_id, $flat_id)
    {
        $cases = Caseq::where('state', 'solicitar_info')
            ->where(function (Builder $query) use ($user_id, $flat_id) {
                $query->where('user_id', $user_id)
                    ->orWhere('flat_id', $flat_id);
            })
            ->get();
        return json_encode(response(['message' => 'Lista de interacciones recientes tanto entrantes como salientes', 'data' => CaseqResource::collection($cases)], 200));
    }

    public function getInteractionsWithUser($user_id)
    {
        return json_encode(response(
            [
                'message' => 'Lista de interacciones recientes tanto entrantes como salientes',
                'data' => Paginate::paginate(RecentlyInteraction::where('caller_id', $user_id)->orWhere('receiver_id', $user_id)->get())
            ],
            200));
    }

    public function getUserInteractions($user_id)
    {
        $search = request()->query('search');
        $recentlyInteractions = RecentlyInteraction::query()
            ->where('caller_id', $user_id)
            ->orWhere('receiver_id', $user_id)
            ->latest()
            ->get();
        if ($search) {
            $arrayFilters = ['comment'];
            FilterData::queryData($recentlyInteractions, $search, $arrayFilters);
        }
        return response()->json([
            'message' => 'Lista de interacciones recientes tanto entrantes como salientes',
            'data' => Paginate::paginate($recentlyInteractions)
        ]);
    }

    public function store(Request $request)
    {
        $user = new Caseq();
        $user->fill($request->all());
        $user->type = 'contact';
        $user->save();

        return view('show.thank_you');
    }


    public function update($id, Request $request)
    {
        $user = User\User::findOrFail($id);
        $user->fill($request->all());
        $user->type = 'contact';
        $user->save();

        return view('show.thank_you');
    }

    public function list($id)
    {
        $user = User\User::findOrFail($id);

        $user->save();

        return response('User updated', 200);
    }

    public function delete($id)
    {
        $user = User\User::findOrFail($id);

        try {
            $user->delete();
            return response('User deleted', 200);

        } catch (\Exception $e) {
            return response('Could not delete user', 404);
        }

    }

    public function lastOpenCases($id)
    {

        $cases = Caseq::where('building_id', $id)
            ->whereNotIn('state', ['CERRADO', 'finalizado'])
            ->whereHas('last_category', function ($query) {
                $query->whereRaw("lower(name) not like '%comunicados%'");
            })
            ->get();
        return response(['message' => 'Lista de casos abiertos por edificio', 'data' => BuildingCasesResource::collection($cases)], 200);

    }

    public function lastOpenTracings($id)
    {
        $cases = Caseq::where('building_id', $id)
            ->where('tracing', 1)
            ->where('state', '!=', 'CERRADO')
            ->get();

        return json_encode(response(['message' => 'Lista de seguimientos abiertos por edificios', 'data' => BuildingCasesResource::collection($cases)], 200));
    }

    public function info($id)
    {
        $cases = Caseq::where('building_id', $id)->where('tracing', 0)->where('state', '!=', 'finalizado')->where('state', '!=', 'CERRADO')->get();

        return json_encode(response(['message' => 'Lista de casos abiertos por edificio', 'data' => CaseqResource::collection($cases)], 200));

    }

    public function allInfo($id)
    {
        $cases = Caseq::where('building_id', $id)->where('state', '!=', 'finalizado')->where('state', '!=', 'CERRADO')->get();

        return json_encode(response(['message' => 'Lista de casos abiertos por edificio', 'data' => CaseqResource::collection($cases)], 200));

    }

    public function quickCaseFormat($idCat)
    {
        $cat = Category::findOrFail($idCat);
        $dataString = $cat->area_id . '|' . $cat->priority . '|' . $cat->description . '|' . $cat->case_title . '|' . $cat->case_description;
        return ['id' => $cat->id, 'data' => $dataString];
    }


    public function tracing($id)
    {
        $cases = Caseq::where('building_id', $id)->where('tracing', 1)->where('state', '!=', 'finalizado')->where('state', '!=', 'CERRADO')->get();

        return json_encode(response(['message' => 'Lista de seguimientos abiertos por edificios', 'data' => CaseqResource::collection($cases)], 200));
    }


    public function userCases($id)
    {
        $cases = Caseq::where('user_id', $id)->where('state', '!=', 'finalizado')->where('state', '!=', 'CERRADO')->count();

        return $cases;
    }

    function createChildCase(Request $request)
    {
        $result = Caseq::createChildCase($request);
        return json_encode(response(['message' => 'Caso hijo creado', 'data' => $result], 200));
    }

    public function getChildsOfCase($id)
    {
        $childs_cases = Caseq::where('parent_case_id', $id)->get();
        return json_encode(response(['message' => 'Casos hijos del caso', 'data' => $childs_cases], 200));
    }

    public function getChildsOfCaseState($id, $state)
    {
        $childs_cases = Caseq::where('parent_case_id', $id)->where('state', $state)->get();
        return json_encode(response(['message' => 'Casos hijos del caso', 'data' => $childs_cases], 200));
    }

    public function makeChildExistCase(CaseRequest $request, $child_id)
    {

        $cases_id = explode(',', $child_id);

        foreach ($cases_id as $case_id) {
            $case = Caseq::findOrFail($case_id);
            $case->parent_case_id = $request->parent_case_id;
            $case->save();
        }

    }

}
