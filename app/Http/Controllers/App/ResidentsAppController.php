<?php

namespace App\Http\Controllers\App;

use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\User;
use App\Models\User\Contact;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ResidentsAppController extends CrudController
{

    public function list()
    {
        $data = \Auth::user()->residents()->get();

        return response(['message' => 'Lista de residentes de usuario', 'data' => $data], 200);

    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'surname' => 'required',
        ]);


        $user = new User\User();
        $user->fill($request->all());
        $current = User\User::findOrFail($request->current_user);
        $user->password = Hash::make($request->password ?? $request->ci);
        $user->type = 'contact';
        $user->updated_by = $current->updated_by;
        $user->created_from_app = true;
        $user->created_by = $current->id;
        $user->needs_verification = 1;
        $user->formatDocument();
        $user->formatPhones();
        $user->save();

        $buildingContact = new BuildingContact();
        $buildingContact->fill($request->all());
        $buildingContact->contact_id = $user->id;
        $buildingContact->flat_id = $current->flat_id;
        $buildingContact->building_id = $current->building_id;
        $buildingContact->main_building = 1;
        $buildingContact->intercom_name = $request->intercom_name;
        $buildingContact->intercom_incoming_call = isset($request->intercom_incoming_call) && $request->intercom_incoming_call != null ? $request->intercom_incoming_call : false;
        $buildingContact->save();

            Contact::checkIfUserHasManyBuildingsAndGenerateBuildingsString(User\User::find($user->id)) ?? Contact::checkIfUserHasBuilding(User\User::find($user->id));

        return response(['message' => 'Resident added'], 200);
    }

    public function update($id, Request $request)
    {
        $user = User\User::findOrFail($id);
        $user->fill($request->except('contact_type'));
        $user->type = 'contact';
        if ($request->password) {
            $user->password = Hash::make($request->password);
        }
        $user->formatDocument();
        $user->formatPhones();

        $current = User\User::findOrFail($request->current_user);
        $user->updated_by = $current->id;

        $buildingContact = BuildingContact::query()
            ->where('contact_id', $id)
            ->where('main_building', 1)
            ->first();
        BuildingContact::deleteIntercomContact($buildingContact);

        $buildingContact->fill($request->all());

        $buildingContact->main_building = 1;
        $buildingContact->intercom_name = $request->intercom_name;
        $buildingContact->intercom_incoming_call = $request->intercom_incoming_call ?? false;
        $buildingContact->save();

            Contact::checkIfUserHasManyBuildingsAndGenerateBuildingsString(User\User::find($id)) ?? Contact::checkIfUserHasBuilding(User\User::find($id));

        $user->save();
        BuildingContact::createDeleteIntercomContact($buildingContact);

        return response(['message' => 'Resident updated'], 200);
    }

    public function storeVideoCallSupported($id, Request $request)
    {
        $user = User\User::findOrFail($id);
        $user->device_supports_video_call = $request->videocall_supported;
        $user->save();
        return response(['message' => 'Resident updated'], 200);
    }

    public function updateLastAutoLogin($id, Request $request)
    {
        $user = User\User::findOrFail($id);
        try {
            $user->updateLastAutoLogin($request);
            return response(['message' => 'Resident last auto login updated'], 200);
        } catch (\Exception $e) {
            return response(['message' => 'Could not update Residents last auto login', 'error' => $e->getMessage()], 501);
        }
    }


    public function delete($id)
    {
        $user = User\User::findOrFail($id);
        $buildingContact = BuildingContact::where('contact_id', $id)->first();

        try {
            $user->delete();
            $buildingContact->delete();
            return response(['message' => 'Resident deleted'], 200);

        } catch (\Exception $e) {
            return response(['message' => 'Could not delete Resident'], 501);
        }

    }
}
