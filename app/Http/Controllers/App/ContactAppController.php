<?php

namespace App\Http\Controllers\App;

use App\Http\Fields\Api\ContactFields;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Imports\ContactsImport;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\User;
use App\Notifications\RegisterUser;
use App\Notifications\UserWithoutMailRegistered;
use App\Services\DataTransformerAuthorizationService;
use App\Services\DataTransformerResidentService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Mockery\Exception;


/**
 * Class UserCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class ContactAppController extends CrudController
{

    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use Notifiable;

    public function setup()
    {
        $this->crud->setModel('App\Models\User\Contact');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/api/contact');
        $this->crud->setEntityNameStrings('contacto', 'contactos');
        $this->crud->denyAccess('show');

    }

    protected function setupCreateOperation()
    {
        $fields = new \App\Http\Fields\ContactFields();
        $this->crud->addFields($fields->getFormFields(), 'both');

    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function list()
    {
        return \App\Models\User\User::all();
    }

    public function listAuthorizations($id)
    {
        $data = User\Contact::findOrFail($id)->authorizations()->get();

        return response(['message' => 'Lista de autorizaciones de usuario', 'data' => $data], 200);

    }

    public function create()
    {
        $current_contact = null;

        return view('create/contact', compact('current_contact'));
    }

    public function edit($token)
    {

        $current_contact = User\User::where('token', $token)->first();

        if ($current_contact) {
            $current_contact = $current_contact->id;

            return view('update/contact', compact('current_contact'));
        }
        return abort(404, 'Usuario sin token');
    }

    public function register($email)
    {
        return view('register/contact', compact('email'));
    }


    public function passwordReset()
    {
        return view('login/password-reset');
    }

    public function showRegistrationMailView()
    {
        return view('show.send_register_mail');
    }

    public function sendRegistrationMail(Request $request)
    {
        $type = $request->type;
        $ids = $request->ids;

        return User\Contact::sendRegistrationMail($type, $ids);
    }

    public function store(Request $request)
    {
        $user = new User\Contact();
        $user->fill($request->all());
        $user->type = 'contact';
        $user->needs_verification = true;
        $user->password = Hash::make($request->password);
        $user->birthday = null;
        $user->formatDocument();
        $user->save();
        $request->id = $user->id;

        $buildingContact = new BuildingContact();
        $buildingContact->fill($request->except('announcements'));
        $buildingContact->building_id = null;
        $buildingContact->flat_id = null;
        $buildingContact->contact_id = $user->id;
        $buildingContact->phone_mobile = BuildingContact::trimAndReturnPhone($request->phone_mobile);
        $buildingContact->phone_home= BuildingContact::trimAndReturnPhone($request->phone_home);
        $buildingContact->save();

        $toNotify = \App\Models\User\User::findOrFail(config('constants.foxsys_user')); //Tamara - for testing purposes only
        $toNotify->notify(new UserWithoutMailRegistered($user->id, $request->all()));

        return view('show.thank_you_registered');
    }


    public function update($id, Request $request)
    {
        $user = User\User::findOrFail($id);
        $user->fill($request->all());
        $user->type = 'contact';
        $user->formatDocument();
        $user->formatPhones();
        $user->password = Hash::make($request->password);
        $user->save();

        return view('show.thank_you');
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        try {
            $this->crud->delete($id);
            \Alert::add('success', '<strong>Acción completada con exito</strong><br>Contacto eliminado.')->flash();
            return view('show.unregistered');
        } catch (Exception $e) {
            \Alert::add('error', '<strong>No se ha podido eliminar</strong><br>Intente más tarde.')->flash();
            echo $e;
        }
    }

    public function updatePassword(Request $request)
    {
        return view('update/contact-password');
    }


    public function authorizations($id)
    {
        $data = User\Contact::findOrFail($id)->authorizations()->get();
        $userDataTransformer = new DataTransformerAuthorizationService();
        $transformedData = $userDataTransformer->transformContactData($data);
        return response(['message' => 'Lista de autorizaciones de usuario', 'data' => $transformedData], 200);

    }

    public function residents($id)
    {
        $contacts = User\Contact::findOrFail($id)->residents()->get();
        $transformer = new \App\Services\DataTransformerResidentService();
        $data = $transformer->transformContactData($contacts);
        return response()->json(['message' => 'Lista de residentes de usuario', 'data' => $data], 200);
    }

}
