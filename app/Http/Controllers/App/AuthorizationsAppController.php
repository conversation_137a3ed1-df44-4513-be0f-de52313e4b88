<?php

namespace App\Http\Controllers\App;

use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\User;
use App\Models\User\Contact;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Illuminate\Http\Request;

class AuthorizationsAppController extends CrudController
{

    public function list()
    {
        $data = \Auth::user()->authorizations()->get();

        return response(['message' => 'Lista de autorizaciones de usuario', 'data' => $data], 200);

    }

    public function store(Request $request)
    {
        $userBuildingContact = new BuildingContact();
        $user = new User\User();

        $request = $request->all();

        if (isset($request['schedule'])) {
            $userBuildingContact->schedule = $request['schedule'];
        }

        $user->fill($request);
        $userBuildingContact->fill($request);
        $userBuildingContact->complete_name = $request['name'] . ' ' . $request['surname'];
        $user->password = bcrypt($request['name']);
        $user->type = 'contact';
        $user->save();

        $currentUser = BuildingContact::where('contact_id', $request['current_user'])->first();
        $userBuildingContact->flat_id = $currentUser->flat_id;
        $userBuildingContact->building_id = $currentUser->building_id;
        $userBuildingContact->contact_id = $user->id;
        $userBuildingContact->main_building = 1;
        $user->created_from_app = true;
        $userBuildingContact->contact_type = $request['contact_type'] ?? 'Autorizado';
        $user->created_by = $currentUser->contact_id;

        $user->formatDocument();
        $user->formatPhones();
            Contact::checkIfUserHasManyBuildingsAndGenerateBuildingsString(User\User::find($user->id)) ?? Contact::checkIfUserHasBuilding(User\User::find($user->id));
        $user->save();
        $userBuildingContact->save();
        return response(['message' => 'Authorized user added'], 200);
    }

    public function update($id, Request $request)
    {
        $userBuildingContact = BuildingContact::where('contact_id', $id)
            ->where('main_building', 1)
            ->first();
        $user = User\User::findOrFail($id);
        $user->fill($request->all());
        $userBuildingContact->fill($request->all());

        $user->type = 'contact';
        $userBuildingContact->contact_type = $request->contact_type ?? 'Autorizado';
        $current = User\User::findOrFail($request->current_user);
        $user->updated_by = $current->id;
        $user->formatDocument();
        $user->formatPhones();
            Contact::checkIfUserHasManyBuildingsAndGenerateBuildingsString(User\User::find($id)) ?? Contact::checkIfUserHasBuilding(User\User::find($id));
        $user->save();
        $userBuildingContact->save();

        return response(['message' => 'Authorized user updated'], 200);

    }

    public function delete($id)
    {
        $user = User\User::findOrFail($id);
        try {
            BuildingContact::query()
                ->where('contact_id', $id)
                ->where('flat_id', $user->flat_id)
                ->where('building_id', $user->building_id)
                ->delete();
            $buildingContact = BuildingContact::query()
                ->where('contact_id', $id)
                ->first();
            if ($buildingContact) {
                $buildingContact->main_building = true;
                $buildingContact->save();
            } else {
                $user->delete();
            }

            return response(['message' => 'Authorized user deleted'], 200);

        } catch (\Exception $e) {
            return response(['message' => 'Could not delete Authorized user'], 404);
        }

    }
}
