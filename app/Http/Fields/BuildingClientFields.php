<?php

namespace App\Http\Fields;

use App\Models\Utilities\Country;
use Carbon\Carbon;


class BuildingClientFields extends BuildingFields
{
    public function getFormFields()
    {
        return [
            [   // Upload
                'name'      => 'order_job_checked',
                'label'     => 'Orden de trabajo revisada',
                'type'      => 'upload',
                'upload'    => true,
                'disk'      => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-6'],
            ],
            [   // Upload
                'name'      => 'rev_security',
                'label'     => 'Relevamiento de seguridad',
                'type'      => 'upload',
                'upload'    => true,
                'disk'      => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-6'],
            ]
            ];
    }

}
