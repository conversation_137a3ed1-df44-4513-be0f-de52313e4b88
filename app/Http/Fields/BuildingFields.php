<?php

namespace App\Http\Fields;

use App\Models\Building;
use App\Models\Utilities\Country;
use Carbon\Carbon;


class BuildingFields extends AbstractFields
{
    public function getFormFields()
    {
        return [
            // image
            [
                'label' => "",
                'name' => "image",
                'type' => 'image_custom_building',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'wrapper' => ['class' => 'form-group col-md-2'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Imagen',
                ],
            ],

            [   // Number
                'name' => 'building_number',
                'label' => '',
                'type' => 'building_number',
                'default' => Building::where('building_number', '>', '0')->orderBy('building_number', 'desc')->first()->building_number + 1,
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Número edificio',
                    'class' => 'text-center create_edit_back_color',
                    'style' => '',
                ],
            ],
            [
                'name' => 'name', // The db column name
                'label' => "", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-7'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Nombre del edificio',
                    'placeholder' => 'Nombre del edificio',
                    'style' => 'margin-top: 9px; width: 115%; margin-left: -41px;',
                ],
            ],
            [   // Label
                'name' => 'Datos Generales', // the name of the db column
                'icon' => 'information-circle',
                'type' => 'ion_icons',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'building_state',
                'type' => 'dropdown',
                'dropdown_items' => 'BUILDING_STATE',
                'default' => 'installation',
                'attributes' => [
                    'column-order' => 1,
                    'class' => 'dropdown-building',
                    'style' => 'padding:0'
                ],
            ],
            [   // radio
                'name' => 'building_type', // the name of the db column
                'label' => 'Tipo de edificio', // the input label
                'type' => 'radio_mariano_style',
                'inline' => true,
                'default' => "Residencial",
                'wrapper' => ['class' => 'col-md-12'],
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    "Residencial" => "Residencial",
                    "Oficina" => "Oficina",
                ],
                // optional
                'attributes' => [
                    'column-order' => 1,
                    'class' => 'bool_fields_back',
                    'required' => true,
                ],
            ],
            [   // Table
                'name' => 'address',
                'label' => 'Dirección',
                'type' => 'table_mariano_style',
                'entity_singular' => 'Dirección', // used on the "Add X" button
                'columns' => [
                    'street' => 'Calle',
                    'number' => 'Número',
                    'between_streets' => 'Esquina'
                ],
                'max' => 4, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'city', // The db column name
                'label' => "Ciudad", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-8'],
                'attributes' => [
                    'column-order' => 1,
                    'placeholder' => 'Ciudad',
                ],

            ],
            [
                'name' => 'postal_code', // The db column name
                'label' => "Código postal", // Table column heading
                'type' => "number",
                'thousands_sep' => '',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 1,
                    'placeholder' => 'Número',
                    'style' => 'margin-left: -15px; width: 112%;',
                ],
            ],
            [
                'name' => 'agreed_start_date',
                'label' => 'Fecha de inicio acordada',
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'agreed_start _date',
                ],
            ],
            [
                'name' => 'agreed_start_time',
                'label' => 'Hora de inicio acordada',
                'type' => "time",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'agreed_start _date',
                ],
            ],
            [
                'name' => 'estimated_start_date',
                'type' => "estimated_start_date_building",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'width: 100%!important;text-align: left;',
                ],
            ],
            [   // Label
                'name' => 'Instalación', // the name of the db column
                'icon' => 'construct',
                'type' => 'ion_icons',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'installation_date', // The db column name
                'label' => "Instalación acordada", // Table column heading
                'type' => "date_change_all_dates",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'installation_time', // The db column name
                'label' => "Restricciones de horas", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Label
                'name' => 'warranty_date', // the name of the db column
                'type' => 'building_equipament',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Servicio', // the name of the db column
                'icon' => 'shield',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'service_start_date', // The db column name
                'label' => "Inicio de servicio", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'service_start_date',
                    'onchange' => 'getDateWarranty()',
                ],
            ],

            [   // radio
                'name' => 'service_type', // the name of the db column
                'label' => 'Tipo de servicio', // the input label
                'type' => 'space_radio',
                'default' => 'Portería',
                'options' => [Building::$buildingServiceType],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'color: blue;',
                    'required' => true,
                ],
            ],
            [
                'name' => 'initial_date', // The db column name
                'label' => "Datos iniciales", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: -60px;'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Hidden
                'name' => 'space-initial_date',
                'type' => 'hidden',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'value' => 'active',
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'service_level', // The db column name
                'label' => "Nivel de servicio", // Table column heading
                'type' => 'select_from_array',
                'options' => ['bajo' => 'Bajo', 'medio' => 'Medio', 'alto' => 'Alto'],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'before_delivery', // The db column name
                'label' => "Pre-entrega", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'schedule',
                'label' => 'Horarios del servicio',
                'type' => 'repeatable_mariano_style',
                'fields' => [
                    [   // Checkbox
                        'name' => 'inlineCheckbox1',
                        'label' => 'L &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-right: 25px;'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox2',
                        'label' => 'M &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday2'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox3',
                        'label' => 'X &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday3'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox4',
                        'label' => 'J &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday4'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox5',
                        'label' => 'V &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday5'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox6',
                        'label' => 'S &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox7',
                        'label' => 'D',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-left: 25px;', 'id' => 'inlinecheckday7'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timei1ser',
                        'type' => 'time',
                        'label' => 'Horario inicial',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timef1ser',
                        'type' => 'time',
                        'label' => 'Horario final',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                ],
                'new_item_label' => 'Añadir horario', // customize the text of the button
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],

            [   // Label
                'name' => 'Llaves', // the name of the db column
                'icon' => 'key',
                'type' => 'label',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'tags_delivery_by', // the name of the db column
                'label' => 'Distribución por', // the input label
                'type' => 'radio_mariano_style',
                'default' => 'Foxsys',
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    'Foxsys' => "Foxsys",
                    'Portero' => "Portero edificio"
                ],
                // optional
                'inline' => true, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                    'required' => true,
                ],
            ],
            [
                'name' => 'tags_delivery_date', // The db column name
                'label' => "Fecha acordada para distribución de llaveros", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_porter',
                'label' => 'Porteria',
                'type' => 'counter',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_admin',
                'label' => 'Admin',
                'type' => 'counter2',
                'wrapper' => ['class' => 'form-group col-md-3', 'title' => 'Administración'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_clear',
                'label' => 'Limpieza',
                'type' => 'counter3',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],


            [   // Number
                'name' => 'extra_keys_other',
                'label' => 'Otros',
                'type' => 'counter4',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            //*****************************************************************END FIRST COLUMN

            [   // Label
                'name' => 'Características del edificio', // the name of the db column
                'icon' => 'business',
                'type' => 'ion_icons',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'door_special_denomination', // the name of the db column
                'label' => 'Torres:', // the input label
                'type' => 'switch_button4',
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'tower_info',
                'label' => '',
                'type' => 'repeatable_tower',
                'fields' => [
                    [
                        'name' => 'id', // The db column name
                        'label' => "", // Table column heading
                        'type' => "hidden",
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'flats', // The db column name
                        'label' => "Apartamentos", // Table column heading
                        'type' => "textarea_flat",
                        'wrapper' => ['class' => 'form-group col-md-12 switch-button-of-denomination-for-tower-in-building', 'style' => 'padding: 0px; margin-left: -1px;'],
                        'attributes' => [
                            'column-order' => 2,
                            'class' => 'form-control white-field',
                        ],
                    ],
                    [
                        'name' => 'tower_denomination', // The db column name
                        'label' => "Denominación torres", // Table column heading
                        'type' => "text_hidden_if_tower_of",
                        'wrapper' => ['class' => 'form-group col-md-6 element-to-show-when-switch-tower-on', 'style' => 'display:none;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'tower_comment', // The db column name
                        'label' => "Comentarios de torres", // Table column heading
                        'type' => "text_hidden_if_tower_of",
                        'wrapper' => ['class' => 'form-group col-md-6 element-to-show-when-switch-tower-on', 'style' => 'display:none;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'doors_quantity', // The db column name
                        'label' => "Puertas", // Table column heading
                        'type' => "number_in_tower",
                        'default' => 1,
                        'wrapper' => ['class' => 'form-group col-md-6 doors_quantity'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'tower_access', // The db column name
                        'label' => "Com. de acceso", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'tower_address', // The db column name
                        'label' => "Dirección", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'tower_corner', // The db column name
                        'label' => "Esquina", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'opens_from_flat', // The db column name
                        'label' => "Abrir desde arriba", // Table column heading
                        'type' => 'radio',
                        'default' => 'no',
                        'options' => ['si' => 'Si', 'no' => 'No'],
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'inline' => true, // show the radios all on the same line?
                        'attributes' => [
                            'class' => 'white-field form-control bool_fields_back',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [   // radio
                        'name' => 'lock', // the name of the db column
                        'label' => 'Cerradura', // the input label
                        'type' => 'radio',
                        'inline' => true, // show the radios all on the same line?
                        'options' => [
                            // the key will be stored in the db, the value will be shown as label;
                            1 => "Si",
                            null => "No"
                        ],
                        // optional
                        //'inline'      => false, // show the radios all on the same line?
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'class' => 'white-field form-control bool_fields_back',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [
                        'name' => 'doorman_format', // The db column name
                        'label' => "¿Cómo llaman desde abajo?", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-12'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [   // radio
                        'name' => 'allows_elevator_comunication', // the name of the db column
                        'label' => '¿Permite comunicado en el ascensor?:', // the input label
                        'type' => 'radio',
                        'default' => '0',
                        'inline' => true, // show the radios all on the same line?
                        'options' => [
                            // the key will be stored in the db, the value will be shown as label;
                            1 => "Si",
                            0 => "No",

                        ],
                        // optional
                        //'inline'      => false, // show the radios all on the same line?
                        'wrapper' => ['class' => 'form-group col-md-12'],
                        'attributes' => [
                            'column-order' => 2,
                            'required' => 'required',
                        ],
                    ],
                    [
                        'name' => 'measures_locations', // The db column name
                        'label' => "Ubicación de medidores", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding-bottom:25px;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'gates_ramps_controllers', // The db column name
                        'label' => "Control de Garaje", // Table column heading
                        'type' => 'radio',
                        'default' => 'no',
                        'inline' => true,
                        'options' => ['si' => 'Si', 'no' => 'No'],
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => [
                            'Class' => 'white-field form-control',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [
                        'name' => 'gates_ramps_denomination', // The db column name
                        'label' => "Denominación", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-8'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],

                ],
                'min_rows' => 1, // minimum rows allowed, when reached the "delete" buttons will be hidden
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Upload
                'name' => 'gates_ramps_file',
                'label' => 'Instructivo de manipulacion para garaje',
                'type' => 'upload',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Delivery', // the name of the db column
                'icon' => 'dolly-flatbed ',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'delivery', // the name of the db column
                'label' => '', // the input label
                'type' => 'radio',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Permitido",
                    0 => "No permitido",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // radio
                'name' => 'delivery_comment', // the name of the db column
                'label' => 'Comentario de delivery', // the input label
                'type' => 'text_with_info_bellow',
                'text_info' => 'Información relevante para Monitoreo o Atención a clientes',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Acceso de Cadetes (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-cube la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'cadete_hall', // the name of the db column
                'label' => 'Hall', // the input label
                'type' => 'radio-inline-building-cadete-hall',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'cadete_hall_comments',
                'type' => 'repeatable_custom_comments_cadete_hall',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'label' => '',
                        'type' => "text_with_close_button_area",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'cadete_up', // the name of the db column
                'label' => 'Subir', // the input label
                'type' => 'radio-inline-building-cadete-up',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'cadete_up_comments',
                'type' => 'repeatable_custom_comments_cadete_up',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],


            [   // Label
                'name' => 'Seguridad', // the name of the db column
                'icon' => 'shield',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'security_comments',
                'label' => "Comentarios de seguridad",
                'type' => "input_security_alert_building",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'fields_included' => [0 => 'security_comments_2', 1 => 'security_comments_3'],
                'text_info' => "Información de alerta",
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'description_field',
                ],

            ],

            [   // repeatable
                'name' => 'comments',
                'label' => 'Exepciones',
                'type' => 'repeatable_custome_comments',
                'new_item_label' => 'Nuevo comentario',
                'max_rows' => 3,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'com_com', // The db column name
                        'label' => "", // Table column heading
                        'type' => "text_with_info_bellow_area",
                        'text_info' => "Otra información",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],


            [
                'name' => 'Acceso de Personas (Nuevo)',
                'icon' => 'people',
                'type' => 'ion_icons',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;color:var(--ColorGreenTitle)',
                ],
                'wrapper' => ['class' => 'title-access-people'],
            ],
            [   // Label
                'name' => 'Instrucciones y/o excepciones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'instructions_explanations',
                'type' => 'repeatable_custom_comments_building_people_access',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'instructions_explanations', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'amenities', // the name of the db column
                'label' => 'Amenities', // the input label
                'type' => 'radio-inline-building-amenities',
                'inline' => true,
                'default' => 0,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'instructions_explanations_comments',
                'type' => 'repeatable_custom_comments_building_amenities',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'instructions_explanations_comments', // The db column name
                        'label' => '',
                        'type' => "text_with_close_button_area",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],

            [   // Label
                'name' => 'Acceso de Servicios (Nuevo)',
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-user-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Instrucciones y/o excepciones ', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'services_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Particularidades (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-info-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Registros', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'records_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Tags', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'tags_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Investigaciones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'investigations_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Otros', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'others_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            // ******************************************************* END COLUMNA 2
            [
                'name' => 'gamma_code', // The db column name
                'label' => "Respuesta física", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'placeholder' => 'Escribir respuesta fisica',
                ],
            ],
            [   // Label
                'name' => 'Administración y Servicios', // the name of the db column
                'icon' => 'info-circle',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Table
                'name' => 'porter',
                'label' => 'Porteros',
                'type' => 'dor_man_building',
                'entity_singular' => 'Portero', // used on the "Add X" button
                'columns' => [
                    'name' => 'Nombre',
                    'ci' => 'C.I',
                    'hour' => 'Horario (Ej: "L a V de 15:00 a 20:00 y 22:00 a 02:00 / S y D de 12:00 a 18:00")',
                    'number' => 'Número'
                ],
                'max' => 8, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Table
                'name' => 'cleaning_time',
                'label' => 'Limpieza',
                'type' => 'table_cleaning_time_building',
                'entity_singular' => 'Limpieza', // used on the "Add X" button
                'columns' => [
                    'name' => 'Nombre',
                    'ci' => 'C.I',
                    'hour' => 'Horario (Ej: "L a V de 15:00 a 20:00 y 22:00 a 02:00 / S y D de 12:00 a 18:00")',
                    'number' => 'Número'
                ],
                'max' => 8, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [    // Select2Multiple = n-n relationship (with pivot table)
                'label' => "Contactos referentes",
                'type' => 'select2_multiple_filtering_contacts_in_building',
                'name' => 'referents', // the method that defines the relationship in your Model
                // optional
                'model' => "App\Models\user\Contact", // foreign key model
                'attribute' => 'complete_name', // foreign key attribute that is shown to user
                'pivot' => true, // on create&update, do you need to add/delete pivot table entries?
                // 'select_all' => true, // show Select All and Clear buttons?
                // optional
                'options' => true,
                'attributes' => [
                    'column-order' => 3,
                ], // force the related options to be a custom query, instead of all(); you can use this to filter the results show in the select
            ],

            [
                'type' => "select2_multiple_filtering_contacts_in_building",
                'label' => "Contactos de comisión",
                'name' => 'commissions',
                'options' => true,
                'entity' => 'commissions',
                'attribute' => "complete_name",
                'ajax' => true,
                'pivot' => true,
                'inline_create' => ['entity' => 'contact'], // specify the entity in singular
                'allow_clear' => false, // specify the entity in singular
                'model' => "App\Models\User\Contact",
                // that way the assumed URL will be "/admin/tag/inline/create"
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'class' => 'form-control white-field',
                ],
            ],
            [
                'type' => "select2",
                'name' => 'administration_id',
                'label' => "Administradoras",
                'link_label' => "+Nueva Administradora",
                'link_href' => "/admin/administrator/create",
                'entity' => 'administration',
                'model' => "App\Models\Company\Administration",
                'attribute' => "name",
                'ajax' => true,
                'inline_create' => true, // specify the entity in singular
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'class' => 'form-control white-field',
                ],

            ],
            [
                'type' => "select2_multiple",
                'label' => "Servicios y Proveedores",
                'link_label' => "+Nuevo Servicio",
                'link_href' => "/admin/service/create",
                'name' => 'services', // the method on your model that defines the relationship
                'attribute' => "full_name",
                'ajax' => true,
                'inline_create' => ['entity' => 'service'], // specify the entity in singular
                'allow_clear' => false, // specify the entity in singular
                // that way the assumed URL will be "/admin/tag/inline/create"
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'rows' => 5,
                ],
            ],
            [   // Label
                'name' => 'Normas generales (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-file-alt la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Normas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'rules_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Gestiones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'arrangements_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Averías', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'malfunctions_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Mudanzas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'moving_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Información general (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-info-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Locales comerciales', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'commercial_locals_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Baño', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'bathrooms_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Medidores',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'medidores_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Azotea',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'rooftop_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Alarma incendio',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'fire_alarm_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Otras alarmas',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'others_alarms_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Instrucciones adicionales',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'aditional_instructions_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Medidores',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [
                'name' => 'medidores_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment',
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            // Kazoo
            [
                'name' => 'Kazoo',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'Kazoo',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-top: 5%',
                ],
            ],
            [
                'name' => 'account_kazoo',
                'label' => "Cuenta",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],

            ],
            [
                'name' => 'domain_kazoo',
                'label' => "Dominio",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'margin-bottom: 3%'
                ],

            ],
            [
                'name' => 'own_foxsys',
                'label' => 'Interno Foxsys',
                'type' => 'margin_own_foxsys',
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'number_of_internet_contrat',
                'label' => "Nº de contrato Internet",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding: 0 20px'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'order_job',
                'label' => 'Orden de trabajo',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'order_job_checked',
                'label' => 'Orden de trabajo revisada',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'contract',
                'label' => 'Contrato',
                'type' => 'upload_only_comercial',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security',
                'label' => 'Relevamiento de seguridad',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security2',
                'label' => 'Relevamiento de seguridad 2',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security2'
                ],
            ],
            [   // Upload
                'name' => 'rev_security3',
                'label' => 'Relevamiento de seguridad 3',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security3'
                ],
            ],
            [   // Upload
                'name' => 'rev_security4',
                'label' => 'Relevamiento de seguridad 4',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security4'
                ],
            ],
            [   // Label
                'name' => '+ Añadir relavamiento', // the name of the db column
                'icon' => '',
                'type' => 'label_link',
                'large-row' => 'col-md-12',
                'wrapper' => [
                    'style' => 'margin-botton: 15px'
                ],
                'attributes' => [
                    'column-order' => 3,
                    'style' => '',
                ],
            ],
            [
                'name' => 'app_foxsys', // The db column name
                'label' => "App Foxsys", // Table column heading
                'type' => "switch_button",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'open_door_disabled', // The db column name
                'label' => "Abrir desde app deshabilitado", // Table column heading
                'type' => "switch_button2",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [
                'name' => 'video_disabled', // The db column name
                'label' => "Videollamada de app deshabilitada", // Table column heading
                'type' => "switch_button3",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'access_by_pin', // the name of the db column
                'label' => "Acceso por PIN",
                'type' => "switch_button_PIN",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'access_by_pin'
                ],
            ],
            [
                'name' => 'incoming_call', // The db column name
                'label' => "Timbre Directo Activado", // Table column heading
                'type' => "switch_button_building_incoming_call",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'incoming_call'
                ],
            ],
            [
                'name' => 'face_recognition', // The db column name
                'label' => "Reconocimiento facial", // Table column heading
                'type' => "switch_button_face_recognition",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'face_recognition'
                ],
            ],
            [
                'name' => 'camera_anpr', // The db column name
                'label' => "Cámara ANPR", // Table column heading
                'type' => "switch_button_camera_anpr",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12', ],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'incoming_call',
                ],
            ],
            [  // Select
                'label' => "Ejecutivo Comercial",
                'type' => 'select',
                'name' => 'operator_id', // the db column for the foreign key

                'entity' => 'comercial',
                'allows_null' => false,
                'default' => backpack_user()->id == 30911 ? 30911 : 30910,

                // optional - manually specify the related model and attribute
                'model' => "App\Models\User\Comercial", // related model
                'attribute' => 'name', // foreign key attribute that is shown to user

                // optional - force the related options to be a custom query, instead of all();
                'options' => (function ($query) {
                    return $query->orderBy('name', 'ASC')->get();
                }), //  you can use this to filter the results show in the select
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding: 0 20px;'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 3,
                ],
            ],

            [   // Label
                'name' => 'Equipamiento y Datos de Foxsys (Nuevo)', // the name of the db column
                'type' => 'building-edit-equipment-foxsys',
                'icon' => 'la la-camera la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Teléfonos', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'phones_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'phone', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Altoparlantes', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'speakers_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'speaker', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Sirenas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'sirens_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'siren', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Alarmas integradas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'integrated_alarms_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'integrated_alarm', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [  // Select
                'type' => 'created_by',
                'name' => 'created_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [  // Select
                'type' => 'updated_by',
                'name' => 'updated_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [  // Select
                'type' => 'hidden',
                'name' => 'id', // the db column for the foreign key
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

        ];
    }

    public function getFormFieldsTechnic()
    {
        return [
            [   // Label
                'name' => 'General', // the name of the db column
                'icon' => 'city',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'name', // The db column name
                'label' => "Nombre", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'readonly' => 'readonly',
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'operator_id', // The db column name
                'label' => "Ejecutivo comercial", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'readonly' => 'readonly',
                    'Class' => 'white-field form-control',
                    'column-order' => 1,

                ],
            ],
            [   // radio
                'name' => 'building_type', // the name of the db column
                'label' => 'Tipo de edificio', // the input label
                'type' => 'radio',
                'wrapper' => ['class' => 'col-md-6'],
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    "Oficina" => "Oficina",
                    "Residencial" => "Residencial",

                ],
                'attributes' => [
                    'readonly' => 'readonly',
                    'disabled' => 'disabled',
                    'column-order' => 1,

                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
            ],
            [   // Label
                'name' => 'Fechas', // the name of the db column
                'icon' => 'calendar',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'installation_date', // The db column name
                'label' => "Fecha de instalación", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'warranty_date', // the name of the db column
                'type' => 'building_equipament',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'before_delivery', // The db column name
                'label' => "Fecha de Pre-entrega", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'service_start_date', // The db column name
                'label' => "Fecha acordada de inicio de servicio", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'id' => 'service_start_date',
                    'column-order' => 2,
                    'onchange' => 'getDateWarranty()',
                ],
            ],
            [   // Label
                'name' => 'Interno Foxsys', // the name of the db column
                'icon' => 'info-circle',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Upload
                'name' => 'order_job_checked',
                'label' => 'Orden de trabajo revisada',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security',
                'label' => 'Relevamiento de seguridad',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security2',
                'label' => 'Relevamiento de seguridad 2',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security2'
                ],
            ],
            [   // Upload
                'name' => 'rev_security3',
                'label' => 'Relevamiento de seguridad 3',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security3'
                ],
            ],
            [   // Upload
                'name' => 'rev_security4',
                'label' => 'Relevamiento de seguridad 4',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security4'
                ],
            ],
            [   // Label
                'name' => '+ Añadir relavamiento', // the name of the db column
                'icon' => '',
                'type' => 'label_link',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => '',
                ],
            ],
            [   // Label
                'name' => 'Kazoo', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Kazoo', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-top: 5%',
                ],
            ],
            [
                'name' => 'account_kazoo', // The db column name
                'label' => "Cuenta", // Table column heading
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],

            ],
            [
                'name' => 'domain_kazoo', // The db column name
                'label' => "Dominio", // Table column heading
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'margin-bottom: 3%'
                ],

            ],
        ];
    }

    public function getColumnsFields()
    {
        return [
            [   // Number
                'name' => 'building_number',
                'label' => '&nbsp;&nbsp;#',
                'type' => 'text',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('buildings.' . $column['name'], 'like', '%' . $searchTerm . '%');
                },
            ],
            [
                'name' => 'name',
                'label' => 'Nombre',
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'visibleInExport' => false,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('buildings.' . $column['name'], 'like', '%' . $searchTerm . '%');
                },

            ],
            [
                'name' => 'service_type', // The db column name
                'label' => "Tipo de servicio", // Table column heading
                'type' => "service_type_column",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'options' => Building::$buildingServiceType,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('buildings.' . $column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'building_type', // The db column name
                'label' => "Tipo de edificio", // Table column heading
                'type' => "building_type_column",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('buildings.' . $column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'address_fake', // The db column name
                'label' => "Dirección", // Table column heading
                'type' => "text",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('buildings.' . 'address', 'like', '%' . $searchTerm . '%');
                }
            ], [
                'name' => 'building_cases', // The db column name
                'label' => "Casos abiertos", // Table column heading
                'type' => "building_cases",
            ],

        ];
    }

    public function getShowFields()
    {
        return [
            [
                'label' => "Profile Image",
                'name' => "image",
                'type' => 'image',
                'height' => '330px',
            ],
            [
                'name' => 'name',
                'label' => 'Nombre',
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }

            ],
            [
                'name' => 'building_type', // The db column name
                'label' => "Tipo de edificio", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'service_type', // The db column name
                'label' => "Tipo de servicio", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'address', // The db column name
                'label' => "Dirección", // Table column heading
                'type' => "text",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'between_streets', // The db column name
                'label' => "Entre Calles", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'phone', // The db column name
                'label' => "Teléfono", // Table column heading
                'type' => "number",
                'thousands_sep' => '',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],


        ];
    }

    public function getFormFieldsSpecial()
    {
        return [
            // image
            [
                'label' => "",
                'name' => "image",
                'type' => 'image_custom_building',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'wrapper' => ['class' => 'form-group col-md-2'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Imagen',
                ],
            ],

            [   // Number
                'name' => 'building_number',
                'label' => '',
                'type' => 'number',
                'default' => Building::where('building_number', '>', '0')->orderBy('building_number', 'desc')->first()->building_number + 1,
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Número edificio',
                    'class' => 'text-center create_edit_back_color',
                    'style' => 'width: 60px; height: 47px;border-radius: 5px; padding: 16px; margin-left: -10px; margin-top: 22px;',
                ],
            ],
            [
                'name' => 'name', // The db column name
                'label' => "", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-7'],
                'attributes' => [
                    'column-order' => 1,
                    'title' => 'Nombre del edificio',
                    'placeholder' => 'Nombre del edificio',
                    'style' => 'margin-top: 9px; width: 115%; margin-left: -41px;',
                ],
            ],
            [   // Label
                'name' => 'Datos Generales', // the name of the db column
                'icon' => 'city',
                'type' => 'label',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'building_state',
                'type' => 'dropdown',
                'dropdown_items' => 'BUILDING_STATE',
                'default' => 'installation',
                'attributes' => [
                    'column-order' => 1,
                    'class' => 'dropdown-building',
                    'style' => 'padding:0'
                ],
            ],
            [   // radio
                'name' => 'building_type', // the name of the db column
                'label' => 'Tipo de edificio', // the input label
                'type' => 'radio_mariano_style',
                'inline' => true,
                'default' => "Residencial",
                'wrapper' => ['class' => 'col-md-12'],
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    "Residencial" => "Residencial",
                    "Oficina" => "Oficina",
                ],
                // optional
                'attributes' => [
                    'column-order' => 1,
                    'class' => 'bool_fields_back',
                    'required' => true,
                ],
            ],
            [   // Table
                'name' => 'address',
                'label' => 'Dirección',
                'type' => 'table_mariano_style',
                'entity_singular' => 'Dirección', // used on the "Add X" button
                'columns' => [
                    'street' => 'Calle',
                    'number' => 'Número',
                    'between_streets' => 'Esquina'
                ],
                'max' => 4, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'city', // The db column name
                'label' => "Ciudad", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-8'],
                'attributes' => [
                    'column-order' => 1,
                    'placeholder' => 'Ciudad',
                ],

            ],
            [
                'name' => 'postal_code', // The db column name
                'label' => "Código postal", // Table column heading
                'type' => "number",
                'thousands_sep' => '',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 1,
                    'placeholder' => 'Número',
                    'style' => 'margin-left: -15px; width: 112%;',
                ],
            ],
            [
                'name' => 'agreed_start_date',
                'label' => 'Fecha de inicio acordada',
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'agreed_start _date',
                ],
            ],
            [
                'name' => 'agreed_start_time',
                'label' => 'Hora de inicio acordada',
                'type' => "time",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'agreed_start _date',
                ],
            ],
            [
                'name' => 'estimated_start_date',
                'type' => "estimated_start_date_building",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'width: 100%!important;text-align: left;',
                ],
            ],
            [   // Label
                'name' => 'Instalación', // the name of the db column
                'icon' => 'italic',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'installation_date', // The db column name
                'label' => "Instalación acordada", // Table column heading
                'type' => "date_change_all_dates",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'installation_time', // The db column name
                'label' => "Restricciones de horas", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Label
                'name' => 'warranty_date', // the name of the db column
                'type' => 'building_equipament',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Servicio', // the name of the db column
                'icon' => 'shield',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'service_start_date', // The db column name
                'label' => "Inicio de servicio", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'id' => 'service_start_date',
                    'column-order' => 1,
                    'onchange' => 'getDateWarranty()',
                ],
            ],
            [   // radio
                'name' => 'service_type', // the name of the db column
                'label' => 'Tipo de servicio', // the input label
                'type' => 'space_radio',
                'default' => 'Portería',
                'options' => [Building::$buildingServiceType],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'color: blue;',
                    'required' => true,
                ],
            ],
            [
                'name' => 'initial_date', // The db column name
                'label' => "Datos iniciales", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: -60px;'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'initial_date', // The db column name
                'label' => "Datos iniciales", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: -60px;'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Hidden
                'name' => 'space-initial_date',
                'type' => 'hidden',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'value' => 'active',
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'service_level', // The db column name
                'label' => "Nivel de servicio", // Table column heading
                'type' => 'select_from_array',
                'options' => ['bajo' => 'Bajo', 'medio' => 'Medio', 'alto' => 'Alto'],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'before_delivery', // The db column name
                'label' => "Pre-entrega", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'schedule',
                'label' => 'Horarios del servicio',
                'type' => 'repeatable_mariano_style',
                'fields' => [
                    [   // Checkbox
                        'name' => 'inlineCheckbox1',
                        'label' => 'L &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-right: 25px;'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox2',
                        'label' => 'M &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday2'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox3',
                        'label' => 'X &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday3'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox4',
                        'label' => 'J &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday4'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox5',
                        'label' => 'V &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday5'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox6',
                        'label' => 'S &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [   // Checkbox
                        'name' => 'inlineCheckbox7',
                        'label' => 'D',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-left: 25px;', 'id' => 'inlinecheckday7'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timei1ser',
                        'type' => 'time',
                        'label' => 'Horario inicial',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timef1ser',
                        'type' => 'time',
                        'label' => 'Horario final',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                ],
                'new_item_label' => 'Añadir horario', // customize the text of the button
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // radio
                'name' => 'tags_delivery_by', // the name of the db column
                'label' => 'Distribución por', // the input label
                'type' => 'radio_mariano_style',
                'default' => 'Foxsys',
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    'Foxsys' => "Foxsys",
                    'Portero' => "Portero edificio"
                ],
                // optional
                'inline' => true, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                    'required' => true,
                ],
            ],
            [
                'name' => 'tags_delivery_date', // The db column name
                'label' => "Fecha acordada para distribución de llaveros", // Table column heading
                'type' => "date",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_porter',
                'label' => 'Porteria',
                'type' => 'counter',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_admin',
                'label' => 'Admin',
                'type' => 'counter2',
                'wrapper' => ['class' => 'form-group col-md-3', 'title' => 'Administración'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_clear',
                'label' => 'Limpieza',
                'type' => 'counter3',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [   // Number
                'name' => 'extra_keys_other',
                'label' => 'Otros',
                'type' => 'counter4',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            //*****************************************************************END FIRST COLUMN

            [   // Label
                'name' => 'Características', // the name of the db column
                'icon' => 'city ',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'door_special_denomination', // the name of the db column
                'label' => 'Puertas y Torres:', // the input label
                'type' => 'switch_button4',
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'tower_info',
                'label' => '',
                'type' => 'repeatable_tower',
                'fields' => [
                    [
                        'name' => 'id', // The db column name
                        'label' => "", // Table column heading
                        'type' => "hidden",
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'flats', // The db column name
                        'label' => "Apartamentos", // Table column heading
                        'type' => "textarea_flat",
                        'wrapper' => ['class' => 'form-group col-md-12 switch-button-of-denomination-for-tower-in-building', 'style' => 'padding: 0px; margin-left: -1px;'],
                        'attributes' => [
                            'column-order' => 2,
                            'class' => 'form-control white-field',
                        ],
                    ],
                    [
                        'name' => 'tower_denomination', // The db column name
                        'label' => "Denominación torres", // Table column heading
                        'type' => "text_hidden_if_tower_of",
                        'wrapper' => ['class' => 'form-group col-md-6 element-to-show-when-switch-tower-on', 'style' => 'display:none;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'tower_comment', // The db column name
                        'label' => "Comentarios de torres", // Table column heading
                        'type' => "text_hidden_if_tower_of",
                        'wrapper' => ['class' => 'form-group col-md-6 element-to-show-when-switch-tower-on', 'style' => 'display:none;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'doors_quantity', // The db column name
                        'label' => "Puertas", // Table column heading
                        'type' => "number_in_tower",
                        'default' => 1,
                        'wrapper' => ['class' => 'form-group col-md-6 '],
                        'attributes' => [
                            'column-order' => 2,
                            'min' => 1,
                        ],
                    ],
                    [
                        'name' => 'tower_access', // The db column name
                        'label' => "Comentarios de acceso", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'tower_address', // The db column name
                        'label' => "Dirección", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'tower_corner', // The db column name
                        'label' => "Esquina", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 2,
                        ],

                    ],
                    [
                        'name' => 'opens_from_flat', // The db column name
                        'label' => "Abrir desde arriba", // Table column heading
                        'type' => 'radio',
                        'default' => 'no',
                        'options' => ['si' => 'Si', 'no' => 'No'],
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'inline' => true, // show the radios all on the same line?
                        'attributes' => [
                            'class' => 'white-field form-control bool_fields_back',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [   // radio
                        'name' => 'lock', // the name of the db column
                        'label' => 'Cerradura', // the input label
                        'type' => 'radio',
                        'inline' => true, // show the radios all on the same line?
                        'options' => [
                            // the key will be stored in the db, the value will be shown as label;
                            1 => "Si",
                            null => "No"
                        ],
                        // optional
                        //'inline'      => false, // show the radios all on the same line?
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'class' => 'white-field form-control bool_fields_back',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [
                        'name' => 'doorman_format', // The db column name
                        'label' => "¿Cómo llaman desde abajo?", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-12'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [   // radio
                        'name' => 'allows_elevator_comunication', // the name of the db column
                        'label' => '¿Permite comunicado en el ascensor?:', // the input label
                        'type' => 'radio',
                        'default' => '0',
                        'inline' => true, // show the radios all on the same line?
                        'options' => [
                            // the key will be stored in the db, the value will be shown as label;
                            1 => "Si",
                            0 => "No",

                        ],
                        // optional
                        //'inline'      => false, // show the radios all on the same line?
                        'wrapper' => ['class' => 'form-group col-md-12'],
                        'attributes' => [
                            'column-order' => 2,
                            'required' => 'required',
                        ],
                    ],
                    [
                        'name' => 'measures_locations', // The db column name
                        'label' => "Ubicación de medidores", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding-bottom:25px;'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],
                    [
                        'name' => 'gates_ramps_controllers', // The db column name
                        'label' => "Control de Garaje", // Table column heading
                        'type' => 'radio',
                        'default' => 'no',
                        'inline' => true,
                        'options' => ['si' => 'Si', 'no' => 'No'],
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => [
                            'Class' => 'white-field form-control',
                            'column-order' => 2,
                            'required' => true,
                        ],
                    ],
                    [
                        'name' => 'gates_ramps_denomination', // The db column name
                        'label' => "Denominación", // Table column heading
                        'type' => "text",
                        'wrapper' => ['class' => 'form-group col-md-8'],
                        'attributes' => [
                            'column-order' => 2,
                        ],
                    ],

                ],
                'min_rows' => 1, // minimum rows allowed, when reached the "delete" buttons will be hidden
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Upload
                'name' => 'gates_ramps_file',
                'label' => 'Instructivo de manipulacion para garaje',
                'type' => 'upload',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Delivery', // the name of the db column
                'icon' => 'dolly-flatbed ',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'delivery', // the name of the db column
                'label' => '', // the input label
                'type' => 'radio',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Permitido",
                    0 => "No permitido",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // radio
                'name' => 'delivery_comment', // the name of the db column
                'label' => 'Comentario de delivery', // the input label
                'type' => 'text_with_info_bellow',
                'text_info' => 'Información relevante para Monitoreo o Atención a clientes',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],

            [   // Label
                'name' => 'Acceso de Cadetes (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-cube la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // radio
                'name' => 'cadete_hall', // the name of the db column
                'label' => 'Hall', // the input label
                'type' => 'radio-inline-building-cadete-hall',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'cadete_hall_comments',
                'type' => 'repeatable_custom_comments_cadete_hall',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'label' => '',
                        'type' => "text_with_close_button_area",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'cadete_up', // the name of the db column
                'label' => 'Subir', // the input label
                'type' => 'radio-inline-building-cadete-up',
                'inline' => true,
                'default' => 1,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                    2 => "Excepciones",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'cadete_up_comments',
                'type' => 'repeatable_custom_comments_cadete_up',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Seguridad', // the name of the db column
                'icon' => 'shield',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [
                'name' => 'security_comments',
                'label' => "Comentarios de seguridad",
                'type' => "input_security_alert_building",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'fields_included' => [0 => 'security_comments_2', 1 => 'security_comments_3'],
                'text_info' => "Información de alerta",
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'description_field',
                ],

            ],
            [   // repeatable
                'name' => 'comments',
                'label' => 'Exepciones',
                'type' => 'repeatable_custome_comments',
                'new_item_label' => 'Nuevo comentario',
                'max_rows' => 3,
                'init_rows' => 1,
                'fields' => [
                    [
                        'name' => 'com_com', // The db column name
                        'label' => "", // Table column heading
                        'type' => "text_with_info_bellow_area",
                        'text_info' => "Otra información",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'Acceso de Personas (Nuevo)',
                'icon' => 'people',
                'type' => 'ion_icons',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;color:var(--ColorGreenTitle)',
                ],
                'wrapper' => ['class' => 'title-access-people'],
            ],
            [   // Label
                'name' => 'Instrucciones y/o excepciones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'instructions_explanations',
                'type' => 'repeatable_custom_comments_building_people_access',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'instructions_explanations', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // radio
                'name' => 'amenities', // the name of the db column
                'label' => 'Amenities', // the input label
                'type' => 'radio-inline-building-amenities',
                'inline' => true,
                'default' => 0,
                'options' => [
                    // the key will be stored in the db, the value will be shown as label;
                    1 => "Sí",
                    0 => "No",
                ],
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'required' => true,
                ],
            ],
            [   // repeatable
                'name' => 'instructions_explanations_comments',
                'type' => 'repeatable_custom_comments_building_amenities',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'instructions_explanations_comments', // The db column name
                        'label' => '',
                        'type' => "text_with_close_button_area",
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],

            [   // Label
                'name' => 'Acceso de Servicios (Nuevo)',
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-user-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Instrucciones y/o excepciones ', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'services_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Particularidades (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-info-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Registros', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'records_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Tags', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'tags_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Investigaciones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'investigations_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [   // Label
                'name' => 'Otros', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'others_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            // ******************************************************* END COLUMNA 2
            [
                'name' => 'gamma_code', // The db column name
                'label' => "Respuesta física", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'placeholder' => 'Escribir respuesta fisica',
                ],
            ],
            [   // Label
                'name' => 'Administración y Servicios', // the name of the db column
                'icon' => 'info-circle',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Table
                'name' => 'porter',
                'label' => 'Porteros',
                'type' => 'dor_man_building',
                'entity_singular' => 'Portero', // used on the "Add X" button
                'columns' => [
                    'name' => 'Nombre',
                    'hour' => 'Horario (Ej: "L a V de 15:00 a 20:00 y 22:00 a 02:00 / S y D de 12:00 a 18:00")',
                    'number' => 'Número'
                ],
                'max' => 8, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Table
                'name' => 'cleaning_time',
                'label' => 'Limpieza',
                'type' => 'table_cleaning_time_building',
                'entity_singular' => 'Limpieza', // used on the "Add X" button
                'columns' => [
                    'name' => 'Nombre',
                    'hour' => 'Horario (Ej: "L a V de 15:00 a 20:00 y 22:00 a 02:00 / S y D de 12:00 a 18:00")',
                    'number' => 'Número'
                ],
                'max' => 8, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [    // Select2Multiple = n-n relationship (with pivot table)
                'label' => "Contactos referentes",
                'type' => 'select2_multiple_filtering_contacts_in_building',
                'name' => 'referents', // the method that defines the relationship in your Model
                // optional
                'model' => "App\Models\user\Contact", // foreign key model
                'attribute' => 'complete_name', // foreign key attribute that is shown to user
                'pivot' => true, // on create&update, do you need to add/delete pivot table entries?
                // 'select_all' => true, // show Select All and Clear buttons?
                // optional
                'options' => true,
                'attributes' => [
                    'column-order' => 3,
                ], // force the related options to be a custom query, instead of all(); you can use this to filter the results show in the select
            ],
            [
                'type' => "select2_multiple_filtering_contacts_in_building",
                'label' => "Contactos de comisión",
                'name' => 'commissions',
                'options' => true,
                'entity' => 'commissions',
                'attribute' => "complete_name",
                'ajax' => true,
                'pivot' => true,
                'inline_create' => ['entity' => 'contact'], // specify the entity in singular
                'allow_clear' => false, // specify the entity in singular
                'model' => "App\Models\User\Contact",
                // that way the assumed URL will be "/admin/tag/inline/create"
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'type' => "select2",
                'name' => 'administration_id',
                'label' => "Administradoras",
                'link_label' => "+Nueva Administradora",
                'link_href' => "/admin/administrator/create",
                'entity' => 'administration', // the method on your model that defines the relationship
                'model' => "App\Models\Company\Administration",
                'attribute' => "name",
                'ajax' => true,
                'inline_create' => true, // specify the entity in singular
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'class' => 'form-control white-field',
                ],

            ],
            [
                'type' => "select2_multiple",
                'label' => "Servicios y Provedores",
                'link_label' => "+Nuevo Servicio",
                'link_href' => "/admin/service/create",
                'name' => 'services', // the method on your model that defines the relationship
                'attribute' => "full_name",
                'ajax' => true,
                'inline_create' => ['entity' => 'service'], // specify the entity in singular
                'allow_clear' => false, // specify the entity in singular
                // that way the assumed URL will be "/admin/tag/inline/create"
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'rows' => 5,
                ],
            ],
            [   // Label
                'name' => 'Normas generales (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-file-alt la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Normas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'rules_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Gestiones', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'arrangements_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Averías', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'malfunctions_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Mudanzas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'moving_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Información general (Nuevo)', // the name of the db column
                'type' => 'building-edit-cadete-access',
                'icon' => 'la la-info-circle la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Locales comerciales', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'commercial_locals_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Baño', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'bathrooms_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Medidores', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'medidores_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Azotea', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'rooftop_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Alarma incendio', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'fire_alarm_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Otras alarmas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'others_alarms_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Instrucciones adicionales', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal',
                ],
            ],
            [   // repeatable
                'name' => 'aditional_instructions_comments',
                'type' => 'repeatable_custom_comments_building_services',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 10,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'comment', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Kazoo', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold;',
                ],
            ],
            [   // Label
                'name' => 'Kazoo', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-top: 5%',
                ],
            ],
            [
                'name' => 'account_kazoo', // The db column name
                'label' => "Cuenta", // Table column heading
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],

            ],
            [
                'name' => 'domain_kazoo', // The db column name
                'label' => "Dominio", // Table column heading
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'margin-bottom: 3%'
                ],

            ],
            [   // radio
                'name' => 'own_foxsys', // the name of the db column
                'label' => 'Interno Foxsys', // the input label
                'type' => 'margin_own_foxsys',
                // optional
                //'inline'      => false, // show the radios all on the same line?
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'number_of_internet_contrat', // The db column name
                'label' => "Nº de contrato Internet", // Table column heading
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'order_job',
                'label' => 'Orden de trabajo',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'order_job_checked',
                'label' => 'Orden de trabajo revisada',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'contract',
                'label' => 'Contrato',
                'type' => 'upload_only_comercial',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security',
                'label' => 'Relevamiento de seguridad',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'max-height: 4%'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Upload
                'name' => 'rev_security2',
                'label' => 'Relevamiento de seguridad 2',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security2'
                ],
            ],
            [   // Upload
                'name' => 'rev_security3',
                'label' => 'Relevamiento de seguridad 3',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security3'
                ],
            ],
            [   // Upload
                'name' => 'rev_security4',
                'label' => 'Relevamiento de seguridad 4',
                'type' => 'upload_only_technical',
                'upload' => true,
                'disk' => 'uploads', // if you store files in the /public folder, please ommit this; if you store them in /storage or S3, please specify it;
                'wrapper' => ['class' => 'form-group col-md-12 d-none', 'style' => 'max-height: 4%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'rev_security4'
                ],
            ],
            [   // Label
                'name' => '+ Añadir relavamiento', // the name of the db column
                'icon' => '',
                'type' => 'label_link',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => '',
                ],
            ],
            [
                'name' => 'app_foxsys', // The db column name
                'label' => "App Foxsys", // Table column heading
                'type' => "switch_button",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'open_door_disabled', // The db column name
                'label' => "Abrir desde app deshabilitado", // Table column heading
                'type' => "switch_button2",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [
                'name' => 'video_disabled', // The db column name
                'label' => "Videollamada de app deshabilitada", // Table column heading
                'type' => "switch_button3",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'access_by_pin', // the name of the db column
                'label' => "Acceso por PIN",
                'type' => "switch_button_PIN",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'access_by_pin'
                ],
            ],
            [
                'name' => 'incoming_call', // The db column name
                'label' => "Timbre Directo Activado", // Table column heading
                'type' => "switch_button_building_incoming_call",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'incoming_call'
                ],
            ],
            [
                'name' => 'face_recognition', // The db column name
                'label' => "Reconocimiento facial", // Table column heading
                'type' => "switch_button_face_recognition",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'face_recognition'
                ],
            ],
            [
                'name' => 'camera_anpr', // The db column name
                'label' => "Cámara ANPR", // Table column heading
                'type' => "switch_button_camera_anpr",
                'default' => 0,
                'wrapper' => ['class' => 'form-group col-md-12', ],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'incoming_call',
                ],
            ],
//            [
//                'name' => 'access_by_pin_permanent', // the name of the db column
//                'label' => "Acceso por PIN permanente",
//                'type' => "switch_button_PIN_permanent",
//                'default' => 0,
//                'wrapper' => ['class' => 'form-group col-md-12'],
//                'attributes' => [
//                    'column-order' => 3,
//                    'id' => 'access_by_pin_permanent'
//                ],
//            ],
//            [
//                'name' => 'access_by_pin_temporary', // the name of the db column
//                'label' => "Acceso por PIN temporal",
//                'type' => "switch_button_PIN_temporary",
//                'default' => 0,
//                'wrapper' => ['class' => 'form-group col-md-12'],
//                'attributes' => [
//                    'column-order' => 3,
//                    'id' => 'access_by_pin_temporary'
//                ],
//            ],
            [  // Select
                'label' => "Ejecutivo Comercial",
                'type' => 'select',
                'name' => 'operator_id', // the db column for the foreign key

                'entity' => 'comercial',
                'allows_null' => false,
                'default' => backpack_user()->id == 30911 ? 30911 : 30910,

                // optional - manually specify the related model and attribute
                'model' => "App\Models\User\Comercial", // related model
                'attribute' => 'name', // foreign key attribute that is shown to user

                // optional - force the related options to be a custom query, instead of all();
                'options' => (function ($query) {
                    return $query->orderBy('name', 'ASC')->get();
                }), //  you can use this to filter the results show in the select
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 15px;'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Equipamiento y Datos de Foxsys (Nuevo)', // the name of the db column
                'type' => 'building-edit-equipment-foxsys',
                'icon' => 'la la-camera la-2x',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-bottom: 5px;',
                ],
            ],
            [   // Label
                'name' => 'Teléfonos', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'phones_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'phone', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Altoparlantes', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'speakers_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'speaker', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Sirenas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'sirens_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'siren', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [   // Label
                'name' => 'Alarmas integradas', // the name of the db column
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'color: black; font-weight: normal; padding: 0 5px;',
                ],
            ],
            [   // repeatable
                'name' => 'integrated_alarms_comments',
                'type' => 'repeatable_custom_comments_building_equipment_foxsys',
                'new_item_label' => 'Añadir comentario',
                'max_rows' => 5,
                'init_rows' => 0,
                'fields' => [
                    [
                        'name' => 'integrated_alarm', // The db column name
                        'type' => "text_with_close_button_area",
                        'label' => '',
                        'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding:0px;'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [  // Select
                'type' => 'created_by',
                'name' => 'created_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [  // Select
                'type' => 'updated_by',
                'name' => 'updated_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%; padding: 0 20px;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [  // Select
                'type' => 'hidden',
                'name' => 'id', // the db column for the foreign key
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [  // Select
                'type' => 'created_by',
                'name' => 'created_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],

            [  // Select
                'type' => 'updated_by',
                'name' => 'updated_by', // the db column for the foreign key
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'margin-top: 7%;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [  // Select
                'type' => 'hidden',
                'name' => 'id', // the db column for the foreign key
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
        ];
    }
}
