<?php

namespace App\Http\Fields;

use App\Models\Accounting\Bill;
use App\Models\User\User;
use App\Models\Utilities\Country;
use Carbon\Carbon;


class ContactFields extends AbstractFields
{
    public function getFormFields()
    {
        return [
            [
                'name' => 'Principal',
                'icon' => 'city',
                'type' => 'label_change_building',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold; color:gray',
                ],
            ],
            [
                'label' => "Contacto no ingresado en el sistema",
                'type' => 'select_building_and_flat_in_contact_columns_multiple',
                'name' => 'id',
                'wrapper' => ['class' => 'col-md-12'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'type_of_contact_in_building',
                'label' => 'Unidad',
                'type' => 'radio_contact_type',
                'options' => [
                    "Residencial" => "Residencial",
                    "Oficina" => "Oficina",
                ],
                'default' => 'Residencial',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'owner_or_tenant',
                'label' => "Relación",
                'type' => 'radio',
                'options' => [
                    'S/D' => 'S/D',
                    'Propietario' => 'Propietario',
                    'Inquilino' => 'Inquilino',
                    'Apoderado' => 'Apoderado'
                ],
                'default' => 'S/D',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                    'name' => 'owner_or_tenant'
                ],
            ],
            [
                'name' => 'contact_type',
                'label' => "Tipo de contacto",
                'type' => 'select_from_array_contact_type',
                'options' =>
                    [
                        'Residente' => 'Residente',
                        'No Residente' => 'No Residente',
                        'Comisión' => 'Comisión',
                        'Contacto Principal' => 'Contacto Principal',
                        'Contacto Principal - Comisión' => 'Contacto Principal - Comisión',
                        'Oficina' => 'Oficina',
                        'Prohibido Ingreso' => 'Prohibido Ingreso',
                    ],
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'id' => 'contact_type',
                    'Class' => 'white-field form-control',
                    'column-order' => 1,
                    'onchange' => 'showElementOnChange()',
                ],
            ],
            [
                'name' => 'referrer',
                'label' => "Referente del edificio",
                'type' => "checkbox_icon",
                'icon-name' => "award",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: 25px'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'blank',
                'type' => "hidden",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'empty_flat',
                'label' => "Apartamento vacío",
                'type' => "checkbox_icon",
                'icon-name' => "user-alt-slash",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: -25px'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'schedule',
                'label' => 'Horarios de autorización',
                'init_rows' => 0,
                'type' => 'repeatable_schedule_contact',
                'fields' => [
                    [
                        'name' => 'inlineCheckbox1',
                        'label' => 'L &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-right: 25px;'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox2',
                        'label' => 'M &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday2'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox3',
                        'label' => 'X &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday3'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox4',
                        'label' => 'J &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday4'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox5',
                        'label' => 'V &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday5'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox6',
                        'label' => 'S &nbsp;',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-inline: 25px;', 'id' => 'inlinecheckday6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'inlineCheckbox7',
                        'label' => 'D',
                        'type' => 'checkbox',
                        'wrapper' => ['class' => 'form-group col-md-1', 'style' => 'padding-left: 25px;', 'id' => 'inlinecheckday7'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timei1ser',
                        'type' => 'time',
                        'label' => 'Horario inicial',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                    [
                        'name' => 'timef1ser',
                        'type' => 'time',
                        'label' => 'Horario final',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'column-order' => 1,
                        ],
                    ],
                ],
                'new_item_label' => '+Añadir horario de autorización',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 1,

                ],
            ],
            [
                'name' => ['start_date', 'end_date'],
                'new_item_label' => '+Añadir fecha de autorización',
                'label' => 'Añadir fecha de autorización',
                'type' => 'date_range_with_link',
                'default' => [Carbon::today('America/Montevideo')->now(), Carbon::today('America/Montevideo')->endOfDay()],
                'date_range_options' => [
                    'drops' => 'down',
                    'timePicker' => true,
                    'locale' => ['format' => 'DD/MM/YYYY HH:mm']
                ],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'start_end_date'
                ],
            ],
            [
                'name' => 'Notas Y Comentarios',
                'icon' => 'file',
                'type' => 'label_gray',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold; color:gray',
                ],
            ],
            [
                'name' => 'description',
                'label' => "",
                'type' => "diferent_columns_same_text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'fields_included' => [0 => 'notes_2', 1 => 'notes_3'],
                'attributes' => [
                    'column-order' => 1,
                    'id' => 'description_field',
                ],

            ],
            [
                'name' => 'Llaves e integrantes',
                'icon' => 'key',
                'type' => 'label_fields_relation',
                'attributes' => [
                    'column-order' => 1,
                    'style' => 'font-size: 18px; font-weight: bold; color: gray',
                ],
            ],
           [
                'name' => 'keys_acces',
                'type' => 'keys_fields_relation',
                'attributes' => [
                    'column-order' => 4,
                ],
            ],
            [
                'name' => 'Información personal',
                'icon' => 'user',
                'type' => 'label_gray',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold; color:gray',
                ],
            ],
            [
                'name' => 'name',
                'label' => "Nombre <span style='color:red'>*</span>",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'name_contact',
                ],
            ],
            [
                'name' => 'surname',
                'label' => "Apellido <span id='contact_lastname' style='color:red'>*</span>",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'sur_name_contact',

                ],
            ],
            [
                'name' => 'birthday',
                'label' => 'Fecha nacimiento',
                'type' => 'date',
                'datetime_picker_options' => [
                    'format' => 'DD/MM/YYYY',
                    'language' => 'es'
                ],
                'allows_null' => true,
                'wrapper' => ['class' => 'form-group'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'birthday'
                ],

            ],
            [
                'name' => 'foreign_document',
                'label' => "Tipo de documento",
                'type' => 'select_from_array',
                'options' => [0 => 'Cedula', 1 => 'Pasaporte', 2 => 'DNI', 3 => 'Otro'],
                'allows_null' => false,
                'default' => 'one',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'id' => 'foreign_document',
                    'column-order' => 2,
                    'class' => 'white-field form-control',
                ],
            ],
            [
                'name' => 'ci',
                'label' => "Documento de identidad",
                'type' => "text_ci",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'security_word',
                'label' => "Palabra clave",
                'type' => "text_security_word",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'Datos del contacto',
                'icon' => 'user',
                'type' => 'label_gray',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'style' => 'font-size: 18px; font-weight: bold; color: gray',
                ],
            ],
            [
                'name' => 'own_foxsys',
                'label' => 'Telefonos',
                'type' => 'sub_label',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'column-order' => 2,
                ],
            ],
            [
                'name' => 'dont_call',
                'label' => "No llamar",
                'icon-name' => "phone-slash",
                'icon-color' => "red",
                'type' => "checkbox_icon",
                'wrapper' => ['class' => 'form-group col-md-9', 'style' => 'padding-top: 1%;'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'dont_callbox'
                ],
            ],
            [
                'name' => 'phone_mobile',
                'label' => "Teléfono celular",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'phone_mobile'
                ],
            ],
            [
                'name' => 'phone_home',
                'label' => "Teléfono hogar",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'phone_home'
                ],
            ],
            [
                'name' => 'home_is_primary_phone',
                'label' => "Marcar teléfono hogar como primario",
                'type' => "checkbox_icon",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 2,
                    'id' => 'home_is_primary_phonebox'
                ],
            ],
            [
                'name' => 'others_mobil_phones',
                'label' => '',
                'type' => 'repeatable_external_number',
                'fields' => [
                    [
                        'name' => 'phone_number',
                        'type' => 'text',
                        'label' => 'Teléfono extra',
                        'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'padding-left: 0%'],
                    ],
                    [
                        'name' => 'foreign_phone',
                        'type' => 'checkbox',
                        'label' => 'Teléfono extranjero',
                        'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'padding-top: 8%'],
                    ],
                ],
                'attributes' => [
                    'column-order' => 2,
                ],
                'new_item_label' => 'Añadir teléfono',
                'init_rows' => 0,

            ],
            [
                'name' => 'Autogestión',
                'icon' => 'mobile',
                'type' => 'label_gray_autogestion',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; color:gray',
                ],
            ],
            [
                'label' => "",
                'name' => "image",
                'type' => 'image_not_edit',
                'upload' => true,
                'crop' => true,
                'aspect_ratio' => 1,
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'readonly' => 'readonly',
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'dispositivo',
                'label' => "Dispositivo",
                'type' => "square_info",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 3,
                ],

            ],
            [
                'label' => "",
                'name' => "image",
                'type' => 'image_custom',
                'upload' => true,
                'crop' => true,
                'aspect_ratio' => 9/16,
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 3,
                    'title' => 'Imagen',
                ],
            ],
            [
                'name' => 'notification_service_and_products',
                'label' => "Deseo recibir información sobre productos, servicios y ofertas por correo electrónico.",
                'type' => "checkbox_notification_products_and_services",
                'wrapper' => ['class' => 'form-group col-md-12', 'style' => 'padding-top: 1%;'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'email',
                'label' => "Email",
                'type' => 'contact_email',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'email'
                ],
            ],
            [
                'name' => 'address',
                'label' => "Dirección",
                'type' => "contact_text",
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'address'
                ],

            ],
            [
                'name' => 'door_number',
                'label' => "Número de puerta",
                'type' => "contact_text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'door_number'
                ],
            ],
            [
                'name' => 'address_flat_number',
                'label' => "Número de apartamento",
                'type' => "contact_text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 3,
                    'id' => 'address_flat_number'
                ],
            ],
            [
                'name' => 'intercom_name',
                'label' => "Nombre en Intercom",
                'type' => "text_contact_kazoo_intercom",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'intercom_incoming_call',
                'label' => "Permitir timbre directo",
                'type' => "checkbox_icon_contact_intercom_incoming_call",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: 35px'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'Kazoo',
                'icon' => '',
                'type' => 'label',
                'large-row' => 'col-md-12',
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; margin-top: 5%',
                ],
            ],
            [
                'name' => 'name_kazoo',
                'label' => "Nombre Kazoo",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'user_sip_kazoo',
                'label' => "Usuario SIP Kazoo",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'password_kazoo',
                'label' => "Contraseña Kazoo",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'number_kazoo',
                'label' => "Número Kazoo",
                'type' => "label_kazoo",
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'passchange',
                'label' => 'Resetear contraseña',
                'type' => 'change_pass',
                'attributes' => [
                    'column-order' => 3,
                ],
                'wrapper' => ['class' => 'form-group col-md-12 mt-1'],
            ],
            [
                'name' => 'code',
                'label' => "PIN de acceso permanente",
                'type' => "access_code_generator_contact",
                'wrapper' => ['class' => 'form-group'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'access_code_temporal',
                'label' => "PIN de acceso temporal",
                'type' => "access_code_generator_contact_temporal",
                'wrapper' => ['class' => 'form-group'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'name' => 'referrer', // The db column name
                'label' => "Contacto de Referencia", // Table column heading
                'type' => "checkbox_icon_check_monitoreo",
                'wrapper' => ['class' => 'form-group col-md-6', 'style' => 'margin-top: 25px;'],
                'attributes' => [
                    'column-order' => 1,
                ],
            ],
            [
                'name' => 'Vehículos',
                'icon' => 'car',
                'type' => 'label_gray',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                    'style' => 'font-size: 18px; font-weight: bold; color:gray',
                ],
            ],
            [
                'name' => 'vehicles',
                'type' => 'cars_field_in_contact',
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'type' => 'created_by',
                'name' => 'created_by',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
            [
                'type' => 'updated_by',
                'name' => 'updated_by',
                'wrapper' => ['class' => 'form-group col-md-12'],
                'attributes' => [
                    'column-order' => 3,
                ],
            ],
        ];
    }

    public function getColumnsFields()
    {
        return [

            [
                'name' => "full_flats_buildings_text",
                'label' => "Edificio",
                'type' => "text",
                'limit' => '80',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhereHas('buildings', function ($q) use ($searchTerm) {
                            $terms = explode(' - ', $searchTerm);
                            $q->where(function ($subQuery) use ($terms) {
                                if (count($terms) > 1) {
                                    $subQuery->where('building_number', 'like', '%' . trim($terms[0]) . '%')
                                        ->where('name', 'like', '%' . trim($terms[1]) . '%');
                                } else {
                                    $subQuery->where('building_number', 'like', '%' . $terms[0] . '%')
                                        ->orWhere('name', 'like', '%' . $terms[0] . '%');
                                }
                            });
                        });
                    }
                },


                'wrapper' => ['class' => 'col-md-12'],
            ],
            [
                'name' => 'number_with_tower',
                'type' => 'text',
                'label' => 'Apartamento',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhereHas('flats', function ($q) use ($searchTerm) {
                            $q->where('number_with_tower', 'like', '%' . $searchTerm . '%');
                        }
                        );
                    }
                },
                'wrapper' => ['class' => 'col-md-12'],
            ],
            [
                'name' => 'name',
                'label' => "Nombre",
                'type' => "contact_name",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $filters = explode(" ", $searchTerm);
                        $nameFilter = implode(' ', $filters);
                        $query->orWhere('users.complete_name', 'like', '%' . $nameFilter . '%');
                    }
                }
            ],
            [
                'name' => 'surname',
                'label' => "Apellido",
                'type' => "contact_surname",

            ],
            [
                'name' => 'email',
                'label' => "Email",
                'type' => 'text',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhere('users.email', 'like', '%' . $searchTerm . '%');
                    }
                }
            ],
            [
                'name' => 'contact_type',
                'label' => "Tipo de contacto",
                'type' => 'contact_type',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhereHas('buildingContacts', function ($q) use ($searchTerm, $column) {
                            $q->where($column['name'], 'like', '%' . $searchTerm . '%');
                        });
                    }
                }
            ],
            [
                'name' => 'ci',
                'label' => "Doc./Clave",
                'type' => "ci",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $searchTerm = User::formatDocumentByCi($searchTerm, false);
                        $query->orWhere('users.ci', 'like', '%' . $searchTerm . '%')
                            ->orWhere('users.security_word', 'like', '%' . $searchTerm . '%');
                    }
                }
            ],
            [
                'name' => "phone_mobile",
                'label' => "Teléfono",
                'type' => "link_number",
                'limit' => 12,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');
                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhereHas('buildingContacts', function ($q) use ($searchTerm) {
                            $q->where('phone_mobile', 'like', '%' . $searchTerm . '%');
                            $q->orWhere('phone_home', 'like', '%' . $searchTerm . '%');
                        });
                    }
                }
            ],
            [
                'name' => 'owner_or_tenant',
                'label' => "Propietario o inquilino",
                'type' => "contact_owner_or_tenant",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $searchField = request()->input('search_field');

                    if ($searchField == $column['name'] || !$searchField) {
                        $query->orWhereHas('buildingContacts', function ($q) use ($searchTerm, $column) {
                            $q->where($column['name'], 'like', '%' . $searchTerm . '%');
                        });
                    }
                }
            ],
            [
                'name' => 'dont_call',
                'label' => "No llamar",
                'type' => "dont_call",
            ],
            [
                'name' => 'deleted_at',
                'label' => "Estado",
                'type' => "contact_status",
            ],
        ];
    }

    public function getShowFields()
    {
        return [
            [
                'label' => "Imagen de perfil",
                'name' => "image",
                'type' => 'image',
                'height' => '330px',

            ],
            [
                'name' => "building",
                'label' => "Edificio",
                'type' => "model_function",
                'function_name' => 'buildingName',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhereHas('building', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%');
                    }
                    );
                }
            ],
            [
                'name' => "flat",
                'label' => "Apartamento",
                'type' => "model_function",
                'function_name' => 'flatName',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhereHas('flat', function ($q) use ($searchTerm) {
                        $q->where('number', 'like', '%' . $searchTerm . '%');
                    }
                    );
                }

            ],
            [
                'name' => 'name',
                'label' => "Nombre",
                'type' => "text",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $filters = explode(" ", $searchTerm);
                    $nameFilter = '%';
                    foreach ($filters as $filter) {
                        $nameFilter .= $filter . '%';
                    }
                    $query->orWhere($column['name'], 'like', $nameFilter);
                }
            ],

            [
                'name' => 'email',
                'label' => "Email",
                'type' => 'text',
                'limit' => 20,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'contact_type',
                'label' => "Tipo de contacto",
                'type' => 'select2_from_array',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'ci',
                'label' => "Documento",
                'type' => "text",
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => "phone_mobile",
                'label' => "Teléfono",
                'type' => "model_function",
                'function_name' => 'getPrimaryPhone',
                'limit' => 12,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'owner_or_tenant',
                'label' => "Propietario o inquilino",
                'type' => "text",
                'wrapper' => ['class' => 'form-group col-md-6'],
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere($column['name'], 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'dont_call',
                'label' => "No llamar",
                'type' => "boolean",
            ],


        ];
    }
}
