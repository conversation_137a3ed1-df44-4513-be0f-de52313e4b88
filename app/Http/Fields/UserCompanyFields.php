<?php

namespace App\Http\Fields;

use App\Models\Accounting\Bill;
use App\Models\Utilities\Country;
use Carbon\Carbon;


class UserCompanyFields extends AbstractFields
{
    public function getFormFields()
    {
        return [
            [
                'label' => "Imagen de perfil",
                'name' => "image",
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'aspect_ratio' => 1, // ommit or set to 0 to allow any aspect ratio
            ],
            [
                'name' => 'name',
                'label' => trans('backpack::permissionmanager.name'),
                'type' => 'text',
            ],
            [
                'name' => 'surname',
                'label' => 'Apellido',
                'type' => 'text',
            ],
            [
                'name' => 'email',
                'label' => trans('backpack::permissionmanager.email'),
                'type' => 'email',
                'attributes' => [
                    'autocomplete' => 'new-password',
                ],
            ],
            [
                'name' => 'password',
                'label' => trans('backpack::permissionmanager.password'),
                'type' => 'password',
                'attributes' => [
                    'autocomplete' => 'new-password',
                ],
            ],
            [
                'name' => 'password_confirmation',
                'label' => trans('backpack::permissionmanager.password_confirmation'),
                'type' => 'password',
            ],

            [

                'label' => trans('backpack::permissionmanager.roles'),
                'name' => 'roles', // the method that defines the relationship in your Model
                'entity' => 'roles', // the method that defines the relationship in your Model
                'attribute' => 'name', // foreign key attribute that is shown to user
                'model' => config('permission.models.role'), // foreign key model
                'pivot' => true, // on create&update, do you need to add/delete pivot table entries?]
                'number_columns' => 3, //can be 1,2,3,4,6
                'type' => 'checklist',
                'default_permissions' => [
                    'Empresa' => 11,
                ],
            ],
        ];
    }


    public function getColumnsFields()
    {

        return [
            [
                'name' => 'name',
                'label' => trans('backpack::permissionmanager.name'),
                'type' => 'text',
            ],
            [
                'name' => 'surname',
                'label' => 'Apellido',
                'type' => 'text',
            ],
            [
                'name' => 'email',
                'label' => trans('backpack::permissionmanager.email'),
                'type' => 'email',
            ],

        ];
    }


}
