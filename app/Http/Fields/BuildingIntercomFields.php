<?php

namespace App\Http\Fields;

use App\Models\Accounting\Bill;
use App\Models\Utilities\Country;
use Carbon\Carbon;


class BuildingIntercomFields extends AbstractFields
{
    public function getFormFields()
    {
        return [
            [
                'name' => 'intercom_type',
                'label' => "Tipo de Intercom",
                'type' => 'select_from_array_intercoms',
                'allows_null' => false,
                'default' => 'AKUVOX',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'Class' => 'white-field form-control',
                ],
            ],
            [  // Select2
                'label' => "Edificio",
                'type' => 'select2_custom_building_intercoms_building',
                'name' => 'building_id', // the db column for the foreign key
                'entity' => 'building', // the method that defines the relationship in your Model
                'model' => "App\Models\Building", // foreign key model
                'attribute1' => 'building_number',
                'attribute2' => 'name',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'required' => true,
            ],
            [
                'name' => 'intercom_service',
                'label' => "Servicio",
                'type' => 'select_from_array',
                'options' => ['PUERTA' => 'PUERTA', 'GARAGE' => 'GARAGE', 'BARBACOA' => 'BARBACOA'],
                'allows_null' => false,
                'default' => 'PUERTA',
                'wrapper' => ['class' => 'form-group col-md-1'],
                'required' => true,
                'attributes' => [
                    'class' => 'white-field form-control',
                    'id' => 'intercom_service'
                ],
            ],
            [  // Select2
                'label' => "Apartamentos",
                'type' => 'select2_custom_building_flat_id_intercoms',
                'name' => 'flats', // the db column for the foreign key
                'select_all' => true,
                'wrapper' => ['class' => 'form-group col-md-3'],
            ],
            [   // Text
                'name' => 'ip',
                'label' => "IP",
                'type' => 'text',
                'required' => true,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'ip',
                ]
            ],
            [   // Text
                'name' => 'user',
                'label' => "Usuario <span style='color:red'>*</span>",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4', 'style' => 'display:none;'],
                'default' => 'root',
                'attributes' => [
                    'id' => 'user',
                ],
            ],
            [   // Text
                'name' => 'switch',
                'label' => "Switch",
                'type' => 'number',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'required' => true,
                'attributes' => [
                    'id' => 'switch',
                    'max' => 9999999999,
                ]
            ],
            [   // Text
                'name' => 'door_name',
                'label' => "Nombre Puerta",
                'type' => 'text',
                'required' => true,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'door_name',
                ]
            ],
            [   // Text
                'name' => 'password',
                'label' => "Password <span style='color:red'>*</span>",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4', 'style' => 'display:none;'],
                'attributes' => [
                    'id' => 'password',
                ],
            ],
            [   // Text
                'name' => 'action',
                'label' => "Acción <span style='color:red'>*</span>",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4', 'style' => 'display:none;'],
                'attributes' => [
                    'id' => 'action',
                ],
            ],
            [   // Text
                'name' => 'asterisk_extension_number',
                'label' => "Extensión Asterisk",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'asterisk_extension_number',
                ]
            ],
            [   // Text
                'name' => 'extension_3cx',
                'label' => "Extensión 3CX",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'extension_3cx',
                ]
            ],
            [   // Text
                'name' => 'tower_prefix',
                'label' => "Prefijo Torre",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'tower_prefix',
                ]
            ],

            [
                'label' => "Tipo de Intercoms <span style='color:red'>*</span>",
                'type' => 'select2_custom_building_intercoms',
                'name' => 'intercom_type',
                'entity' => 'intercomModel',
                'model' => "App\Models\IntercomModel",
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attribute2' => 'model',
                'attribute1' => 'type',
                'required' => true,
                'attributes' => [
                    'id' => 'intercom_type',
                ],
            ],
            [
                'name' => 'delay',
                'label' => "Delay (Sólo AK)",
                'type' => 'number',
                'required' => true,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'delay',
                ],
            ],
            [
                'name' => 'num',
                'label' => "Num (Sólo AK, por defecto 1)",
                'type' => 'number',
                'default' => 1,
                'required' => true,
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'num-ak',
                ],
            ],
            [
                'name' => 'access_group',
                'label' => "Grupo de acceso",
                'type' => 'text_intercom_access_group',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'access_group',
                ],
            ],
            [
                'name' => 'order_group',
                'label' => "Orden en el grupo",
                'type' => 'text_intercom_order_group',
                'wrapper' => ['class' => 'form-group col-md-4'],
                'attributes' => [
                    'id' => 'order_group',
                ],
            ],
            [
                'name' => 'rtsp_url',
                'label' => "Urls RTSP",
                'required' => true,
                'type' => 'table_mariano_style',
                'entity_singular' => 'url',
                'columns' => [
                    'url' => 'Url',
                ],
                'max' => 5, // maximum rows allowed in the table
                'min' => 0, // minimum rows allowed in the table
            ],
            [
                'name' => 'has_keyboard',
                'label' => "Tiene teclado",
                'type' => "checkbox_intercom_has_keyboard",
                'wrapper' => ['class' => 'form-group col-md-6 mt-3'],

            ],


        ];
    }

    public function getColumnsFields()
    {

        return [
            [  // Select2
                'label' => "Edificio",
                'type' => 'text',
                'name' => 'building_name',
            ],
            [   // Text
                'name' => 'intercom_service',
                'label' => "Servicio",
                'type' => 'text',
            ],
            [  // Select2
                'label' => "Apartamentos",
                'type' => 'building_intercoms_flats',
                'name' => 'number_with_tower',
                'limit' => 30,
            ],
            [   // Text
                'name' => 'ip',
                'label' => "IP",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [   // Text
                'name' => 'switch',
                'label' => "Switch",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [   // Text
                'name' => 'door_name',
                'label' => "Nombre Puerta",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [   // Text
                'name' => 'asterisk_extension_number',
                'label' => "Extensión Asterisk",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [   // Text
                'name' => 'tower_prefix',
                'label' => "Prefijo Torre",
                'type' => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [
                'name' => 'fake_model',
                'label' => "Intercom",
                'type' => 'text'
            ],


        ];
    }


}
