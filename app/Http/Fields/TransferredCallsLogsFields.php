<?php

namespace App\Http\Fields;

use App\Models\Accounting\Bill;
use Carbon\Carbon;


class TransferredCallsLogsFields extends AbstractFields
{
    public function getFormFields()
    {
        return [];
    }

    public function getColumnsFields()
    {
        return [
            [
                'name' => 'extension_3cx',
                'label' => 'Extension 3cx',
                'type' => 'text',
            ],
            [
                'name' => 'data',
                'label' => 'Data',
                'type' => 'json',
            ]
        ];
    }
}
