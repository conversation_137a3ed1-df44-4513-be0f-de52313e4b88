<?php

namespace App\Events;

use App\Models\Caseq;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CaseCreatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $case;
    public function __construct(Caseq $case)
    {
        $this->case = $case;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
