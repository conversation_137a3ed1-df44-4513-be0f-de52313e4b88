<?php

namespace App\Services;

use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Flat;
use App\Models\KazooFlatsConsecutive;
use Http;
use Illuminate\Support\Str;

enum MethodsEnum: string
{
    case POST = 'post';
    case GET = 'get';
    case PUT = 'put';
    case PATCH = 'patch';
    case DELETE = 'delete';
}

enum ResponseErrorStatusEnum: string
{
    case SUCESS = "success";
    case ERROR = "error";
}

class KazooService
{
    private static $kazoo_auth_api_key = "";
    private static $request_content_type = "application/json";
    private static $auth_token = '';
    private static $kazoo_foxsys_domain_suffix = '.uy.foxsys.cloud';

    private static $towersLetterNumbers = [
        '1' => 1,
        '2' => 2,
        '3' => 3,
        '4' => 4,
        '5' => 5,
        '6' => 6,
        '7' => 7,
        '8' => 8,
        '9' => 9,
        'A' => 1,
        'B' => 2,
        'C' => 3,
        'D' => 4,
        'E' => 5,
        'F' => 6,
        'G' => 7,
        'H' => 8,
        'I' => 9,
    ];

    //

    /**
     * @throws \Exception
     */
    public static function executeHttpRequest(string $url, array $attributes, string $method, bool $authorization = true)
    {
        try {
            $headers = [
                'Content-Type' => self::$request_content_type,
            ];
            if ($authorization) {
                $headers['X-Auth-Token'] = self::$auth_token;
            }
            $response = Http::withHeaders($headers)
                ->$method($url, $attributes);
            $responseDecoded = json_decode($response, true);
            \Log::info('executeHttpRequest', ['responseDecoded' => $responseDecoded]);

            if (isset($responseDecoded['error']) && str_contains($responseDecoded['message'], 'invalid_credentials')) {
                self::login();
                return self::executeHttpRequest($url, $attributes, $method, $authorization);
            }
            return $response;
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    // Functions
    // Login
    /**
     * @throws \Exception
     */
    public static function login()
    {
        self::$kazoo_auth_api_key = config('constants.kazoo_api_key');

        try {
            $url = config("constants.kazoo_full_url") . "/v2/api_auth";
            $attributes = [
                'data' => ['api_key' => self::$kazoo_auth_api_key],
            ];

            \Log::info('Login - Credentials', ['url' => $url, 'attributes' => $attributes]);

            $login = Http::put($url, $attributes);
            self::$auth_token = json_decode($login, true)['auth_token'];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * ACCOUNTS
     * @throws \Exception
     */
    public static function getAccount(string|int $accountId)
    {
        try {
            $url = config("constants.kazoo_full_url") . "/v1/accounts/$accountId";
            $attributes = [];
            $account = json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::GET->value), true);

            if (isset($account) && $account['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($account['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'account' => $account['data'],
                    'account_kazoo' => $account['data']['name'],
                    'account_kazoo_id' => $account['data']['id'],
                    'domain_kazoo' => $account['data']['realm']
                ];
            }
            return [
                'success' => false,
                'message' => $account['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function createAccount(string|int $building_number)
    {
        try {
            $building = Building::query()->where('building_number', $building_number)->first();
            $accountKazoo = "$building->building_number - $building->name";
            $domainKazoo = $building->building_number . self::$kazoo_foxsys_domain_suffix;

            $url = config("constants.kazoo_full_url") . "/v1/accounts";

            $attributes = [
                "data" => [
                    "ui_restrictions" => [
                        "myaccount" => [
                            "user" => [
                                "show_tab" => true
                            ],
                            "twoway" => [
                                "show_tab" => true
                            ],
                            "transactions" => [
                                "show_tab" => true
                            ],
                            "service_plan" => [
                                "show_tab" => true
                            ],
                            "outbound" => [
                                "show_tab" => true
                            ],
                            "inbound" => [
                                "show_tab" => true
                            ],
                            "errorTracker" => [
                                "show_tab" => true
                            ],
                            "billing" => [
                                "show_tab" => true
                            ],
                            "balance" => [
                                "show_tab" => true,
                                "show_minutes" => true,
                                "show_credit" => true
                            ],
                            "account" => [
                                "show_tab" => true
                            ]
                        ]
                    ],
                    "ui_metadata" => [
                        "version" => "4.3-140",
                        "ui" => "monster-ui",
                        "origin" => "myaccount"
                    ],
                    "ui_flags" => [
                        "numbers_format" => "international"
                    ],
                    "timezone" => "America/Montevideo",
                    "reseller_id" => "12bf08ff394fbeaf02823a3cb502b9e7",
                    "realm" => $domainKazoo,
                    "notifications" => [
                        "low_balance" => [
                            "sent_low_balance" => true,
                            "last_notification" => ***********
                        ],
                        "first_occurrence" => [
                            "sent_initial_registration" => true,
                            "sent_initial_call" => false
                        ]
                    ],
                    "name" => $accountKazoo,
                    "language" => "es-ES",
                    "is_reseller" => false,
                    "created" => ***********,
                    "contact" => [
                        "billing" => [
                            "street_address" => " Eduardo Acevedo 868",
                            "region" => "Montevideo",
                            "postal_code" => "11300",
                            "locality" => "Montevideo",
                            "country" => "UY"
                        ]
                    ],
                    "call_restriction" => [
                        "unknown" => [
                            "action" => "inherit"
                        ],
                        "tollfree_us" => [
                            "action" => "inherit"
                        ],
                        "toll_us" => [
                            "action" => "inherit"
                        ],
                        "international" => [
                            "action" => "inherit"
                        ],
                        "emergency" => [
                            "action" => "inherit"
                        ],
                        "did_us" => [
                            "action" => "inherit"
                        ],
                        "caribbean" => [
                            "action" => "inherit"
                        ]
                    ],
                    "descendants_count" => 0,
                    "enabled" => true,
                    "id" => md5($building->building_number),
                    "wnm_allow_additions" => false,
                    "superduper_admin" => false,
                    "billing_mode" => "limits_only"
                ]
            ];

            \Log::info('KazooService::createAccount', ['url' => $url, 'attributes' => $attributes]);
            $account = self::executeHttpRequest($url, $attributes, MethodsEnum::PUT->value);
            $createdAccount = json_decode($account, true);
            \Log::info('KazooService::createAccount', ['createdAccount' => $createdAccount]);
            if (isset($createdAccount) && $createdAccount['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($createdAccount['data'] == []) {
                    return [];
                }

                return [
                    'success' => true,
                    'account' => $createdAccount['data'],
                    'account_kazoo' => $accountKazoo,
                    'account_kazoo_id' => $createdAccount['data']['id'],
                    'domain_kazoo' => $domainKazoo
                ];
            }

            return [
                'success' => false,
                'message' => $createdAccount['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function updateAccount(string $accountId)
    {
        try {
            $building = Building::query()->where('account_kazoo_id', $accountId)->first();

            $accountKazoo = "$building->building_number - $building->name";
            $domainKazoo = $building->building_number . self::$kazoo_foxsys_domain_suffix;

            $url = config("constants.kazoo_full_url") . "/v1/accounts/{$accountId}";

            $attributes = [
                "data" => [
                    "notifications" => [
                        "low_balance" => [
                            "sent_low_balance" => false
                        ],
                        "first_occurrence" => [
                            "sent_initial_registration" => true
                        ]
                    ],
                    "timezone" => "America/Montevideo",
                    "realm" => $domainKazoo,
                    "raw_azure_queue_cdr" => "cdrssalesforcenew4",
                    "name" => $accountKazoo,
                    "max_connect_failures" => 100,
                    "dial_plan" => [
                        "^(\d{8})$" => [
                            "suffix" => "",
                            "prefix" => "+506",
                            "description" => "National"
                        ]
                    ],
                    "azure_queue_stats" => "statsprod",
                    "id" => "2f6079677a725e244e0c3016d7ea9e19",
                    "wnm_allow_additions" => false,
                    "superduper_admin" => false,
                    "created" => ***********,
                    "enabled" => true,
                    "reseller_id" => "27479965191a5d235db853296037c655",
                    "is_reseller" => false,
                    "billing_mode" => "limits_only"
                ]
            ];

            $account = self::executeHttpRequest($url, $attributes, MethodsEnum::PATCH->value);
            $updatedAccount = json_decode($account, true);

            if (isset($updatedAccount) && $updatedAccount['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($updatedAccount['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'account' => $updatedAccount['data'],
                    'account_kazoo' => $accountKazoo,
                    'account_kazoo_id' => $updatedAccount['data']['id'],
                    'domain_kazoo' => $domainKazoo
                ];
            }
            return [
                'success' => false,
                'message' => $updatedAccount['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * DEVICES
     */
    public static function generateDeviceUserData(string|int $contactId, string|int $flatId)
    {
        $buildingContact = BuildingContact::withTrashed()->where('contact_id', $contactId)->where('flat_id', $flatId)->first();

        if (!$buildingContact) {
            return null;
        }
        $userData = KazooFlatsConsecutive::query()->where('contact_id', $contactId)->where('flat_id', $flatId)->first();
        $deviceFlatConsecutive = str_pad(KazooFlatsConsecutive::getConsecutive($flatId, $contactId), 2, "0", STR_PAD_LEFT);
        $flat = Flat::query()->find($flatId);
        $flatNumber = str_pad(trim(strtolower($flat->number)) != "edificio" ? $flat->number : "0", 4, "0",
            STR_PAD_LEFT);
        if (!is_numeric($flatNumber)) {
            return null;
        }
        $tower = $flat->tower_id ? explode("-", $flat->number_with_tower)[0] : null;
        $towerNumber = 0;
        if ($tower) {
            $towerNumber = self::getTowerNumber($tower);
        }
        $username = isset($userData) ? $userData->user_sip_kazoo : Str::random(10) . $deviceFlatConsecutive;
        \Log::info('generateDeviceUserData', ['username' => $username, 'userData' => $userData]);
        \Log::info('generateDeviceUserData->id', ['id' => '$buildingContact->contact_id$buildingContact->flat_id$buildingContact->building_id']);
        return [
            'username' => $username,
            'name_kazoo' => "$flat->number_with_tower - $buildingContact->complete_name",
            'account_kazoo' => Building::query()->where('id', $buildingContact->building_id)->value('account_kazoo_id'),
            'id' => "$buildingContact->contact_id$buildingContact->flat_id$buildingContact->building_id",
            'consecutive' => $deviceFlatConsecutive,
            'kazoo_number' => "0$towerNumber$flatNumber$deviceFlatConsecutive"
        ];
    }

    public static function getTowerNumber(string $towerDenomination): int
    {
        return strlen(trim($towerDenomination)) <= 1 ? 0 : self::$towersLetterNumbers[strtoupper($towerDenomination[1])] ?? 0;
    }

    /**
     * @throws \Exception
     */
    public static function createOrUpdateDevice(string|int $accountId, string|int $flatId)
    {
        try {
            $userData = self::generateDeviceUserData($accountId, $flatId);
            \Log::info('KazooService::createOrUpdateDevice->$userData', ['userData' => $userData]);
            if ($userData == null) {
                return [
                    'success' => false,
                    'message' => 'Datos inválidos para Kazoo',
                ];
            }
            $userId = $userData['id'];

            $kazooFlatsData = KazooFlatsConsecutive::query()
                ->where('contact_id', $accountId)
                ->where('flat_id', $flatId);

            $name = $userData['name_kazoo'];
            $account_kazoo = $userData['account_kazoo'];
            $kazoo_number = $kazooFlatsData->value('kazoo_number') ?? $userData['kazoo_number'];
            $consecutive = $userData['consecutive'];

            $password = $kazooFlatsData->value('password_kazoo') ?? Str::random(12);
            $username = $kazooFlatsData->value('user_sip_kazoo') ?? $userData['username'];


            if (!is_numeric($kazoo_number)) {
                return [
                    'success' => false,
                    'message' => 'El número kazoo no es válido',
                ];
            }

            $attributes = [
                "data" => [
                    "sip" => [
                        "method" => "password",
                        "invite_format" => "contact",
                        "username" => $username,
                        "password" => $password,
                        "expire_seconds" => 300
                    ],
                    "enabled" => true,
                    "caller_id" => [
                        "internal" => [
                            "name" => Str::substr($name, 0, 35)
                        ]
                    ],
                    "ignore_completed_elsewhere" => false,
                    "call_restriction" => [
                        "closed_groups" => [
                            "action" => "inherit"
                        ],
                        "tollfree_us" => [
                            "action" => "inherit"
                        ],
                        "toll_us" => [
                            "action" => "inherit"
                        ],
                        "emergency" => [
                            "action" => "inherit"
                        ],
                        "caribbean" => [
                            "action" => "inherit"
                        ],
                        "did_us" => [
                            "action" => "inherit"
                        ],
                        "international" => [
                            "action" => "inherit"
                        ],
                        "unknown" => [
                            "action" => "inherit"
                        ]
                    ],
                    "media" => [
                        "audio" => [
                            "codecs" => [
                                "PCMU",
                                "PCMA"
                            ]
                        ],
                        "video" => [
                            "codecs" => []
                        ],
                        "fax" => [
                            "option" => "false"
                        ],
                        "fax_option" => false,
                        "encryption" => [
                            "enforce_security" => false,
                            "methods" => [
                            ]
                        ],
                        "webrtc" => true
                    ],
                    "contact_list" => [
                        "exclude" => false
                    ],
                    "device_type" => "softphone",
                    "suppress_unregister_notifications" => true,
                    "name" => Str::substr($name, 0, 35),
                    "ui_metadata" => [
                        "version" => "4.3-140",
                        "ui" => "monster-ui",
                        "origin" => "callflows"
                    ],
                    "exclude_from_queues" => false,
                    "mwi_unsolicited_updates" => true,
                    "register_overwrite_notify" => false,
                    "id" => md5($userData['id'])
                ]
            ];

            $url = config("constants.kazoo_full_url") . "/v1/accounts/$account_kazoo/devices";

            $deviceExists = self::getDevice($account_kazoo, md5($userId));
            \Log::info('KazooService::createOrUpdateDevice->getDevice', ['userData' => $deviceExists]);

            if (isset($deviceExists) && $deviceExists['success'] === true) {
                if (isset($deviceExists['device']) && $deviceExists['device'] == []) {
                    return [];
                }
                \Log::info('KazooService::createOrUpdateDevice->getDevice', ['updateDevice' => $account_kazoo, md5($userId), $username, $kazoo_number, $password, $name, "$userId$userId", $consecutive]);

                return self::updateDevice($account_kazoo, md5($userId), $username, $kazoo_number, $password, $name, "$userId$userId", $consecutive);
            }

            $device = self::executeHttpRequest($url, $attributes, MethodsEnum::PUT->value);
            \Log::info('KazooService::createOrUpdateDevice->$device', ['device' => $device]);

            $createdDevice = json_decode($device, true);
            \Log::info('KazooService::createOrUpdateDevice->$createdDevice', ['device' => $createdDevice]);

            if (isset($createdDevice) && $createdDevice['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($createdDevice['data'] == []) {
                    return [];
                }

                $callflow = self::createCallFlow($account_kazoo, md5($userData['id']), "$kazoo_number", $name, "$userId$userId");

                if (count($callflow) > 0 && $callflow['success'] === true) {
                    if ($callflow['callflow'] == []) {
                        return [];
                    }

                    return [
                        'success' => true,
                        'device' => $createdDevice['data'],
                        'name_kazoo' => $name,
                        'device_id' => $createdDevice['data']['id'],
                        'username' => $username,
                        'password' => $password,
                        'callflow_id' => $callflow['callflow_id'],
                        'kazoo_number' => $kazoo_number,
                        'consecutive' => $consecutive,
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $callflow['message']
                    ];
                }

            }
            return [
                'success' => false,
                'message' => $createdDevice['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function getDevice(string $account_kazoo, string $deviceId)
    {
        try {
            $url = config("constants.kazoo_full_url") . "/v1/accounts/$account_kazoo/devices/$deviceId";
            \Log::info('KazooService::getDevice->$url', ['device' => $url]);
            $attributes = [];
            $device = json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::GET->value), true);

            if (isset($device) && $device['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($device['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'device' => $device['data'],
                    'device_id' => $device['data']['id'],
                ];
            }
            return [
                'success' => false,
                'message' => $device['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function updateDevice(int|string $kazooAccount, int|string $deviceId, string $username, int|string $callflowNumber, string $password, string $name, int|string $callflowId, $consecutive)
    {
        try {
            \Log::info('device', ['deviceId' => $deviceId]);
            $url = config("constants.kazoo_full_url") . "/v2/accounts/$kazooAccount/devices/$deviceId";
            $attributes = [
                "data" => [
                    "sip" => [
                        "method" => "password",
                        "invite_format" => "contact",
                        "username" => $username,
                        "password" => $password,
                        "expire_seconds" => 300
                    ],
                    "enabled" => true,
                    "caller_id" => [
                        "internal" => [
                            "name" => Str::substr($name, 0, 35)
                        ]
                    ],
                    "ignore_completed_elsewhere" => false,
                    "call_restriction" => [
                        "closed_groups" => [
                            "action" => "inherit"
                        ],
                        "tollfree_us" => [
                            "action" => "inherit"
                        ],
                        "toll_us" => [
                            "action" => "inherit"
                        ],
                        "emergency" => [
                            "action" => "inherit"
                        ],
                        "caribbean" => [
                            "action" => "inherit"
                        ],
                        "did_us" => [
                            "action" => "inherit"
                        ],
                        "international" => [
                            "action" => "inherit"
                        ],
                        "unknown" => [
                            "action" => "inherit"
                        ]
                    ],
                    "media" => [
                        "audio" => [
                            "codecs" => [
                                "PCMU",
                                "PCMA"
                            ]
                        ],
                        "video" => [
                            "codecs" => []
                        ],
                        "fax" => [
                            "option" => "false"
                        ],
                        "fax_option" => false,
                        "encryption" => [
                            "enforce_security" => false,
                            "methods" => [
                            ]
                        ]
                    ],
                    "contact_list" => [
                        "exclude" => false
                    ],
                    "device_type" => "softphone",
                    "suppress_unregister_notifications" => false,
                    "name" => $name,
                    "ui_metadata" => [
                        "version" => "4.3-140",
                        "ui" => "monster-ui",
                        "origin" => "callflows"
                    ],
                    "exclude_from_queues" => false,
                    "mwi_unsolicited_updates" => true,
                    "register_overwrite_notify" => false,
                    "id" => $deviceId
                ]
            ];


            $device = self::executeHttpRequest($url, $attributes, MethodsEnum::PATCH->value);
            $updatedDevice = json_decode($device, true);

            \Log::info('KazooService::updateDevice', ['device' => $device, 'updatedDevice' => $updatedDevice]);
            if (isset($updatedDevice) && $updatedDevice['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($updatedDevice['data'] == []) {
                    return [];
                }
                $callflowToUpdate = KazooFlatsConsecutive::query()->where('user_sip_kazoo', $username)->value('callflow_id');
                \Log::info('callflowToUpdate', ['callflowToUpdate' => $callflowToUpdate]);
                if ($callflowToUpdate) {
                    $callflow = self::updateCallFlow($kazooAccount, $deviceId, $callflowToUpdate, $name, "$callflowNumber", $name, "$callflowId");
                    if (count($callflow) > 0 && $callflow['success'] === true) {
                        if ($callflow['callflow'] == []) {
                            return [];
                        }
                        return [
                            'success' => true,
                            'device' => $updatedDevice['data'],
                            'name_kazoo' => $name,
                            'device_id' => $updatedDevice['data']['id'],
                            'username' => $username,
                            'password' => $password,
                            'callflow_id' => $callflow['callflow_id'],
                            'kazoo_number' => $callflowNumber,
                            'consecutive' => $consecutive,
                        ];
                    }
                } else {
                    $callflow = self::createCallFlow($kazooAccount, $deviceId, $callflowNumber, $name, "$callflowId");
                    \Log::info('createCallFlow', ['callflow' => $callflow]);
                    if ($callflow['success'] === true) {
                        if ($callflow['callflow'] == []) {
                            return [];
                        }
                        return [
                            'success' => true,
                            'device' => $updatedDevice['data'],
                            'name_kazoo' => $name,
                            'device_id' => $updatedDevice['data']['id'],
                            'username' => $username,
                            'password' => $password,
                            'callflow_id' => $callflow['callflow_id'],
                            'kazoo_number' => $callflowNumber,
                            'consecutive' => $consecutive,
                        ];
                    }
                }
            }
            return [
                'success' => false,
                'message' => $updatedDevice['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function deleteDevice(string|int $account_kazoo, string|int $deviceId)
    {
        try {
            if ($deviceId) {
                $url = config("constants.kazoo_full_url") . "/v2/accounts/$account_kazoo/devices/$deviceId";
                $attributes = [];
                json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::DELETE->value), true);
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * CALLFLOWS
     * @throws \Exception
     */
    public static function createCallFlow(string|int $kazooAccount, string|int $deviceId, string $number, string $callFlowName, string|int $callFlowId)
    {
        try {
            $url = config("constants.kazoo_full_url") . "/v1/accounts/$kazooAccount/callflows";
            $callFlowIdRandom = md5(Str::random(10));
            $attributes = [
                "data" => [
                    "flow" => [
                        "data" => [
                            "id" => $deviceId,
                            "timeout" => 20,
                            "can_call_self" => false,
                            "delay" => 0,
                            "dial_strategy" => "simultaneous"
                        ],
                        "module" => "device",
                    ],
                    "numbers" => ["$number"],
                    "ui_metadata" => [
                        "version" => "4.3-140",
                        "ui" => "monster-ui",
                        "origin" => "callflows"
                    ],
                    "name" => $callFlowName,
                    "contact_list" => [
                        "exclude" => false
                    ],
                    "metadata" => [
                        $deviceId => [
                            "name" => $callFlowName,
                            "pvt_type" => "device"
                        ]
                    ],
                    "id" => $callFlowIdRandom
                ]
            ];

            $callFlow = self::executeHttpRequest($url, $attributes, MethodsEnum::PUT->value);
            $createdCallFlow = json_decode($callFlow, true);
            if (isset($createdCallFlow) && $createdCallFlow['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($createdCallFlow['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'callflow' => $createdCallFlow['data'],
                    'callflow_id' => $createdCallFlow['data']['id'],
                ];
            }
            return [
                'success' => false,
                'message' => $createdCallFlow['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function updateCallFlow(string|int $kazooAccount, string|int $deviceId, string $callFlowToUpdate, string $deviceName, string $number, string $callFlowName, string|int $callFlowId, bool $reuseCallFlow = false)
    {
        try {
            $url = config("constants.kazoo_full_url") . "/v2/accounts/$kazooAccount/callflows/$callFlowToUpdate";
            $attributes = [
                "data" => [
                    "flow" => [
                        "data" => [
                            "id" => $deviceId,
                            "timeout" => 20,
                            "can_call_self" => false,
                            "delay" => 0,
                            "dial_strategy" => "simultaneous"
                        ],
                        "module" => "device",
                    ],
                    "numbers" => [
                        "$number"
                    ],
                    "ui_metadata" => [
                        "version" => "4.3-140",
                        "ui" => "monster-ui",
                        "origin" => "callflows"
                    ],
                    "name" => $callFlowName,
                    "contact_list" => [
                        "exclude" => false
                    ],
                    "metadata" => [
                        "$deviceId" => [
                            "name" => $deviceName,
                            "pvt_type" => "device"
                        ]
                    ],
                    "id" => $callFlowToUpdate
                ]
            ];

            $callflow = json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::PATCH->value), true);
            if (isset($callflow) && $callflow['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($callflow['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'callflow' => $callflow['data'],
                    'callflow_id' => $callflow['data']['id'],
                ];
            }
            return self::createCallFlow($kazooAccount, $deviceId, $number, $callFlowName, $callFlowId);
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function deleteCallFlow(string|int $account_kazoo, string|int $callflowId)
    {
        try {
            if ($callflowId) {
                $url = config("constants.kazoo_full_url") . "/v2/accounts/$account_kazoo/callflows/$callflowId";
                $attributes = [];
                json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::DELETE->value), true);
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function deleteAllCallFlow(Building $building)
    {
        try {
            if ($building->account_kazoo_id) {
                $url = config("constants.kazoo_full_url") . "/v1/accounts/$building->account_kazoo_id/callflows";
                $attributes = [];
                $allCallflows = json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::GET->value), true);
                foreach ($allCallflows['data'] as $callflow) {
                    $callflowExists = self::getCallflow($building->account_kazoo_id, $callflow['id']);
                    if (isset($callflowExists) && $callflowExists['success']) {
                        self::deleteCallFlow($building->account_kazoo_id, $callflow['id']);
                    }
                    KazooFlatsConsecutive::query()
                        ->where('callflow_id', $callflow['id'])
                        ->delete();
                }
            }
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public static function getCallflow(string $account_kazoo, string $callflowId)
    {
        try {
            $url = config("constants.kazoo_full_url") . "/v1/accounts/$account_kazoo/callflows/$callflowId";
            $attributes = [];
            $callflow = json_decode(self::executeHttpRequest($url, $attributes, MethodsEnum::GET->value), true);
            if (isset($callflow) && $callflow['status'] == ResponseErrorStatusEnum::SUCESS->value) {
                if ($callflow['data'] == []) {
                    return [];
                }
                return [
                    'success' => true,
                    'callflow' => $callflow['data'],
                    'callflow_id' => $callflow['data']['id'],
                ];
            }
            return [
                'success' => false,
                'message' => $callflow['message'],
            ];
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
