<?php

namespace App\Observers;

use App\Models\Area;
use App\Notifications\CreateTemporaryNotification;
use App\Models\TemporaryContact;
use App\Notifications\CreateTemporaryNotificationUpdate;

class TemporaryObserver
{
    /**
     * Handle the temporary contact "created" event.
     *
     * @param  \App\TemporaryContact  $temporary_contact
     * @return void
     */
    public function created(TemporaryContact $temporary_contact)
    {
        //Notifications
        $categories = Area::where('name','Atención')->first()?->category;
        $old_reponsables = [];
        if($temporary_contact->type == 'authorized' || $temporary_contact->type == 'resident' ) {
            foreach ($categories as $cat) {
                $responsables = $cat->responsables();
                foreach ($responsables as $responsable) {
                    if (!in_array($responsable->id, $old_reponsables) and $responsable->id != backpack_user()->id) {
                        $responsable->notify(new CreateTemporaryNotification($temporary_contact));
                    }
                    array_push($old_reponsables, $responsable->id);
                }
            }
        }
        //End notification
    }

    public function saving(TemporaryContact $temporaryContact)
    {
        $temporaryContact->complete_name = $temporaryContact->contact_name . ' ' . $temporaryContact->surname;
    }

    public function updating(TemporaryContact $temporaryContact)
    {
        $temporaryContact->complete_name = $temporaryContact->contact_name . ' ' . $temporaryContact->surname;
    }

    /**
     * Handle the temporary contact "updated" event.
     *
     * @param  \App\TemporaryContact  $temporary_contact
     * @return void
     */
    public function updated(TemporaryContact $temporary_contact)
    {
        //Notifications
        $categories = Area::where('name','Atención')->first()?->category;
        $old_reponsables = [];
        if(($temporary_contact->type == 'authorized' && $temporary_contact->type != $temporary_contact->getOriginal('type')) || ($temporary_contact->type == 'resident' && $temporary_contact->type != $temporary_contact->getOriginal('type')) ) {
            foreach ($categories as $cat) {
                $responsables = $cat->responsables();
                foreach ($responsables as $responsable) {
                    if (!in_array($responsable->id, $old_reponsables) and $responsable->id != backpack_user()->id) {
                        $responsable->notify(new CreateTemporaryNotificationUpdate($temporary_contact));
                    }
                    array_push($old_reponsables, $responsable->id);
                }
            }
        }
        //End notification
    }

    /**
     * Handle the temporary contact "deleted" event.
     *
     * @param  \App\TemporaryContact  $temporaryContact
     * @return void
     */
    public function deleted(TemporaryContact $temporaryContact)
    {
        //
    }

    /**
     * Handle the temporary contact "restored" event.
     *
     * @param  \App\TemporaryContact  $temporaryContact
     * @return void
     */
    public function restored(TemporaryContact $temporaryContact)
    {
        //
    }

    /**
     * Handle the temporary contact "force deleted" event.
     *
     * @param  \App\TemporaryContact  $temporaryContact
     * @return void
     */
    public function forceDeleted(TemporaryContact $temporaryContact)
    {
        //
    }
}
