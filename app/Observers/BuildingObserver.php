<?php

namespace App\Observers;

use App\Models\AccessCode;
use App\Models\Building;

class BuildingObserver
{
    public static function removeAccessCodesPermanentTemporary($building_id, bool $permanent = true)
    {
        (new AccessCode())->deleteAccessCodesPermanentOrTemporary($building_id, $permanent);
        AccessCode::query()
            ->where('building_id', $building_id)
            ->where('expiration', $permanent)
            ->delete();
    }

    public static function removeAccessCodes($building_id)
    {
        AccessCode::deleteAccessCodesAfterDeleteBuilding($building_id);
        AccessCode::query()
            ->where('building_id', $building_id)
            ->delete();
    }

    public function creating(Building $building): void
    {
        // TODO: Cuando se separen los pines verificar estado de cada uno y eliminar el otro

//        $building->access_by_pin = $building->access_by_pin_permanent || $building->access_by_pin_temporary;
    }

    public function updating(Building $building): void
    {
        // TODO: Cuando se separen los pines verificar estado de cada uno y eliminar el otro

        //        $building->access_by_pin = $building->access_by_pin_permanent || $building->access_by_pin_temporary;
    }

    public function deleting(Building $building): void
    {
    }

    public function saving(Building $building): void
    {
        // TODO: Cuando se separen los pines verificar estado de cada uno y eliminar el otro

//        $building->access_by_pin = $building->access_by_pin_permanent || $building->access_by_pin_temporary;

    }
}
