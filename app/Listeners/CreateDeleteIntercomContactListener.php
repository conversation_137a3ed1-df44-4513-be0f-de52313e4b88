<?php

namespace App\Listeners;

use App\Events\ContactUpdatedEvent;
use App\Models\BuildingsRelationships\BuildingContact;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateDeleteIntercomContactListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 3;
    public $timeout = 120;

    public function handle(ContactUpdatedEvent $event)
    {
        $contact = $event->contact;

        $buildingContact = BuildingContact::query()
            ->where('contact_id', '=', $contact->id)
            ->where('main_building', '=', 1)
            ->first();

        $buildingIncomingCall = $buildingContact?->building?->incoming_call;

        if ($buildingIncomingCall) {
            BuildingContact::createDeleteIntercomContact($buildingContact);
        }
    }
}

