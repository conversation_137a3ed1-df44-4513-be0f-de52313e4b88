<?php

namespace App\Traits;

trait CustomRevisionsTrait
{

    public function listRevisions($id)
    {
        $this->crud->hasAccessOrFail('revise');
        $this->crud->setOperation('revise');

        $entry = $this->crud->getEntry($id);
        $perPage = config('backpack.operations.revise.per_page', 20);
        $paginatedData = $this->crud->getPaginatedRevisionsForEntry($id, $perPage);

        $data = [
            'crud' => $this->crud,
            'entry' => $entry,
            'revisions' => $paginatedData['revisions'],
            'paginator' => $paginatedData['paginator'],
            'title' => trans('revise-operation::revise.revisions'),
        ];

        if (request()->ajax()) {
            return view('revise-operation::revision_timeline_paginated', $data)->render();
        }

        return view('revise-operation::revisions_paginated', $data);
    }
}
