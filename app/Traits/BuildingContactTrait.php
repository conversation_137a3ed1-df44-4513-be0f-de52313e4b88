<?php

namespace App\Traits;

use App\Imports\ContactsImport;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Caseq;

trait BuildingContactTrait
{

    public static function updatePersonalDataFromContact(BuildingContact $buildingContact)
    {
        $buildingContact->complete_name = $buildingContact->contact?->name . ' ' . $buildingContact->contact?->surname;
        $buildingContact->building_name = $buildingContact->building?->name ?: "";
        $buildingContact->email = $buildingContact->contact?->email;
        $buildingContact->ci = $buildingContact->contact?->ci;
        $buildingContact->image = $buildingContact->contact?->image;
        $buildingContact->security_word = $buildingContact->contact?->security_word;
        $buildingContact->phone_mobile = str_replace(' ', '', ContactsImport::getMobilePhone($buildingContact->phone_mobile));
        $buildingContact->flat_number = $buildingContact->flat?->number;
        $buildingContact->tower_denomination = $buildingContact->flat->tower?->tower_denomination;
    }

    public static function updateCasesAnnouncements(BuildingContact $buildingContact)
    {
        $announcements = [];
        Caseq::query()
            ->where('last_category_id', config('constants.category_announcement'))
            ->where('building_id', $buildingContact->building_id)
            ->whereNotIn('state', ['finalizado', 'cerrado', 'CERRADO', 'FINALIZADO'])
            ->each(function (Caseq $caseq) use (&$announcements, $buildingContact) {
                if($caseq->user_id == $buildingContact->contact_id) {
                    $announcements[] = $caseq->description;
                } else if($caseq->flat_id == $buildingContact->flat_id) {
                    $announcements[] = $caseq->description;
                } else {
                    $announcements[] = $caseq->description;
                }
            });

        $buildingContact->announcements = json_encode($announcements);
    }

    public static function bootBuildingContactTrait()
    {
        static::creating(function (BuildingContact $buildingContact) {
            self::updatePersonalDataFromContact($buildingContact);
            self::updateCasesAnnouncements($buildingContact);
        });

        static::saving(function (BuildingContact $buildingContact) {
            if ($buildingContact->contact) {
                self::updatePersonalDataFromContact($buildingContact);
                self::updateCasesAnnouncements($buildingContact);
            }
        });

        static::updating(function (BuildingContact $buildingContact) {
            if ($buildingContact->contact) {
                self::updatePersonalDataFromContact($buildingContact);
                self::updateCasesAnnouncements($buildingContact);
            }
        });
    }
}
