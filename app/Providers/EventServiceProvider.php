<?php

namespace App\Providers;

use App\Events\ContactUpdatedEvent;
use App\Events\UserLogged;
use App\Events\WittyBotMessageSentEvent;
use App\Listeners\CreateDeleteIntercomContactListener;
use App\Listeners\RegisterInAsterisk;
use App\Listeners\StoreUserDeviceInfo;
use App\Listeners\StoreUserPushInfo;
use App\Listeners\WittyBotMessageSent;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\Caseq;
use App\Models\Flat;
use App\Models\TemporaryContact;
use App\Observers\BuildingObserver;
use App\Observers\CaseObserver;
use App\Observers\FlatObserver;
use App\Observers\TemporaryObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        UserLogged::class => [
            StoreUserPushInfo::class,
//            RegisterInAsterisk::class,
            StoreUserDeviceInfo::class,
        ],
        ContactUpdatedEvent::class => [
            CreateDeleteIntercomContactListener::class,
        ],

    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }

    protected $observers = [
        Caseq::class => [CaseObserver::class],
        TemporaryContact::class => [TemporaryObserver::class],
    ];

    public function shouldDiscoverEvents()
    {
        return true;
    }
}
