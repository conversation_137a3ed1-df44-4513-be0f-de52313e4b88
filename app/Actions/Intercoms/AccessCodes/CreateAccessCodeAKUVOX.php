<?php

namespace App\Actions\Intercoms\AccessCodes;

use App\Models\AccessCode;
use App\Models\User\Contact;

class CreateAccessCodeAKUVOX
{
    public static function handle($userId, $accessCode, $building_intercoms, $expiration = null, $cardCode = null)
    {
        $contactName = Contact::query()->find($userId)?->complete_name;

        $ips = GetIntercomsIps::handle($building_intercoms);

        $results = array();

        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');
        $curlUrl = "$serverIP:$serverPORT/api/user/create/akuvox/";

        foreach ($ips as $ip) {
            $url = $curlUrl . $ip;

            $header = array("Content-Type: application/json");
            $settings = [];
            $data = null;
            $user = [];
            $user['Name'] = $contactName;
            $user['PrivatePIN'] = (string)$accessCode;
            $user['ScheduleRelay'] = '1001-1';
            $data['item'] = [$user];
            $settings['data'] = $data;
            $settings['target'] = 'user';
            $settings['action'] = 'add';


            try {

                $curl = curl_init();

                curl_setopt_array(
                    $curl,
                    array(
                        CURLOPT_URL => $url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => json_encode($settings),

                        CURLOPT_HTTPHEADER => $header,
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => false,

                    )
                );

                $response = curl_exec($curl);

                if (!$response) {
                    AccessCode::$accessCodeBoolean = false;
                    die('Curl failed: ' . curl_error($curl));
                }

                curl_close($curl);

                if (!json_decode($response, true)) {
                   $results[] = ['status' => '501', 'message' => 'No se puede encontrar el intercomunicador ' . $ip . '.'];
                }
                if (array_key_exists('error', json_decode($response, true))) {
                    AccessCode::$accessCodeBoolean = false;
                    $results[] = ['status' => '501', 'message' => json_decode($response, true)['error']['description']];
                } else {
                    $results[] = ['status' => '201', 'message' => json_decode($response, true)];
                }
            } catch (\Http\Client\Exception $exception) {
                AccessCode::$accessCodeBoolean = false;
                $results[] = ['status' => '501', 'message' => $exception->getMessage()];
            }
        }
        return json_encode($results);
    }
}
