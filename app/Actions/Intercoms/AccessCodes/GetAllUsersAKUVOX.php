<?php

namespace App\Actions\Intercoms\AccessCodes;

use App\Models\AccessCode;

class GetAllUsersAKUVOX
{
    public static function handle($building_intercoms)
    {
        $ips = GetIntercomsIps::handle($building_intercoms);

        $results = [];

        $serverIP = config('intercoms.SERVER_IP');
        $serverPORT = config('intercoms.SERVER_PORT');

        foreach ($ips as $ip) {

            $url = "$serverIP:$serverPORT" . '/api/user/get/akuvox/' . $ip;

            try {
                $curl = curl_init();

                curl_setopt_array(
                    $curl,
                    array(
                        CURLOPT_URL => $url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',

                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => false,

                    )
                );
                $response = curl_exec($curl);

                if ($response === FALSE) {
                    AccessCode::$accessCodeBoolean = false;
                    die('Curl failed: ' . curl_error($curl));
                }

                curl_close($curl);

                if (array_key_exists('error', json_decode($response, true))) {
                    AccessCode::$accessCodeBoolean = false;
                    $results[] = ['status' => '501', 'message' => json_decode($response, true)['error']['description']];
                } else if (json_decode($response, true)['status'] === '501') {
                    $results[] = ['status' => '501', 'message' => json_decode($response, true)['message']];
                } else {
                    $results[] = ['status' => '200', 'message' => json_decode($response, true)['message']['data']['item']];
                }
            } catch (\Http\Client\Exception $exception) {
                AccessCode::$accessCodeBoolean = false;
                $results[] = ['status' => '501', 'message' => $exception->getMessage()];
            }
        }

        return json_encode($results);
    }
}
