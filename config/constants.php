<?php

return [
    'category_announcement' => 83,

    'foxsys_user' => 42375,

    'user_id_crm_daemons' => 35304,

    'package_cases' => [101, 102, 103, 44],

    'cases_close_states' => ['finalizado', 'CERRADO', 'FINALIZADO', 'cerrado'],

    'package_state_pending' => 'pendiente',

    'logistics_responsible_id' => 19,

    'asterisk_url' => "http://asteriks3.techlab.com.uy",

    'wittybots_url' => "https://api.wittysuite.com",

    'wittybots_client_url' => "https://client2.wittybots.uy",

    'wittybots_username' => 'dea02816',

    'wittybots_password' => 'd78d96cb',

    'bots_extension_3cx' => ['99901'],

    'bot_resolution' => [
        App\Models\TransferredCalls::REFER => 'Transferencia',
        App\Models\TransferredCalls::ENTER => 'AV Abre Puerta',
    ],

    'package_cases_delivery' => 102,

    'package_cases_reception' => 101,

    'package_cases_receipt_delivery' => 103,

    'foxsys_3cx_extension_marigot' => 16605,

    'foxsys_3cx_extension' => 16603,

    'foxsys_building_id' => 120,

    'foxsys_extension_3cx' => ['00005', '16603', '99901', '99902', '16605'],

    'thor_ws' => [
        "url" => 'thor-api.flow-labs.io',
        "port" => 443,
        "protocol" => 'wss',
    ],

    'akuvox' => 'AKUVOX',

    'akuvox_r29' => 'R29',

    'akuvox_r20k' => 'R20K',

    'akuvox_e18' => 'E18',

    '2n' => '2N',

    'adam' => 'ADAM',

    'cases_priority' => [
        'defecto' => 'DEFECTO',
        'baja' => 'BAJA'
    ],

    'emails_to_send_cases_to_atc' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],


    'kazoo_full_url' => str_contains(env('APP_URL', 'https://thorcrm.techlab.com.uy'), 'https://thorcrm.techlab.com.uy')
        ? 'http://uy-kaz01.foxsys.cloud' . ':' . env('kazoo_url_port', 8000)
        : env('KAZOO_URL', 'http://foxsys.site') . ":" . env('kazoo_url_port', 8000),

    'kazoo_api_key' => str_contains(env('APP_URL', 'https://thorcrm.techlab.com.uy'), 'https://thorcrm.techlab.com.uy') ? "bdcfbb9e242806517f280e25be6b7722a74284d967843d8c6840dba65967dd11" : "6731b92ae3abd33fab928bf49f554a2cab9a1091485e26a1c801ffe5d6af3ae4",

    'kazoo_not_create_devices_building_residential' => [
        'Autorizado' => 'Autorizado',
        'Prohibido Ingreso' => 'Prohibido Ingreso',
        'No Residente' => 'No Residente',
    ],

    'kazoo_not_create_devices_building_office' => [
        'Autorizado' => 'Autorizado',
        'Empleado' => 'Empleado',
        'Prohibido Ingreso' => 'Prohibido Ingreso',
        'Oficina' => 'Oficina',
        'Proveedor' => 'Proveedor',
    ],

    'foxsys_aws' => 'https://foxsys-crm-backup.s3.amazonaws.com/',

    'image_placeholder' => '/images/user_placeholder.png',
];
